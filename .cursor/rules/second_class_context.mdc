---
alwaysApply: true
---
# Second Class Context: Zurno Backend Admin Dashboard

## Project Overview

### Main User Goals
- **Administrative Management**: Comprehensive admin panel for managing e-commerce operations, affiliate programs, marketing campaigns, and user management
- **Business Operations**: Handle orders, inventory, vendor relationships, and financial transactions
- **Data Analytics**: Monitor business performance through multiple dashboard views (analytics, e-commerce, classified marketplace)
- **Content Management**: Manage products, collections, categories, events, and marketing materials
- **Role-Based Access Control**: Secure multi-admin system with granular permissions

### Key User Journeys
1. **Dashboard Navigation**: Landing → Authentication → Role-based Dashboard Selection → Module Access
2. **Product Management**: Products List → Product Details/Edit → Category Management → Collection Organization
3. **Order Processing**: Orders List → Order Details → Status Updates → Commission Tracking
4. **Affiliate Management**: Affiliate Registration → Tier Management → Commission Setup → Payout Processing
5. **Marketing Operations**: Campaign Creation → Event Management → Coupon Setup → Analytics Review

### High-Level UI Architecture
```
App Root (Provider + BrowserRouter)
├── Authentication Layout (Auth + Login)
├── Main Application Layout
│   ├── Header (Navigation + User Controls)
│   ├── Sidebar (Module Navigation)
│   ├── Page Header (Breadcrumbs + Actions)
│   ├── Main Content Area (Routed Components)
│   └── Footer
├── Switcher (Theme/Layout Controls)
└── Modals/Overlays (Global UI Components)
```

## Technology Stack

### Main Frontend Framework
- **React 18**: Primary framework with modern hooks and concurrent features
- **TypeScript**: Full type safety with strict configuration
- **JSX Mode**: Preserve mode for optimal build performance

### Component Libraries
- **Material-UI (@mui/material, @mui/icons-material)**: Primary component library for forms, data display, and navigation
- **React Bootstrap**: Secondary component system for layout and utilities
- **Custom SCSS**: Extensive custom styling with Bootstrap override system

### Build Tools
- **Vite**: Modern build tool with ES2015 target
- **ESBuild**: Fast minification and TypeScript compilation
- **Rollup**: Optimized bundling with tree-shaking
- **Sass**: SCSS preprocessing for modular styling
- **PostCSS**: Style processing pipeline

### Browser Support
- **Target**: ES2020+ browsers
- **Build Target**: ES2015 for maximum compatibility
- **Module System**: ESNext with bundler resolution

### Additional Libraries
- **Charts**: ApexCharts, Chart.js, ECharts for data visualization
- **Maps**: React Google Maps, Leaflet for location services
- **Media**: Amazon IVS for live streaming, React Player for video
- **File Handling**: FilePond, React Dropzone for uploads
- **Forms**: Formik + Yup for validation
- **Utilities**: Lodash, Moment.js, date-fns

## Routing & Navigation

### URL Schema
```
Base Structure:
/{BASE_URL}/
├── dashboards-{type} (ecommerce, analytics, marketplace)
├── managements-{resource} (products, users, orders, etc.)
├── {resource}-details/{id}/{action?} (edit, view modes)
├── managements-{module}/* (nested routing for complex modules)
└── settings, notifications, chatbot (utility routes)

Examples:
/dashboards-ecommerce
/managements-products
/managements-users/details/123/edit
/managements-affiliate-tiers/456/commission-products
```

### Auth-Guarded Routes
- **RequireAuth HOC**: Validates access token and user session
- **AuthorizedRoute Component**: Resource-based permission checking
- **Permission System**: Fine-grained CRUD permissions per resource
- **Role-Based Access**: Super admin vs. regular admin capabilities

### Deep Linking
- **Stateful URLs**: Full application state preserved in URLs
- **Resource Navigation**: Direct links to specific entities with actions
- **Nested Module Support**: Complex multi-level routing for sub-modules
- **Base URL Configuration**: Environment-configurable base paths

## State Management

### Approach/Library Used
- **Redux Toolkit (RTK)**: Primary state management with modern Redux patterns
- **RTK Query**: Integrated data fetching and caching layer
- **React-Redux**: React bindings with hooks-based API

### Data Flow
```
Component → useSelector/useDispatch → RTK Store
                ↓
Component → RTK Query Hooks → API Service → Backend
                ↓
Axios Base Query → HTTP Requests → Server Response
                ↓
RTK Query Cache → Component Re-render
```

### Persistence and Caching
- **Local Storage**: Authentication tokens, user preferences, role information
- **RTK Query Cache**: Automatic request deduplication and background refetching
- **API Service**: Centralized Axios configuration with token management
- **Session Management**: Automatic logout on token expiration

### Key Slices
- **Auth Slice**: User authentication state and token management
- **Authorization Slice**: Permission and role-based access control
- **UI State**: Theme, sidebar, modal states
- **Entity Management**: Cached data for products, users, orders, etc.

## UI/UX Patterns

### Component Structure
```
Page Components (Container Level)
├── Management Tables (List Views)
├── Detail Forms (CRUD Operations)
├── Dashboard Widgets (Analytics Views)
└── Modal Dialogs (Actions & Confirmations)

Shared Components
├── Common Layout (Header, Sidebar, Footer)
├── Form Elements (Inputs, Selects, File Uploads)
├── Data Display (Tables, Cards, Charts)
├── Navigation (Breadcrumbs, Pagination, Search)
└── Feedback (Toasts, Loading, Error States)
```

### Design Principles
- **Consistent Spacing**: 8px grid system with Bootstrap spacing utilities
- **Typography Hierarchy**: Material-UI typography with custom brand fonts
- **Color System**: Brand colors with semantic variants (success, warning, error)
- **Responsive Design**: Mobile-first approach with breakpoint considerations
- **Accessibility**: ARIA attributes, keyboard navigation, semantic HTML

### Accessibility Approach
- **Semantic HTML**: Proper heading hierarchy and landmark elements
- **Keyboard Navigation**: Tab order and focus management
- **Screen Reader Support**: ARIA labels and descriptions
- **Color Contrast**: WCAG 2.1 AA compliance for text and interactive elements
- **Form Accessibility**: Proper labeling and error announcements

## API Integration

### How Data is Fetched/Mutated
- **RTK Query Hooks**: Declarative data fetching with useQuery/useMutation patterns
- **Axios Base Query**: Custom base query implementation for RTK Query
- **Request Interceptors**: Automatic token attachment and error handling
- **Response Transformations**: Data normalization and type safety

### Error Handling
```typescript
// Global Error Handling Pattern
try {
  const response = await apiCall();
  return response.data;
} catch (error) {
  if (error.status === 401) {
    // Redirect to login
    localStorage.clear();
    navigate('/');
  }
  // Display user-friendly error message
  toast.error(error.message || 'Operation failed');
  throw error;
}
```

### API Client Configuration
- **Base URL**: Environment-configurable API endpoints
- **Authentication**: Bearer token with automatic refresh
- **Request Timeout**: Configurable timeout with retry logic
- **Content Types**: JSON primary, multipart for file uploads
- **CORS Handling**: Proper cross-origin configuration

## Security & Privacy

### User Data Handling
- **Token Storage**: Secure local storage with automatic cleanup
- **Data Encryption**: HTTPS-only communication
- **PII Protection**: Minimal data retention and secure transmission
- **Session Management**: Automatic timeout and refresh mechanisms

### XSS/CSRF Protection
- **Content Security Policy**: Strict CSP headers implementation
- **Input Sanitization**: HTML and script injection prevention
- **CSRF Tokens**: Server-side token validation
- **Secure Headers**: X-Frame-Options, X-Content-Type-Options

### Permissions Management
- **Resource-Based Permissions**: Granular CRUD permissions per resource type
- **Role Hierarchy**: Super admin and regular admin role separation
- **Route Protection**: Component-level permission checking
- **Action Authorization**: Method-level permission validation

## Testing Strategy

### Unit Testing
- **Framework**: Jest + React Testing Library (to be implemented)
- **Coverage Target**: 80%+ for utility functions and business logic
- **Mock Strategy**: API mocking with MSW (Mock Service Worker)
- **Component Testing**: Isolated component behavior verification

### Integration Testing
- **API Integration**: End-to-end API flow testing
- **Redux Integration**: Store and reducer integration tests
- **Route Testing**: Navigation and authentication flow tests
- **Form Integration**: Complete form submission workflows

### UI/E2E Testing
- **Framework**: Playwright or Cypress (recommended)
- **Critical Paths**: Authentication, CRUD operations, dashboard navigation
- **Cross-Browser**: Chrome, Firefox, Safari compatibility
- **Mobile Testing**: Responsive design validation

### Visual Regression Testing
- **Tool**: Chromatic or Percy integration
- **Component Stories**: Storybook documentation and testing
- **Design System**: Component library visual consistency
- **Responsive Testing**: Multi-breakpoint visual validation

## Build & Deployment

### Environment Configuration
```
Development: Local development with hot reload
Staging: Production-like environment for testing
Production: Optimized build with performance monitoring

Environment Variables:
- VITE_APP_API_URL: Backend API endpoint
- BASE_URL: Application base path
- NODE_ENV: Build environment flag
```

### Asset Pipeline
- **SCSS Compilation**: Sass → CSS with source maps
- **TypeScript Compilation**: TS → JS with type checking
- **Asset Optimization**: Image compression, font subsetting
- **Code Splitting**: Route-based and component-based chunking

### CDN Usage
- **Static Assets**: Images, fonts, and icons via CDN
- **Library CDNs**: Chart.js, external dependencies
- **Cache Strategy**: Long-term caching with versioned assets
- **Performance**: Global content delivery optimization

### Deployment Steps
1. **Build Process**: `npm run build` with production optimization
2. **Type Checking**: `npm run typecheck` for TypeScript validation
3. **Linting**: ESLint with automatic fixes
4. **Asset Generation**: SCSS compilation and minification
5. **Bundle Analysis**: Size monitoring and optimization
6. **Deployment**: Static file hosting with CDN integration

## Performance Optimization

### Lazy Loading
```typescript
// Route-level code splitting
const ProductManagement = lazy(() => import('./container/managements/product/product_list'));

// Component-level lazy loading
const ChartComponent = lazy(() => import('./components/charts/ApexChart'));
```

### Code Splitting
- **Route-Based**: Each major route as separate bundle
- **Component-Based**: Heavy components (charts, forms) split separately
- **Vendor Splitting**: Third-party libraries in separate chunks
- **Dynamic Imports**: On-demand feature loading

### Critical CSS
- **Above-the-fold**: Inline critical styles for initial render
- **Progressive Loading**: Non-critical styles loaded asynchronously
- **CSS Purging**: Unused style removal in production builds
- **Font Loading**: Optimized web font loading strategy

### Bundle Analysis
- **Bundle Size Monitoring**: Webpack Bundle Analyzer integration
- **Performance Budgets**: Size limits for chunks and assets
- **Tree Shaking**: Dead code elimination
- **Compression**: Gzip/Brotli compression for static assets

## Documentation & Developer Experience

### Style Guide
- **Component Patterns**: Consistent component architecture
- **Naming Conventions**: TypeScript interfaces, component props
- **File Organization**: Module-based directory structure
- **Code Formatting**: Prettier + ESLint configuration

### Storybook or Other UI Documentation Tools
- **Recommended**: Storybook implementation for component documentation
- **Component Library**: Isolated component development and testing
- **Design System**: Visual component catalog with usage examples
- **Interactive Documentation**: Live component playground

### Onboarding Process
1. **Environment Setup**: Node.js, package manager, IDE configuration
2. **Project Structure**: Understanding of modular architecture
3. **Development Workflow**: Local development and hot reload
4. **Code Standards**: Linting, formatting, and TypeScript guidelines
5. **Testing Approach**: Unit and integration testing patterns

### Contribution Workflow
1. **Branch Strategy**: Feature branches with descriptive names
2. **Code Review**: Pull request review process
3. **Automated Checks**: Linting, type checking, and testing
4. **Documentation**: Update relevant documentation with changes
5. **Deployment**: Staging review before production deployment

## MCP Integration Setup

### Memory Bank Integration
```typescript
// Project persistence and context management
const memoryBankConfig = {
  projectName: "zurno-backend-admin",
  contextFiles: [
    "architecture-decisions.md",
    "api-endpoints.md",
    "component-catalog.md",
    "business-logic-patterns.md"
  ]
};
```

### Context7 Integration
```typescript
// Documentation access for libraries
const librariesUsed = [
  "/facebook/react/18",
  "/reduxjs/redux-toolkit",
  "/mui/material-ui",
  "/vitejs/vite",
  "/typescript-eslint/typescript-eslint"
];
```

### Sequential Thinking Integration
```typescript
// Complex multi-step reasoning for architectural decisions
const complexTasks = [
  "performance-optimization-planning",
  "security-audit-implementation",
  "component-refactoring-strategy",
  "api-integration-patterns"
];
```

## Documentation Folder Structure

```
docs/
├── architecture/
│   ├── overview.md
│   ├── component-hierarchy.md
│   ├── state-management.md
│   └── api-integration.md
├── components/
│   ├── design-system.md
│   ├── component-catalog.md
│   └── usage-examples.md
├── development/
│   ├── getting-started.md
│   ├── coding-standards.md
│   ├── testing-guide.md
│   └── deployment.md
├── business/
│   ├── feature-requirements.md
│   ├── user-workflows.md
│   └── admin-permissions.md
└── operations/
    ├── monitoring.md
    ├── performance.md
    └── security.md
```

---

**Note**: This documentation follows the G⊕C⊕E⊕S principle (Goal ⊕ Context ⊕ Expectation ⊕ Source) to provide comprehensive, actionable context for AI-assisted development while maintaining clarity and utility for human developers.