image: node:22-alpine

definitions:
  caches:
    yarn: /usr/local/share/.cache/yarn

pipelines:
  branches:
    staging:
      - step:
          name: Build and Deploy Staging
          caches:
            - yarn
          script:
            - yarn install

            - echo "VITE_APP_API_URL=${VITE_APP_API_URL}" > .env
            - echo "MODE=${MODE}" >> .env
            - echo "VITE_GOOGLE_PLACES_API_KEY=${VITE_GOOGLE_PLACES_API_KEY}" >> .env
            - echo "VITE_ZURNO_ADMIN_USER=${VITE_ZURNO_ADMIN_USER}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_KEY=${VITE_FIREBASE_CONFIG_API_KEY}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_AUTH_DOMAIN=${VITE_FIREBASE_CONFIG_API_AUTH_DOMAIN}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_DATABASE_URL=${VITE_FIREBASE_CONFIG_API_DATABASE_URL}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_PROJECT_ID=${VITE_FIREBASE_CONFIG_API_PROJECT_ID}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_STORAGE_BUCKET=${VITE_FIREBASE_CONFIG_API_STORAGE_BUCKET}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_MESSAGING_SENDER_ID=${VITE_FIREBASE_CONFIG_API_MESSAGING_SENDER_ID}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_APP_ID=${VITE_FIREBASE_CONFIG_API_APP_ID}" >> .env
            - echo "VITE_FIREBASE_CONFIG_API_MEASUREMENT_ID=${VITE_FIREBASE_CONFIG_API_MEASUREMENT_ID}" >> .env
            - echo "VITE_AWS_SNS_REGION=${VITE_AWS_SNS_REGION}" >> .env
            - echo "ADS_WEBSITE_DOMAIN=${ADS_WEBSITE_DOMAIN}" >> .env
            - echo "VITE_SOCKET_HOST=${VITE_SOCKET_HOST}" >> .env
            - echo "VITE_SOCKET_PORT=${VITE_SOCKET_PORT}" >> .env
            - echo "VITE_SOCKET_PASSWORD=${VITE_SOCKET_PASSWORD}" >> .env
            - echo "VITE_SOCKET_PROTOCOL=${VITE_SOCKET_PROTOCOL}" >> .env
            - cat .env

            - apk add --no-cache rsync openssh git

            - yarn build

            # Setup SSH directory
            - mkdir -p ~/.ssh
            - chmod 700 ~/.ssh
            - ssh-keyscan -H $SSH_HOST >> ~/.ssh/known_hosts
            - chmod 644 ~/.ssh/known_hosts

            # Deploy using rsync
            - rsync -avz --delete dist/ $SSH_USER@$SSH_HOST:$REMOTE_PATH
          artifacts:
            - dist/**
