/* EventRaffleModal.css */
.raffle-modal .modal-content {
  background: linear-gradient(135deg, #9f7aea 0%, #3182ce 100%);
  border: none;
  height: 100vh;
}

.raffle-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 90vh;
  padding: 2rem;
}

.raffle-title {
  text-align: center;
  margin-bottom: 2rem;
}

.raffle-title h1 {
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
}

.raffle-title p {
  color: white;
  font-size: 1.125rem;
  opacity: 0.9;
}

.wheel-container {
  position: relative;
  margin-bottom: 2rem;
}

.wheel-pointer {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%) translateY(-0.5rem);
  z-index: 10;
  width: 0;
  height: 0;
  border-top: 24px solid white;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
}
/* 
.wheel-svg {
  filter: drop-shadow(0 25px 25px rgba(0, 0, 0, 0.25));
} */

.controls {
  text-align: center;
}

.spin-button {
  padding: 1rem 2rem;
  margin: 1rem;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 9999px;
  border: none;
  transition: all 0.3s ease;
  transform: scale(1);
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
  color: #374151;
}

.spin-button:not(:disabled) {
  background-color: #0de2ca;
}

.spin-button:not(:disabled):hover {
  background-color: #f5e90b;
  transform: scale(1.05);
}

.spin-button:not(:disabled):active {
  transform: scale(0.95);
}

.spin-button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.reset-button {
  margin-left: 1rem;
  padding: 1rem 1.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: 9999px;
  background-color: #e5e7eb;
  color: #374151;
  border: none;
  transition: all 0.3s ease;
  transform: scale(1);
}

.reset-button:hover {
  background-color: #f3f4f6;
  transform: scale(1.05);
}

.reset-button:active {
  transform: scale(0.95);
}

.winner-display {
  padding: 1.5rem;
  background-color: white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: pulse 2s infinite;
}

.winner-display h2 {
  font-size: 1.5rem;
  font-weight: bold;
  text-align: center;
  color: #374151;
  margin-bottom: 0.5rem;
}

.winner-display h3 {
  font-size: 1.4rem;
  font-weight: bold;
  text-align: center;
  color: #374151;
  margin-bottom: 0.5rem;
}

.winner-display p {
  font-size: 1.25rem;
  text-align: center;
  color: #4b5563;
}

.winner-prize {
  font-weight: bold;
  color: #7c3aed;
}

.instructions {
  margin-top: 2rem;
  text-align: center;
  color: white;
  opacity: 0.75;
  max-width: 28rem;
}

.instructions p {
  font-size: 0.875rem;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}