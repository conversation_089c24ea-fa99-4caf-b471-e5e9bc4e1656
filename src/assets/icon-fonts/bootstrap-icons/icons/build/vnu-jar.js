#!/usr/bin/env node

/*!
 * <PERSON><PERSON><PERSON> to run vnu-jar if Java is available.
 * Copyright 2017-2022 The Bootstrap Authors
 * Copyright 2017-2022 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */

"use strict";

const { execFile, spawn } = require("child_process");
const vnu = require("vnu-jar");

execFile("java", ["-version"], (error, stdout, stderr) => {
  if (error) {
    console.error("Skipping vnu-jar test; Java is missing.");
    return;
  }

  const is32bitJava = !/64-Bit/.test(stderr);

  // vnu-jar accepts multiple ignores joined with a `|`.
  // Also note that the ignores are string regular expressions.
  const ignores = [].join("|");

  const args = [
    "-jar",
    `"${vnu}"`,
    "--asciiquotes",
    "--skip-non-html",
    "--Werror",
    `--filterpattern "${ignores}"`,
    "_site/",
  ];

  // For the 32-bit Java we need to pass `-Xss512k`
  if (is32bitJava) {
    args.splice(0, 0, "-Xss512k");
  }

  return spawn("java", args, {
    shell: true,
    stdio: "inherit",
  }).on("exit", process.exit);
});
