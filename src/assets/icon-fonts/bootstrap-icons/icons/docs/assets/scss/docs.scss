@import "bootstrap/functions";
@import "bootstrap/variables";
@import "bootstrap/maps";
@import "bootstrap/mixins";
@import "bootstrap/utilities";
@import "bootstrap/root";
@import "bootstrap/reboot";
@import "bootstrap/type";
@import "bootstrap/images";
@import "bootstrap/containers";
@import "bootstrap/grid";
@import "bootstrap/tables";
@import "bootstrap/forms";
@import "bootstrap/buttons";
@import "bootstrap/transitions";
@import "bootstrap/dropdown";
@import "bootstrap/button-group";
@import "bootstrap/nav";
@import "bootstrap/navbar";
// @import "bootstrap/card";
// @import "bootstrap/accordion";
@import "bootstrap/breadcrumb";
// @import "bootstrap/pagination";
// @import "bootstrap/badge";
// @import "bootstrap/alert";
// @import "bootstrap/progress";
// @import "bootstrap/list-group";
@import "bootstrap/close";
// @import "bootstrap/toasts";
// @import "bootstrap/modal";
// @import "bootstrap/tooltip";
// @import "bootstrap/popover";
// @import "bootstrap/carousel";
// @import "bootstrap/spinners";
@import "bootstrap/offcanvas";
// @import "bootstrap/placeholders";
@import "bootstrap/helpers";
@import "bootstrap/utilities/api";

@import "variables";
@import "buttons";
@import "clipboard-js";
@import "navbar";
@import "skippy";
@import "footer";
@import "syntax";
@import "ads";

.bd-gutter {
  --bs-gutter-x: #{$bd-gutter-x};
}

.bi {
  display: inline-block;
  vertical-align: -0.125em;
  fill: currentcolor;
}

.hero-notice {
  background-color: $teal-100;

  @media (min-width: 540px) {
    border-radius: 5em !important; // stylelint-disable-line declaration-no-important
  }
}

.highlight {
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  background-color: var(--bs-gray-100);
  border-radius: 0.5rem;

  pre {
    margin-bottom: 0;
    scrollbar-width: none;

    &:focus {
      outline: 0;
    }

    &::-webkit-scrollbar {
      display: none;
    }

    code {
      word-wrap: normal;
    }
  }
}

.bd-example {
  padding: 1.25rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;

  + .bd-clipboard + .highlight pre {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }
}

.f0 {
  font-size: 2rem;

  @media (min-width: 520px) {
    font-size: 3rem;
  }
}
.f3 {
  font-size: 1.25rem;

  @media (min-width: 520px) {
    font-size: 1.5rem;
  }
}
.f5 {
  font-size: 1rem;
}

.hero {
  border-bottom: 1px solid rgba(0, 0, 0, 0.075);

  .highlight {
    margin-bottom: 0;
    color: var(--bs-gray-200);
    background-color: var(--bs-gray-800);

    pre {
      margin-bottom: 0;

      @media (min-width: 768px) {
        padding-right: 4em;
      }
    }
  }

  .btn-clipboard {
    top: 0.625em;
    color: var(--bs-gray-200);
    background-color: var(--bs-gray-800);
  }

  .btn {
    padding: 1rem 1.25rem;
    border-radius: 0.5rem;
  }

  hr {
    max-width: 100px;
  }
}

.icon-search {
  @media (min-width: 768px) {
    width: 35%;
  }
}

.list {
  font-size: 2rem;

  // stylelint-disable  declaration-no-important
  a:hover,
  a:focus {
    &,
    .name {
      color: var(--bs-blue) !important;
    }
  }
  // stylelint-enable  declaration-no-important

  &:empty::before {
    display: block;
    width: 100%;
    padding: 100px 2rem;
    margin-right: 15px;
    margin-left: 15px;
    color: var(--bs-gray-500);
    text-align: center;
    content: "Nothing found, try searching again.";
    background-color: var(--bs-gray-100);
    border-radius: 0.5rem;
  }
}

.btn-group > .btn {
  flex-shrink: 0;
}

.name {
  font-size: 0.8125rem;
}

@media (min-width: 1200px) {
  .row-cols-xl-8 {
    > * {
      flex: 0 0 12.5%;
      max-width: 12.5%;
    }
  }
}

.icon-demo {
  background-color: #fdfdfd;
  background-image: radial-gradient(circle, #ddd 1px, rgba(0, 0, 0, 0) 1px);
  background-size: 1rem 1rem;
}

.icon-demo,
.icon-demo-examples {
  .bi {
    width: 1em;
    height: 1em;
  }
}

// stylelint-disable declaration-no-important
.py-6 {
  padding-top: 4.5rem !important;
  padding-bottom: 4.5rem !important;
}
// stylelint-enable declaration-no-important
