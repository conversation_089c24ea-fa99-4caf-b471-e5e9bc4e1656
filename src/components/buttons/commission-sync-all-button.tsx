import React, { <PERSON> } from 'react';
import { Button } from 'react-bootstrap';
import { hasSuperAdminPermission } from '../../utils/authorization';
import Swal from 'sweetalert2';
import { useSyncAllCommisisonsMutation } from '../../services/affiliation/commission';

interface CommissionSyncAllButtonProps {
  affiliateId: string | undefined;
  setIsLoading: (enable: boolean) => void;
  loadData: () => void;
}

const CommissionSyncAllButton: FC<CommissionSyncAllButtonProps> = ({ affiliateId, setIsLoading, loadData }) => {
  const [syncAllCommissions] = useSyncAllCommisisonsMutation();

  const showConfirmationDialog = () => {
    const dialogContent = affiliateId ?
      `You are going to sync<br/>commission rate and amount of <b>ALL commissions</b><br/>of affiliate ID ${affiliateId}<br/>with the current tier config.` :
      `You are going to sync<br/>commission rate and amount of <b>ALL commissions</b><br/>with the current tier config.`;

    Swal.fire({
      title: "Are you sure?",
      html: dialogContent,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, proceed!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          syncAllCommissions(affiliateId)
            .unwrap()
            .then(() => {
              loadData();
            })
            .catch(console.error)
            .finally(() => {
              setIsLoading(false);
            });
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  return (
    <Button
      hidden={!hasSuperAdminPermission()}
      variant="primary-light m-2"
      onClick={showConfirmationDialog}
    >
      Sync All Commissions <i className="bi bi-arrow-clockwise" />
    </Button>
  );
}

export default CommissionSyncAllButton;