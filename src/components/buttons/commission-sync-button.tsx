import React, { <PERSON> } from 'react';
import { Button } from 'react-bootstrap';
import { hasSuperAdminPermission } from '../../utils/authorization';
import Swal from 'sweetalert2';
import { useSyncCommisisonMutation } from '../../services/affiliation/commission';

interface CommissionSyncButtonProps {
  commissionId: string;
  setIsLoading: (enable: boolean) => void;
  loadData: () => void;
}

const CommissionSyncButton: FC<CommissionSyncButtonProps> = ({ commissionId, setIsLoading, loadData }) => {
  const [syncCommission] = useSyncCommisisonMutation();

  const showConfirmationDialog = () => {
    if (!commissionId) {
      console.error('Commission ID is required');
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      html: "You are going to sync<br/>the commission rate and amount<br/>with the current tier config.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, proceed!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          syncCommission(commissionId)
            .unwrap()
            .then(() => {
              loadData();
            })
            .catch(console.error)
            .finally(() => {
              setIsLoading(false);
            });
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  return (
    <Button
      hidden={!hasSuperAdminPermission()}
      variant="primary-light m-2"
      onClick={showConfirmationDialog}
    >
      Sync Commission <i className="bi bi-arrow-clockwise" />
    </Button>
  );
}

export default CommissionSyncButton;