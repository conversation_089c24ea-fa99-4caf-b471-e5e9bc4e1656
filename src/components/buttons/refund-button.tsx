import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, Modal } from "react-bootstrap";
import {
  useLazyGetRefundStatusQuery,
  useRefundOrderTransactionMutation,
} from "../../services/order";
import { currencySymbol } from "../../utils/constant/currency";
import { toast } from "react-toastify";

interface RefundButtonProps {
  orderId: string;
  orderCurrency?: string;
  onRefundSuccess?: () => void;
}

export const RefundButton: React.FC<RefundButtonProps> = ({
  orderId,
  orderCurrency = "USD",
  onRefundSuccess,
}) => {
  const [refundStatus, setRefundStatus] =
    useState<TRefundStatusResponse | null>(null);
  const [isLoadingStatus, setIsLoadingStatus] = useState(false);
  const [isRefunding, setIsRefunding] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const [getRefundStatus] = useLazyGetRefundStatusQuery();
  const [refundTransaction] = useRefundOrderTransactionMutation();

  useEffect(() => {
    if (orderId && orderId.trim() !== "") {
      loadRefundStatus();
    }
  }, [orderId]);

  const loadRefundStatus = async () => {
    setIsLoadingStatus(true);
    try {
      const result = await getRefundStatus(orderId).unwrap();
      setRefundStatus(result);
    } catch (error: any) {
      console.error("Error loading refund status:", error);
    } finally {
      setIsLoadingStatus(false);
    }
  };

  if (!orderId || orderId.trim() === "" || !refundStatus?.canRefund) {
    return null;
  }

  const handleRefundClick = () => {
    if (!orderId || orderId.trim() === "") {
      console.error(
        "Cannot proceed with refund: orderId is empty or undefined"
      );
      return;
    }
    setShowConfirmModal(true);
  };

  const handleConfirmRefund = async () => {
    if (!orderId || orderId.trim() === "") {
      console.error(
        "Cannot proceed with refund: orderId is empty or undefined"
      );
      return;
    }

    setIsRefunding(true);
    try {
      const result = await refundTransaction(orderId).unwrap();
      setShowConfirmModal(false);

      console.log("Refund API Response:", result);
      toast.success("Order refunded successfully");
      await loadRefundStatus();
      onRefundSuccess?.();
    } catch (error: any) {
      console.error("Refund API Error:", error);
      const errorMessage =
        error?.data?.message || error?.message || "Failed to process refund";
      toast.error(errorMessage);
    } finally {
      setIsRefunding(false);
    }
  };

  const handleCancelRefund = () => {
    setShowConfirmModal(false);
  };

  return (
    <>
      <Button
        variant="danger"
        size="sm"
        className="ms-2 d-flex align-items-center fs-12"
        onClick={handleRefundClick}
        disabled={isLoadingStatus || isRefunding}
      >
        {isLoadingStatus || isRefunding ? (
          <>
            <i className="spinner-border spinner-border-sm me-2" />
            <span>Processing...</span>
          </>
        ) : (
          <>
            <i className="bi-arrow-return-left me-1 fs-12" />
            <span>Refund</span>
          </>
        )}
      </Button>

      <Modal
        show={showConfirmModal}
        onHide={handleCancelRefund}
        centered
        size="lg"
        className="custom-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title className="d-flex align-items-center fw-semibold">
            <i className="bi-exclamation-triangle text-warning me-2 fs-18" />
            <span>Confirm Refund</span>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="mb-4">
            <p className="mb-2 fs-14 fw-medium">
              Are you sure you want to refund this transaction?
            </p>
            <p className="text-muted fs-12 mb-0">
              This action cannot be undone.
            </p>
          </div>

          {refundStatus?.transaction && (
            <div className="card border-0 bg-light mb-3">
              <div className="card-body p-3">
                <h6 className="card-title mb-3 fw-semibold fs-14">
                  Transaction Details
                </h6>
                <div className="row g-2">
                  <div className="col-4">
                    <span className="text-muted fs-12">Amount:</span>
                  </div>
                  <div className="col-8">
                    <span className="fw-medium fs-12">
                      {currencySymbol[orderCurrency]}{" "}
                      {refundStatus.transaction.amount}
                    </span>
                  </div>
                  <div className="col-4">
                    <span className="text-muted fs-12">Source:</span>
                  </div>
                  <div className="col-8">
                    <span className="fw-medium fs-12">
                      {refundStatus.transaction.source}
                    </span>
                  </div>
                  <div className="col-4">
                    <span className="text-muted fs-12">ID:</span>
                  </div>
                  <div className="col-8">
                    <span className="fw-medium fs-11 font-monospace">
                      {refundStatus.transaction.sourceId}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {refundStatus?.refundReason && (
            <div className="alert alert-info border-0 bg-info-subtle">
              <div className="d-flex">
                <i className="bi-info-circle text-info me-2 mt-1 fs-14" />
                <div>
                  <strong className="text-info fs-13">Note:</strong>
                  <span className="ms-1 fs-13">
                    {refundStatus.refundReason}
                  </span>
                </div>
              </div>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="light"
            className="border fs-13"
            onClick={handleCancelRefund}
            disabled={isRefunding}
          >
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={handleConfirmRefund}
            disabled={isRefunding}
            className="ms-2 fs-13"
          >
            {isRefunding ? (
              <>
                <i className="spinner-border spinner-border-sm me-2" />
                <span>Processing...</span>
              </>
            ) : (
              <span>Confirm Refund</span>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
