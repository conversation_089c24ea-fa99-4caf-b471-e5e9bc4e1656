import moment from "moment";
import { FC, Fragment, useEffect, useState, useRef, useCallback } from "react";
import {
    Button,
    Dropdown, Toast, ToastContainer
} from "react-bootstrap";
import { Link, useLocation,  } from "react-router-dom";
import SimpleBar from "simplebar-react";
import { useLazyDeleteNotificationByIdQuery, useLazyListNotificationQuery, useReadNotificationMutation } from "../../../services/notification";

import { ref, onValue } from "firebase/database";
import { database } from "./../../../../firebaseConfig";
import {store} from "../../../services/rtk/store.ts";
import notificationSound from '../../../../audio/trumpeter_swans_fly_land_splash_loud_calls_2pm_ttp_a_190401_54375.wav'

interface HeaderNotificationsProps { }

const HeaderNotifications: FC<HeaderNotificationsProps> = ({ }: any) => {

    // const img1 = <img src={faces2} alt="" />;
    // const img2 = <img src={faces8} alt="" />;

    // const initialNotifications = [
    //   {
    //     id: 1,
    //     src: img1,
    //     icon: "",
    //     name: "Olivia James",
    //     text1: "Congratulate for New template start",
    //     text2: "",
    //     text3: "2 min ago",
    //     avatarcolor: "secondary",
    //   },
    //   {
    //     id: 2,
    //     src: "",
    //     icon: "bx bx-pyramid fs-18",
    //     name: "Order Placed",
    //     text1: "Order Placed Successfully",
    //     text2: "ID: #1116773",
    //     text3: "5 min ago",
    //     avatarcolor: "warning",
    //   },
    //   {
    //     id: 3,
    //     src: img2,
    //     icon: "",
    //     name: "Elizabeth Lewis",
    //     text1: "added new schedule realease date",
    //     text2: "",
    //     text3: "10 min ago",
    //     avatarcolor: "secondary",
    //   },
    //   {
    //     id: 4,
    //     src: "",
    //     icon: "bx bx-pulse fs-18",
    //     name: "Your Order Has Been Shipped",
    //     text1: "Order No: 123456 Has Shipped To Your Delivery Address",
    //     text2: "",
    //     text3: "12 min ago",
    //     avatarcolor: "primary",
    //   },
    //   {
    //     id: 5,
    //     src: "",
    //     icon: "bx bx-badge-check",
    //     name: "Account Has Been Verified",
    //     text1: "Your Account Has Been Verified Sucessfully",
    //     text2: "",
    //     text3: "20 min ago",
    //     avatarcolor: "pink",
    //   },
    // ];

    const CHATBOT_NOTIFICATION = 20
    // const [notifications, setNotifications] = useState([...initialNotifications]);
    const [displayNotifications, setDisplayNotifications] = useState<any[]>([]);
    const [notifications, setNotifications] = useState<TNotification[]>([]);
    const [totalUnread, setTotalUnread] = useState(0)
    const [hasMore, setHasMore] = useState(false)
    const [trigger] = useLazyListNotificationQuery()
    const [page, setPage] = useState(1)
    const [showToast, setShowToast] = useState(false)
    const [toastMessage, setToastMessage] = useState("There is a new notification")

    const location = useLocation()
    const isChatbotPage = location.pathname.startsWith("/chatbot")
    const triggerRef = useRef(trigger)
    const audioRef = useRef<HTMLAudioElement| null>(null)
    const prevUnreadRef = useRef<number>(0)

    triggerRef.current = trigger
    const refetchNotifications = useCallback(
        () => {
            triggerRef
                .current({page: 1})
                .unwrap()
                .then((res) => {
                    const incomingNotifications = res.data || []
                    setNotifications(incomingNotifications);
                    setTotalUnread(
                        (res.meta.total || 0) - (res.meta.readCount || 0)
                    )
                    setHasMore(res.meta.nextPageUrl)
                    const lastNotification = incomingNotifications[0]
                    setToastMessage(lastNotification.title || "There is a new notification")
                })

        },
        []
    )

    const collapseChatbotNotifications = useCallback((notifications: TNotification[]) => {
        const latestChatbotByDate = new Map<string, TNotification>()
        const othersNotification : TNotification[] = []
        for (const notification of notifications) {
            if (Number(notification.type) === CHATBOT_NOTIFICATION && notification.resourceId) {
                const prev = latestChatbotByDate.get(notification.resourceId)
                if (!prev || new Date(notification.createdAt) > new Date(prev.createdAt)) {
                    latestChatbotByDate.set(notification.resourceId, notification)
                }
            } else {
                othersNotification.push(notification)
            }
        }

        const chatbotLatestNotifications = Array.from(latestChatbotByDate.values())
        const merged = [...chatbotLatestNotifications, ...othersNotification]
        return merged.sort(
            (a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )
    }, [])

    const toggleToast = () => setShowToast((value) => !value)

    const currentAdmin = store.getState().auth?.user

    const getRealtimeData = (path : string, onChange ) => {
        const dataRef = ref (database, path)

        return onValue(dataRef, (snapshot) => {
            if (snapshot.exists()) {
                onChange()
            }
            },)
    }

    useEffect(() => {
        if (!currentAdmin?.id) return
        const unsubscribe = getRealtimeData(
           `/admins/${currentAdmin.id}/notifications`,
            refetchNotifications
        )

        return () => {
            unsubscribe()
        }
    }, [currentAdmin?.id, refetchNotifications])

    useEffect(() => {
        trigger({ page })
            .unwrap()
            .then((res) => {
                setNotifications(res.data || [])
                setTotalUnread((res.meta.total || 0) - (res.meta.readCount || 0))
                setHasMore(res.meta.nextPageUrl)
            })
    }, [])

    useEffect(() => {
        setDisplayNotifications(collapseChatbotNotifications(notifications)
            .map((notification) => {
                const icon = notification.isRead
                    ? "bx bx-check"
                    : "bx bx-bell"
                const avatarcolor = notification.isRead
                    ? "success"
                    : "warning"

                const type = Number(notification.type)
                let resourceId : string
                switch (type) {
                    case CHATBOT_NOTIFICATION:
                        resourceId = notification.resourceId
                        break
                    case 6 :
                    case 7:
                        resourceId = notification.comment?.postId
                        break
                    default:
                        resourceId = notification.resourceId
            }


            return {
                id: notification.id,
                src: "",
                icon,
                avatarcolor,
                title: notification.title,
                subtitle: "",
                text: notification.description,
                time: moment(notification.createdAt).fromNow(),
                resourceId: resourceId,
                type: notification.type,
                postSource: notification.postSource
            }
        }))
    }, [notifications])

    useEffect(() => {
        audioRef.current = new Audio(notificationSound)
        audioRef.current.load()
    }, [])

    useEffect(() => {
        if (!audioRef.current) return
        if (totalUnread > prevUnreadRef.current) {
            audioRef.current
                .play()
                .catch(() => {})
            if (!isChatbotPage) {
                setShowToast(true)
            }
        }
        prevUnreadRef.current = totalUnread
    }, [totalUnread, isChatbotPage])

    const [readNotification] = useReadNotificationMutation()
    const handleNotificationClick = (index: number) => {
        if (notifications[index].isRead) { return }

        readNotification(notifications[index].id)
            .unwrap()
            .then(() => {
                setNotifications((prev) => prev.map(note => note.id != notifications[index].id ? note : {
                    ...note,
                    isRead: true
                }))
                setTotalUnread(Math.max(totalUnread - 1, 0))
            })

    }

    const [deleteNotification] = useLazyDeleteNotificationByIdQuery()
    const handleNotificationClose = (index: number) => {
        deleteNotification(notifications[index].id)
            .unwrap()
            .then(() => {
                // Create a copy of the notifications array and remove the item at the specified index
                // const updatedNotifications = [...notifications];
                // const [deleted] = updatedNotifications.splice(index, 1);
                // setNotifications(updatedNotifications);
                setNotifications((prev) => prev.filter(note => note.id != notifications[index].id))
                setTotalUnread(Math.max(totalUnread - 1, 0))
            })
    };

    const handleViewMoreClick = () => {
        trigger({ page: page + 1 })
            .unwrap()
            .then((res) => {
                setPage(page + 1)
                setNotifications((prev) => [...prev, ...(res.data || [])])
                setTotalUnread((res.meta.total || 0) - (res.meta.readCount || 0))
                setHasMore(res.meta.nextPageUrl)
            })
    }

    return (
        <Fragment>
            <div className="header-content-right">
                {showToast && (
                    <ToastContainer  className="toast-container position-fixed bottom-0 end-0 p-3">
                        <Toast id="bottomright-Toast" className="toast colored-toast bg-success text-fixed-white"
                               onClose={toggleToast} show={showToast} delay={3000} autohide>
                            <Toast.Body className="toast-body">
                                {toastMessage}
                            </Toast.Body>
                        </Toast>
                    </ToastContainer>
                )}
                <Dropdown
                    className="header-element notifications-dropdown"
                    autoClose="outside"
                >
                    <Dropdown.Toggle
                        variant=""
                        className="header-link dropdown-toggle"
                        data-bs-toggle="dropdown"
                        data-bs-auto-close="outside"
                        id="messageDropdown"
                        aria-expanded="false"
                    >
                        <i className="bx bx-bell bx-flip-horizontal header-link-icon ionicon"></i>
                        <span
                            className="badge bg-info rounded-pill header-icon-badge pulse pulse-secondary"
                            id="notification-icon-badge"
                        >
                            {totalUnread}
                        </span>
                    </Dropdown.Toggle>
                    <Dropdown.Menu
                        align="end"
                        className="main-header-dropdown dropdown-menu  border-0 dropdown-menu-end"
                        data-popper-placement="none"
                    >
                        <div className="p-3">
                            <div className="d-flex align-items-center justify-content-between">
                                <p className="mb-0 fs-17 fw-semibold">Notifications</p>
                                <span
                                    className="badge bg-secondary-transparent"
                                    id="notifiation-data"
                                >{`${totalUnread} Unread`}</span>
                            </div>
                        </div>
                        <div className="dropdown-divider"></div>
                        <ul className="list-unstyled mb-0">
                            <SimpleBar id="header-notification-scroll">

                                {displayNotifications.map((notification, index) => {
                                    let linkTo: string
                                    if (notification.resourceId == null) {
                                        linkTo = "#"
                                    } else {
                                        switch (notification.type) {
                                            case "6":
                                            case "7":
                                                switch (notification.postSource) {
                                                    case "zurno":
                                                        linkTo = `/managements-marketplace/zurno-post/${notification.resourceId}`
                                                        break
                                                    default:
                                                        linkTo = `/managements-marketplace/post/${notification.resourceId}`
                                                }
                                                break
                                            case "20":
                                                linkTo = `/chatbot/${notification.resourceId}`
                                                break
                                            default:
                                                linkTo = "#"
                                                break
                                        }
                                    }
                                    return (
                                        <Dropdown.Item as="li" key={index}>
                                            <Link
                                                to={linkTo}
                                                onClick={() => handleNotificationClick(index)}
                                            >
                                                <div className="d-flex align-items-start">
                                                    <div className="pe-2">
                                                    <span
                                                        className={`avatar avatar-md bg-${notification.avatarcolor}-transparent rounded-2`}
                                                    >
                                                        {notification.src}
                                                        <i className={notification.icon}></i>
                                                    </span>
                                                    </div>
                                                    <div className="flex-grow-1 d-flex  justify-content-between">
                                                        <div>
                                                            <p className="mb-0 fw-semibold">
                                                                {notification.title}
                                                                {" "}
                                                                <span className="text-warning">
                                                                {notification.subtitle}
                                                            </span>
                                                            </p>
                                                            <span className="fs-12 text-muted fw-normal">
                                                            {notification.text}
                                                        </span>
                                                        </div>
                                                        <div className="min-w-fit-content ms-2 text-end">
                                                            <Button
                                                                variant="light"
                                                                className="btn-sm rounded-pill p-1"
                                                                onClick={(e) => {
                                                                    e.preventDefault()
                                                                    e.stopPropagation()
                                                                    handleNotificationClose(index)
                                                                }}
                                                            >
                                                                <i className="ti ti-x"></i>
                                                            </Button>
                                                            <p className="mb-0 text-muted fs-11">
                                                                {notification.time}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </Link>
                                        </Dropdown.Item>
                                    )
                                })}
                            </SimpleBar>
                        </ul>
                        {hasMore &&
							<div
								className={`p-3 empty-header-item1 border-top ${displayNotifications.length === 0 ? "d-none" : "d-block"
                                }`}
							>
								<div className="d-grid">
									<Button onClick={handleViewMoreClick}>
										View More
									</Button>
                                    {/* <Link
                                    to={`${import.meta.env.BASE_URL}pages/notifications`}
                                    className="btn btn-primary"
                                >
                                    View All
                                </Link> */}
								</div>
							</div>
                        }
                        <div
                            className={`p-5 empty-item1 ${displayNotifications.length === 0 ? "d-block" : "d-none"
                            }`}
                        >
                            <div className="text-center">
                                <span className="avatar avatar-xl avatar-rounded bg-secondary-transparent">
                                    <i className="bx bx-bell-off bx-tada fs-2"></i>
                                </span>
                                <h6 className="fw-semibold mt-3">No New Notifications</h6>
                            </div>
                        </div>
                    </Dropdown.Menu>
                </Dropdown>
            </div>
        </Fragment>
    );
};

export default HeaderNotifications;
