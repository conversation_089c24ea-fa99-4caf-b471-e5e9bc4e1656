import { FC, useEffect, useState } from "react";
import { getAllErrorMessages } from "../../utils/errors";
import Swal from "sweetalert2";
import { useLazyGetPaymentMethodsQuery, usePayForCommissionRequestMutation } from "../../services/affiliation/affiliate";
import PaymentForm from "./payment-form";

interface AffiliatePaymentDialogProps {
  affiliate: TAffiliate;
  show: boolean;
  setIsLoading: (loading: boolean) => void;
  handleClose: (success: boolean) => void;
}

const AffiliatePaymentDialog: FC<AffiliatePaymentDialogProps> = ({ affiliate, show, setIsLoading, handleClose }) => {
  const [err, setErr] = useState<any>({});
  const [paymentMethods, setPaymentMethods] = useState<TPaymentMethod[]>([]);
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>('');

  const [getAllPaymentMethods] = useLazyGetPaymentMethodsQuery();
  const [addPayment] = usePayForCommissionRequestMutation();

  const loadPaymentMethods = (affiliateId: string) => {
    setIsLoading(true);
    setErr({});
    getAllPaymentMethods(affiliateId)
      .unwrap()
      .then((receivedMethods) => {
        setPaymentMethods(receivedMethods);
        if (!selectedPaymentMethodId) {
          for (const method of receivedMethods) {
            if (method.isDefault) {
              setSelectedPaymentMethodId(method.id);
            }
          }
        }
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const submitPayment = (paymentMethodId: string, amount: number) => {
    Swal.fire({
      title: "Are you sure?",
      html: `<p>You are recording to pay ${affiliate.user?.firstName} ${affiliate.user?.lastName} for the amount of $${amount}</p><p>An automatic email will be sent to the affiliate.</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, pay!",
    })
      .then((result) => {
        if (result.isConfirmed) {

          setErr({});
          setIsLoading(true);
          addPayment({
            affiliateId: affiliate.id,
            paymentMethodId,
            amount,
          })
            .unwrap()
            .then(() => {
              handleClose(true);
            })
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            })
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  useEffect(() => {
    if (show) {
      loadPaymentMethods(affiliate.id);
    }
  }, [show]);

  return (
    <PaymentForm
      show={show}
      paymentMethods={paymentMethods}
      err={err}
      handleClose={handleClose}
      submitPayment={submitPayment}
    />
  );
}

export default AffiliatePaymentDialog;