import { Fragment } from "react";
import { Button, Form, Modal } from "react-bootstrap";
import { useState } from "react";

const CommissionRejectionDialog = ({ show, setShow, handleConfirm }: any) => {
  const [rejectReason, setRejectReason] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRejectReason(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShow(false);
    handleConfirm(rejectReason);
    setRejectReason("");
  };

  const handleClose = () => {
    setShow(false);
    setRejectReason("");
  };

  return (
    <Fragment>
      <Modal show={show} onHide={handleClose}>
        <Form onSubmit={handleSubmit}>
          <Modal.Header closeButton>
            <Modal.Title>Commission Rejection</Modal.Title>
          </Modal.Header>

          <Modal.Body>
            <div>
              <label htmlFor="rejectReason" className="form-label">Reject Reason</label>
              <Form.Control
                type="text"
                id="rejectReason"
                placeholder="Enter reason for rejection"
                value={rejectReason}
                onChange={handleChange}
              />
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="secondary" onClick={handleClose}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Confirm
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Fragment>
  );
};

export default CommissionRejectionDialog