import { FC, useEffect, useState } from "react";
import { <PERSON><PERSON>, But<PERSON>, Form, Modal } from "react-bootstrap";
import { ErrorType } from "../../utils/error_type";
import { getAllErrorMessages } from "../../utils/errors";
import { useUpdateCommissionGroupMutation } from "../../services/affiliation/affiliatie-tier-commission-group";
import { ECommissionGroupType } from "../enums/commission_group_type";

interface EditCommissionGroupDialogProps {
  isShow: boolean;
  group: TAffiliateTierCommissionGroup;
  onHide: (group: TAffiliateTierCommissionGroup) => void;
  setIsLoading: (isLoading: boolean) => void;
}

const EditCommissionGroupDialog: FC<EditCommissionGroupDialogProps> = ({ isShow, group, onHide, setIsLoading }) => {
  const [err, setErr] = useState<ErrorType>();
  const [selectedType, setSelectedType] = useState<ECommissionGroupType>(ECommissionGroupType.DEFAULT);

  const [updateCommissionGroup] = useUpdateCommissionGroupMutation();

  useEffect(() => {
    if (isShow) {
      setSelectedType(ECommissionGroupType[group.type as keyof typeof ECommissionGroupType]);
    }
  }, [isShow]);

  const handleFormChange = (e) => {
    e.preventDefault();
    setSelectedType(e.target.value);
  }

  const handleHide = () => {
    onHide(group);
  }

  const handleConfirm = (e) => {
    e.preventDefault();

    setIsLoading(true);
    setErr({});
    updateCommissionGroup({
      groupId: group.id,
      type: selectedType
    })
      .unwrap()
      .then((res) => { onHide(res) })
      .catch((error) => { setErr(getAllErrorMessages(error)) })
      .finally(() => { setIsLoading(false) });
  }

  return (
    <Modal show={isShow} onHide={handleHide}>
      <Form onSubmit={handleConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>Edit Group</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Form.Group className="mb-4">
            <Form.Label>Type</Form.Label>
            <Form.Select name='type' value={selectedType} onChange={handleFormChange}>
              {
                Object.values(ECommissionGroupType).map((type) => (
                  <option key={type} value={type}>{type === ECommissionGroupType.DEFAULT ? 'Default' : 'Flash Sale'}</option>
                ))
              }
            </Form.Select>
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default EditCommissionGroupDialog;