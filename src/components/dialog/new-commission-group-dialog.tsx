import { FC, useState } from "react";
import { Button, Form, Modal } from "react-bootstrap";
import { ECommissionGroupType } from "../enums/commission_group_type";

interface NewCommissionGroupDialogProps {
  isShow: boolean;
  onHide: () => void;
  handleAddCommissionGroup: (type: ECommissionGroupType) => void;
}

const NewCommissionGroupDialog: FC<NewCommissionGroupDialogProps> = ({ isShow, onHide, handleAddCommissionGroup }) => {
  const [selectedType, setSelectedType] = useState<ECommissionGroupType>(ECommissionGroupType.DEFAULT);

  const handleFormChange = (e) => {
    e.preventDefault();
    setSelectedType(e.target.value);
  }

  const handleConfirm = (e) => {
    e.preventDefault();

    handleAddCommissionGroup(selectedType);
    onHide();
  }

  return (
    <Modal show={isShow} onHide={onHide}>
      <Form onSubmit={handleConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>New Group</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Form.Group className="mb-4">
            <Form.Label>Type</Form.Label>
            <Form.Select name='type' value={selectedType} onChange={handleFormChange}>
              {
                Object.values(ECommissionGroupType).map((type) => (
                  <option key={type} value={type}>{type === ECommissionGroupType.DEFAULT ? 'Default' : 'Flash Sale'}</option>
                ))
              }
            </Form.Select>
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default NewCommissionGroupDialog;