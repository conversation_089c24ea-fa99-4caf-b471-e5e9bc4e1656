import { FC, useEffect, useState } from "react";
import { useAddVendorPaymentMutation, useLazyGetAllPaymentMethodsQuery, useLazyGetVendorBalanceQuery } from "../../services/vendors";
import { getAllErrorMessages } from "../../utils/errors";
import Swal from "sweetalert2";
import PaymentForm from "./payment-form";
import formatUSD from "../../utils/currency-formatter";

interface VendorPaymentDialogProps {
  vendor: TVendor;
  show: boolean;
  setIsLoading: (loading: boolean) => void;
  handleClose: (success: boolean) => void;
}

const VendorPaymentDialog: FC<VendorPaymentDialogProps> = ({ vendor, show, setIsLoading, handleClose }) => {
  const [err, setErr] = useState<any>({});
  const [balance, setBalance] = useState<number>(0);
  const [paymentMethods, setPaymentMethods] = useState<TPaymentMethod[]>([]);
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>('');

  const [getBalance] = useLazyGetVendorBalanceQuery();
  const [getAllPaymentMethods] = useLazyGetAllPaymentMethodsQuery();
  const [addPayment] = useAddVendorPaymentMutation();

  const loadBalance = (vendorId: string) => {
    setIsLoading(true);
    setErr({});
    getBalance(vendorId)
      .unwrap()
      .then(setBalance)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const loadPaymentMethods = (vendorId: string) => {
    setIsLoading(true);
    setErr({});
    getAllPaymentMethods(vendorId)
      .unwrap()
      .then((receivedMethods) => {
        setPaymentMethods(receivedMethods);
        if (!selectedPaymentMethodId) {
          for (const method of receivedMethods) {
            if (method.isDefault) {
              setSelectedPaymentMethodId(method.id);
            }
          }
        }
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const submitPayment = (paymentMethodId: string, amount: number, note: string) => {
    Swal.fire({
      title: "Are you sure?",
      html: `<p>You are recording to pay ${vendor.companyName} for the amount of ${formatUSD(amount)}</p><p>An automatic email will be sent to the vendor.</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, pay!",
    })
      .then((result) => {
        if (result.isConfirmed) {

          setErr({});
          setIsLoading(true);
          addPayment({
            vendorId: vendor.id,
            data: {
              amount,
              note,
              paymentMethodId
            }
          })
            .unwrap()
            .then(() => {
              handleClose(true);
            })
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            })
            .finally(() => {
              setIsLoading(false);
            })
        }
      })
      .catch(() => {
        Swal.fire("Error!", "Something went wrong!", "error");
      })
  }

  useEffect(() => {
    if (show) {
      loadBalance(vendor.id);
      loadPaymentMethods(vendor.id);
    }
  }, [show]);

  return (
    <PaymentForm
      show={show}
      balance={balance}
      paymentMethods={paymentMethods}
      err={err}
      handleClose={handleClose}
      submitPayment={submitPayment}
    />
  );
}

export default VendorPaymentDialog;