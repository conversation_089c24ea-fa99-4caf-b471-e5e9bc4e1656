import { debounce } from "lodash";
import React, { useCallback, useEffect, useState } from "react";
import Select, { InputActionMeta } from "react-select";
import { useLazySelectBundleQuery } from "../../services/bundle";

interface LazyBundleSelectProps {
  selectionFunctionQuery?: any;
  getSelectedOptions?: (values: any[] | any) => void;
  formatOptionLabel?: (value: any) => any;
  forceSearch?: [boolean, (value: boolean) => void];
  hidden?: boolean;
  isDisabled?: boolean;
  isClearable?: boolean;
  clearSelected?: [boolean, (value: boolean) => void];
}

const LazyBundleSelect: React.FC<LazyBundleSelectProps> = ({
  selectionFunctionQuery,
  getSelectedOptions,
  formatOptionLabel,
  forceSearch,
  hidden = false,
  isDisabled = false,
  isClearable = false,
  clearSelected,
}) => {
  const [selectBundle] = useLazySelectBundleQuery();

  const [page, setPage] = useState(1);
  const [search, setSearch] = useState("");
  const [selectedOption, setSelectedOption] = useState<any | null>(null);
  const [optionsSelection, setOptionsSelection] = useState<any[]>([]);
  const [optionsSelectionTemp, setOptionsSelectionTemp] = useState<any[]>([]);
  const [isSearchingOptions, setIsSearchingOption] = useState(false);

  const debouncedHandleOptionsSearch = useCallback(
    debounce((query) => {
      selectBundle({
        ...query,
        ...selectionFunctionQuery,
      })
        .unwrap()
        .then((res) => {
          setIsSearchingOption(false);
          setOptionsSelection(res.data || []);
        })
        .catch((error) => {
          console.log("error", error);
          setIsSearchingOption(false);
        });
    }, 1000),
    [selectionFunctionQuery]
  );

  useEffect(() => {
    if (clearSelected?.[0]) {
      setSelectedOption(null);
      clearSelected?.[1](false);
    }
  }, [clearSelected?.[0]]);

  const handleOptionsSearch = (value: string, actionMeta: InputActionMeta) => {
    if (actionMeta.action == "input-change") {
      setSearch(value);
    } else if (actionMeta.action == "input-blur") {
      setSearch("");
    }
  };

  useEffect(() => {
    const query = {
      search: search,
      page: 1,
    };
    setPage(1);
    setOptionsSelection([]);
    setIsSearchingOption(true);
    debouncedHandleOptionsSearch(query);
  }, [search]);

  useEffect(() => {
    if (forceSearch?.[0]) {
      const query = {
        search: search,
        page: 1,
      };
      setPage(1);
      setOptionsSelection([]);
      setIsSearchingOption(true);
      debouncedHandleOptionsSearch(query);
      forceSearch?.[1](false);
    }
  }, [forceSearch]);

  const debouncedHandleBottomScroll = useCallback(
    debounce((query) => {
      selectBundle(query)
        .unwrap()
        .then((res) => {
          setIsSearchingOption(false);
          setOptionsSelectionTemp(res.data || []);
        })
        .catch((error) => {
          console.log("error", error);
          setIsSearchingOption(false);
        });
    }, 1000),
    []
  );

  const handleBottomScroll = () => {
    const query = {
      search: search,
      page: page + 1,
    };

    setPage(page + 1);
    setIsSearchingOption(true);
    debouncedHandleBottomScroll(query);
  };

  useEffect(() => {
    if (optionsSelectionTemp.length > 0) {
      setOptionsSelection([...optionsSelection, ...optionsSelectionTemp]);
      setOptionsSelectionTemp([]);
    }
  }, [optionsSelectionTemp]);

  const handleOptionsChange = (optionsSelected: any) => {
    console.log(optionsSelected);

    if (optionsSelected) {
      getSelectedOptions?.(optionsSelected);
      setSelectedOption(optionsSelected);
    } else {
      getSelectedOptions?.(null);
      setSelectedOption(null);
    }
  };

  return (
    <div hidden={hidden}>
      <Select
        isDisabled={isDisabled}
        isClearable={isClearable}
        filterOption={() => true}
        isLoading={isSearchingOptions}
        value={selectedOption}
        options={optionsSelection}
        onInputChange={handleOptionsSearch}
        onChange={handleOptionsChange}
        onMenuScrollToBottom={handleBottomScroll}
        formatOptionLabel={(data) =>
          formatOptionLabel ? (
            formatOptionLabel(data)
          ) : (
            <div className="d-flex align-items-center">
              <div>
                <div className="fw-semibold">{data.label}</div>
                <div className="text-muted small">{data.data.description}</div>
                <div className="text-muted small">
                  {data.data.products?.length || 0} products • $
                  {data.data.price}
                  {data.data.discountPercentage && (
                    <span className="text-success ms-1">
                      ({data.data.discountPercentage}% off)
                    </span>
                  )}
                </div>
              </div>
            </div>
          )
        }
      />
    </div>
  );
};

export default LazyBundleSelect;
