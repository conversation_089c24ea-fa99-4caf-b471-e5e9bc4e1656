import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, Card } from "react-bootstrap";
import { useLazyGetReferredCustomersCountQuery } from "../../../../services/affiliation/affiliate";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import { ErrorType } from "../../../../utils/error_type";
import { getAllErrorMessages } from "../../../../utils/errors";

interface AffiliateAchievedKpiCardProps {
  affiliateId: string;
  cardHeight: number;
}

const AffiliateAchievedKpiCard: FC<AffiliateAchievedKpiCardProps> = ({ affiliateId, cardHeight }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [count, setCount] = useState<number>(0);

  const [getReferredCustomersCount] = useLazyGetReferredCustomersCountQuery();

  useEffect(() => {
    setIsLoading(true);
    setErr({});
    getReferredCustomersCount(affiliateId)
      .unwrap()
      .then(setCount)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card" style={{ height: `${cardHeight}px` }}>
        <Card.Header>
          <Card.Title>Referred Customers</Card.Title>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <div className="w-100 d-flex align-items-center justify-content-center" style={{ minHeight: "100%" }}>
            <h1 className="mb-0">{new Intl.NumberFormat('en-US').format(count)}</h1>
          </div>
        </Card.Body>
      </Card>
    </Fragment>
  );
}

export default AffiliateAchievedKpiCard;