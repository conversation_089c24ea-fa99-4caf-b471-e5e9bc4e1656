import { FC } from "react";
import { Button } from "react-bootstrap";
import { useCalculateCommissionsMutation } from "../../../../services/affiliation/affiliate";
import { ErrorType } from "../../../../utils/error_type";
import { getAllErrorMessages } from "../../../../utils/errors";

interface CalculateCommissionsButtonProps {
  affiliateId: string;
  startDate?: string;
  endDate?: string;
  setIsLoading: (show: boolean) => void;
  setErr: (error: ErrorType) => void;
  onFinish: (success: boolean) => void;
}

const CalculateCommissionsButton: FC<CalculateCommissionsButtonProps> = ({ affiliateId, startDate, endDate, setIsLoading, setErr, onFinish }) => {
  const [calculateCommissions] = useCalculateCommissionsMutation();

  const handleOnClick = () => {
    setIsLoading(true);
    setErr({});
    calculateCommissions({
      affiliateId,
      startDate,
      endDate,
    })
      .unwrap()
      .then(() => {
        onFinish(true);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
        onFinish(false);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  return (
    <Button
      variant="primary m-2"
      onClick={handleOnClick}
    >
      <i className="bi bi-calculator-fill me-2"></i>Calculate Commissions
    </Button>
  );
}

export default CalculateCommissionsButton;