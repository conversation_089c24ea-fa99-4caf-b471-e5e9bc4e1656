import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Col, Row } from "react-bootstrap";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import { ErrorType } from "../../../../utils/error_type";
import { getAllErrorMessages } from "../../../../utils/errors";
import { useLazyGetNextAffiliateTierQuery } from "../../../../services/affiliation/affiliatie-tier";

interface AffiliateAchievedKpiCardProps {
  currentTierId: string;
  cardHeight: number;
}

const NextTierRequirementCard: FC<AffiliateAchievedKpiCardProps> = ({ currentTierId, cardHeight }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [nextTier, setNextTier] = useState<TAffiliateTier>();

  const [getNextTier] = useLazyGetNextAffiliateTierQuery();

  useEffect(() => {
    setIsLoading(true);
    setErr({});
    getNextTier(currentTierId)
      .unwrap()
      .then(setNextTier)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, []);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card" style={{ height: `${cardHeight}px` }}>
        <Card.Header>
          <Card.Title>Next Tier</Card.Title>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          {
            nextTier ?
              (
                <ul className="list-unstyled order-details-list">
                  {[
                    [
                      'Tier:',
                      `${nextTier.title}`,
                    ],
                    [
                      'Required Number of New Customers:',
                      `${new Intl.NumberFormat('en-US').format(nextTier.requiredNewCustomers)}`,
                    ],
                  ].map((field) => (
                    <Row className="mb-2">
                      <Col xl={7}>
                        <span className="fs-13 me-2 text-default fw-semibold">
                          {field[0]}
                        </span>
                      </Col>
                      <Col xl={5}>
                        <span className="fs-15 text-muted fw-bold">
                          {field[1]}
                        </span>
                      </Col>
                    </Row>
                  ))}
                </ul>
              ) :
              (
                <p>This affiliate has reached the maximum tier</p>
              )
          }

        </Card.Body>
      </Card>
    </Fragment>
  );
}

export default NextTierRequirementCard;