import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, Modal, Row, Table } from "react-bootstrap";
import { useParams } from "react-router-dom";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useLazyGetCommissionByIdQuery, useUpdateCommissionAmountMutation } from "../../../services/affiliation/commission";
import { getAllErrorMessages } from "../../../utils/errors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { TAffiliateCommission } from "../../../types/affiliation/affiliate-commission";
import { format } from "date-fns";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import CommissionSyncButton from "../../../components/buttons/commission-sync-button";
import CommissionApproveButton from "./commission-approve-button";
import CommissionRejectButton from "./commission-reject-button";
import formatNumber from "../../../utils/number-formatter";

interface CommissionDetailProps { }

interface ErrorType {
  messages?: string[];
}

const CommissionDetail: FC<CommissionDetailProps> = () => {
  const { id } = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [commission, setCommission] = useState<Partial<TAffiliateCommission>>({});
  const [adjustmentFormData, setAdjustmentFormData] = useState({
    adjustedAmount: 0,
    adjustedReason: ''
  })
  const [showAdjustmentModal, setShowAdjustmentModal] = useState<boolean>(false)

  const [updateCommissionAmount] = useUpdateCommissionAmountMutation()
  const [getCommissionById] = useLazyGetCommissionByIdQuery();

  const formattedCommissionStatus = (status: EApprovalStatus | undefined) => {
    if (!status) return (<span className="badge bg-warning-transparent">Unknown</span>);

    if (status === EApprovalStatus.PENDING)
      return (<span className="badge bg-warning-transparent">Pending</span>)
    else if (status === EApprovalStatus.ORDER_REFUNDED)
      return (<span className="badge bg-warning-transparent">Order Refunded</span>)
    else if (status === EApprovalStatus.AMOUNT_ADJUSTED)
      return (<span className="badge bg-warning-transparent">Adjusted</span>)
    else if (status === EApprovalStatus.APPROVED)
      return (<span className="badge bg-success-transparent">Approved</span>)
    else
      return (<span className="badge bg-danger-transparent">Rejected</span>)
  }

  const loadCommission = () => {
    if (!id) return;
    setIsLoading(true);

    getCommissionById(id)
      .unwrap()
      .then((res) => {
        setCommission(res);
        setAdjustmentFormData({
          adjustedAmount: res.adjustedAmount,
          adjustedReason: res.adjustedReason
        })
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => setIsLoading(false));
  }

  const adjustmentFieldChange = (e) => {
    const { name, value } = e.target;

    if (name === 'adjustedAmount' && (isNaN(value) || Number(value) < 0))
      return

    setAdjustmentFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const handleUpdateCommissionAmount = () => {
    if (!commission) return
    setIsLoading(true)

    updateCommissionAmount({
      id: commission.id,
      adjustedAmount: adjustmentFormData.adjustedAmount,
      adjustedReason: adjustmentFormData.adjustedReason
    })
      .unwrap()
      .then((res) => {
        setCommission({
          ...commission,
          finalAmount: res.finalAmount,
          adjustedAmount: res.adjustedAmount,
          adjustedReason: res.adjustedReason
        });
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      })
  }

  const onFinishedCommissionApproval = ({ success, updatedCommission }: { success: boolean; updatedCommission: TAffiliateCommission; }) => {
    if (success) {
      setCommission(updatedCommission);
      setAdjustmentFormData({
        adjustedAmount: updatedCommission.adjustedAmount,
        adjustedReason: updatedCommission.adjustedReason
      })
      setErr({});
    }
  }

  useEffect(() => {
    loadCommission();
  }, [id]);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Row>

        <Col xl={7}>
          <Card className="custom-card" style={{ height: '330px' }}>
            <Card.Header>
              <CardHeaderWithBack
                title='Commission Details'
                route=''
              />
              {formattedCommissionStatus(commission.status)}

              <div className="px-4 justify-content-end">
                <CommissionApproveButton
                  commission={commission}
                  setIsLoading={setIsLoading}
                  onFinished={onFinishedCommissionApproval}
                />

                <CommissionRejectButton
                  commission={commission}
                  setIsLoading={setIsLoading}
                  onFinished={onFinishedCommissionApproval}
                />
              </div>
            </Card.Header>

            <Card.Body>
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}

              <ul className="list-unstyled order-details-list">
                {[
                  [
                    'Affiliate:',
                    `${commission.affiliate?.user?.firstName} ${commission.affiliate?.user?.lastName}`,
                  ],
                  [
                    'Created At:',
                    commission.createdAt ? `${format(commission.createdAt, 'EEE, MMM dd, yyyy, hh:mm aa')}` : '',
                  ],
                  [
                    'AFF Code:',
                    `${commission.refCode?.code}`,
                  ],
                  [
                    'Total Commission Amount:',
                    `$${commission.finalAmount}`,
                  ],
                ].map((field) => (
                  <li key={Math.random()}>
                    <span className="me-2 text-default fw-semibold">
                      {field[0]}
                    </span>
                    <span className="fs-14 text-muted">
                      {field[1]}
                    </span>
                  </li>
                ))}
              </ul>
            </Card.Body>
          </Card>
        </Col>

        <Col xl={5}>
          <Card className="custom-card" style={{ height: '330px' }}>
            <Card.Header>
              <Card.Title>Order</Card.Title>
            </Card.Header>
            <Card.Body>
              <ul className="list-unstyled order-details-list">
                {[
                  [
                    'Name:',
                    `${commission.order?.name}`,
                  ],
                  [
                    'ID:',
                    `${commission.order?.id}`,
                  ],
                  [
                    'Shopify ID:',
                    `${commission.order?.shopifyId}`,
                  ],
                  [
                    'Financial Status:',
                    `${commission.order?.financialStatus}`,
                  ],
                  [
                    'Total Price:',
                    `$${commission.order?.totalPrice}`,
                  ],
                  [
                    'Total Discount:',
                    `$${commission.order?.totalDiscounts}`,
                  ],
                  [
                    'Customer:',
                    `${commission.order?.user?.firstName} ${commission.order?.user?.lastName}`,
                  ],
                ].map((field) => (
                  <li key={Math.random()}>
                    <span className="me-2 text-default fw-semibold">
                      {field[0]}
                    </span>
                    <span className="fs-14 text-muted">
                      {field[1]}
                    </span>
                  </li>
                ))}
              </ul>
            </Card.Body>
          </Card>
        </Col>

      </Row>

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Details</Card.Title>
          <div className="px-4 justify-content-end">
            <CommissionSyncButton
              commissionId={commission?.id || ''}
              setIsLoading={setIsLoading}
              loadData={loadCommission}
            />
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION) || (commission.status == EApprovalStatus.REJECTED && commission.commissionAmount === 0)}
              variant="warning"
              className="btn btn-warning ms-2"
              onClick={() => { setShowAdjustmentModal(true) }}
            >
              Adjust
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>&nbsp;</th>
                <th>&nbsp;</th>
                <th>A</th>
                <th>B</th>
                <th>C = A * B</th>
                <th>D</th>
                <th>E = C - D</th>
                <th>F</th>
                <th>G = E * F</th>
              </tr>
              <tr>
                <th>Item</th>
                <th>SKU</th>
                <th>Quantity</th>
                <th>Unit Price ($)</th>
                <th>Item Subtotal ($)</th>
                <th>Item Discount ($)</th>
                <th>Item Total After Discounted ($)</th>
                <th>Commission Rate</th>
                <th>Commission Amount ($)</th>
              </tr>
            </thead>
            <tbody>
              {
                commission.commissionDetails?.map((commissionDetail: TAffiliateCommissionDetail) => (
                  <Fragment key={commissionDetail.id}>
                    <tr>
                      <td>{commissionDetail.orderDetail?.title}</td>
                      <td>{commissionDetail.orderDetail?.sku}</td>
                      <td>{commissionDetail.orderDetail?.currentQuantity}</td>
                      <td>{formatNumber(commissionDetail.orderDetail?.price, 2)}</td>
                      <td>{formatNumber(commissionDetail.orderDetail ? (commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity) : 0.0, 2)}</td>
                      <td>{formatNumber(commissionDetail.orderDetail?.discount, 2)}</td>
                      <td>{formatNumber(commissionDetail.orderDetail ? (commissionDetail.orderDetail.price * commissionDetail.orderDetail.currentQuantity - commissionDetail.orderDetail?.discount) : 0.0, 2)}</td>
                      <td>{commissionDetail.commissionRate * 100}%</td>
                      <td>{formatNumber(commissionDetail.commissionAmount, 2)}</td>
                    </tr>
                  </Fragment>
                ))
              }
              <tr>
                <td colSpan={8}>Total</td>
                <td>{formatNumber(commission.commissionAmount ?? 0, 2)}</td>
              </tr>
              {
                (commission.adjustedAmount !== null && commission.adjustedAmount !== undefined && commission.adjustedAmount > 0) &&
                (<tr>
                  <td colSpan={8}>Adjusted Total (Reason: {commission.adjustedReason}) </td>
                  <td>{formatNumber(commission.adjustedAmount, 2)}</td>
                </tr>
                )
              }
            </tbody>
          </Table>
        </Card.Body>
      </Card>

      <AdjustmentDialog
        show={showAdjustmentModal}
        setShow={setShowAdjustmentModal}
        formData={adjustmentFormData}
        onFieldChange={adjustmentFieldChange}
        handleConfirm={handleUpdateCommissionAmount}
      />
    </Fragment>
  );
}

const AdjustmentDialog = ({
  show,
  setShow,
  formData,
  onFieldChange,
  handleConfirm
}) => {
  const onCancel = () => {
    setShow(false)
  }

  const onSubmit = (e) => {
    e.preventDefault()

    setShow(false)
    handleConfirm()
  }

  return (
    <Fragment>
      <Modal show={show} onHide={onCancel}>
        <Form onSubmit={onSubmit}>

          <Modal.Header closeButton>
            <Modal.Title>Adjust Commission Amount</Modal.Title>
          </Modal.Header>

          <Modal.Body>
            <Form.Group className="mb-4">
              <Form.Label>New Commission Amount</Form.Label>
              <Form.Control
                type="text"
                name="adjustedAmount"
                value={formData.adjustedAmount}
                onChange={onFieldChange}
              />
            </Form.Group>

            <Form.Group className="mb-4">
              <Form.Label>Adjusted Reason</Form.Label>
              <Form.Control
                type="text"
                name="adjustedReason"
                value={formData.adjustedReason}
                onChange={onFieldChange}
              />
            </Form.Group>
          </Modal.Body>

          <Modal.Footer>
            <Button variant="secondary" onClick={onCancel}>
              Cancel
            </Button>
            <Button variant="primary" type="submit">
              Confirm
            </Button>
          </Modal.Footer>

        </Form>
      </Modal>
    </Fragment>
  )
}

export default CommissionDetail;