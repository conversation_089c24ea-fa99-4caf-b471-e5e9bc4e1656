import { But<PERSON> } from "react-bootstrap";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import Swal from "sweetalert2";
import { useUpdateCommissionStatusMutation } from "../../../services/affiliation/commission";
import { getAllErrorMessages } from "../../../utils/errors";

export default function CommissionApproveButton({ commission, setIsLoading, onFinished }) {
  if (!commission) return null;

  const [updateCommissionStatus] = useUpdateCommissionStatusMutation();

  const handleApprovingCommission = () => {
    if (!commission) return;

    Swal.fire({
      title: "Are you sure?",
      html: `<p>Approve commission amount $${commission.finalAmount}?</p><p>An email will be sent to this affiliate</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, approve!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          updateCommissionStatus({ id: commission.id, status: EApprovalStatus.APPROVED })
            .unwrap()
            .then((updatedCommission) => {
              Swal.fire("Approved!",
                `Commission amount $${commission.finalAmount} has been approved.`,
                "success"
              );
              onFinished({ success: true, updatedCommission });
            })
            .catch((error) => {
              Swal.fire("Error!", error.message.body ?? "Something went wrong!", "error");
              onFinished({ success: false });
            })
            .finally(() => {
              setIsLoading(false);
              onFinished({ success: false });
            })
        }
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during commission approval confirmation:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinished({ success: false });
      });
  }

  return (
    <Button
      hidden={!hasPermission(ACTION.APPROVE, RESOURCE.AFFILIATION) || commission.status == EApprovalStatus.APPROVED || parseFloat(commission.finalAmount ?? '0') === 0}
      variant="success"
      className="btn btn-success btn-sm ms-2"
      onClick={handleApprovingCommission}
    >
      Approve
    </Button>
  );
}