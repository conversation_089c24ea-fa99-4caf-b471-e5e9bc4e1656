import { Button } from "react-bootstrap";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import Swal from "sweetalert2";
import { useUpdateCommissionStatusMutation } from "../../../services/affiliation/commission";
import { getAllErrorMessages } from "../../../utils/errors";
import { Fragment, useState } from "react";
import CommissionRejectionDialog from "../../../components/dialog/commission-rejection-dialog";

export default function CommissionRejectButton({ commission, setIsLoading, onFinished }) {
  if (!commission) return null;

  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [updateCommissionStatus] = useUpdateCommissionStatusMutation();

  const handleRejectingCommission = (rejectReason: string) => {
    if (!commission) return;
    setIsLoading(true);

    updateCommissionStatus({ id: commission.id, status: EApprovalStatus.REJECTED, rejectReason })
      .unwrap()
      .then((updatedCommission) => {
        onFinished({ success: true, updatedCommission });
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinished({ success: false });
      })
      .finally(() => {
        setIsLoading(false);
      })
  }

  return (
    <Fragment>
      <Button
        hidden={!hasPermission(ACTION.REJECT, RESOURCE.AFFILIATION) || commission.status == EApprovalStatus.REJECTED || parseFloat(commission.finalAmount ?? '0') === 0}
        variant="danger"
        className="btn btn-danger btn-sm ms-2"
        onClick={() => { setShowConfirmModal(true) }}
      >
        Reject
      </Button>

      <CommissionRejectionDialog
        show={showConfirmModal}
        setShow={setShowConfirmModal}
        handleConfirm={handleRejectingCommission}
      />
    </Fragment>
  );
}