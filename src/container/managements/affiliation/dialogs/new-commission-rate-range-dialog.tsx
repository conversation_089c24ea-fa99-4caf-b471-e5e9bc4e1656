import { FC, useState } from "react";
import { <PERSON><PERSON>, Button, Form, Modal } from "react-bootstrap";
import { useAddCommissionRateMutation } from "../../../../services/affiliation/affiliatie-tier-commission-rate";
import { ErrorType } from "../../../../utils/error_type";
import { getAllErrorMessages } from "../../../../utils/errors";

interface NewCommissionRateRangeDialogProps {
  isShow: boolean;
  commissionGroupId: string,
  setIsLoading: (isLoading: boolean) => void;
  onHide: (success: boolean) => void;
}

interface FormDataError {
  revenueFrom: string;
  commissionRate: string;
}

const NewCommissionRateRangeDialog: FC<NewCommissionRateRangeDialogProps> = ({ isShow, commissionGroupId, setIsLoading, onHide }) => {
  const [err, setErr] = useState<ErrorType>();
  const [formData, setFormData] = useState<Partial<TAffiliateTierCommissionRate>>({
    revenueFrom: 0,
    commissionRate: 0
  });
  const [formDataError, setFormDataError] = useState<FormDataError>({
    revenueFrom: '',
    commissionRate: ''
  });

  const [addCommissionRate] = useAddCommissionRateMutation();

  const handleFormChange = (e) => {
    e.preventDefault();
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  }

  const validate = () => {
    const newFormDataError = {
      revenueFrom: '',
      commissionRate: ''
    };

    if (formData.revenueFrom === null || formData.revenueFrom === undefined) {
      newFormDataError.revenueFrom = 'This field is required';
    } else if (isNaN(formData.revenueFrom)) {
      newFormDataError.revenueFrom = 'Please enter a number';
    } else if (formData.revenueFrom < 0) {
      newFormDataError.revenueFrom = 'Negative number is not accepted';
    }

    if (formData.commissionRate === null || formData.commissionRate === undefined) {
      newFormDataError.commissionRate = 'This field is required';
    } else if (isNaN(formData.commissionRate)) {
      newFormDataError.commissionRate = 'Please enter a number';
    } else if (formData.commissionRate < 0) {
      newFormDataError.commissionRate = 'Negative number is not accepted';
    } else if (formData.commissionRate > 100) {
      newFormDataError.commissionRate = 'Maximum accepted value is 100';
    }

    setFormDataError(newFormDataError);
    return (newFormDataError.revenueFrom.length === 0 && newFormDataError.commissionRate.length === 0);
  }

  const handleConfirm = (e) => {
    e.preventDefault();
    if (!validate()) return;

    const data = {
      commissionGroupId,
      data: {
        revenueFrom: (formData.revenueFrom ?? 0),
        commissionRate: (formData.commissionRate ?? 0) / 100,
      }
    }

    setIsLoading(true);
    setErr({})
    addCommissionRate(data)
      .unwrap()
      .then(() => {
        onHide(true);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      }).finally(() => {
        setIsLoading(false);
      });
  }

  return (
    <Modal show={isShow} onHide={() => onHide(false)}>
      <Form onSubmit={handleConfirm}>
        <Modal.Header closeButton>
          <Modal.Title>New Commission Rate Range</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Form.Group className="mb-4">
            <Form.Label>Start Revenue</Form.Label>
            <Form.Control
              type="text"
              name="revenueFrom"
              value={formData.revenueFrom}
              onChange={handleFormChange}
              isInvalid={formDataError.revenueFrom.length > 0}
            />
            <Form.Control.Feedback type="invalid">
              {formDataError.revenueFrom}
            </Form.Control.Feedback>
          </Form.Group>

          <Form.Group className="mb-4">
            <Form.Label>Commission Rate</Form.Label>
            <Form.Control
              type="text"
              name="commissionRate"
              value={formData.commissionRate}
              onChange={handleFormChange}
              isInvalid={formDataError.commissionRate.length > 0}
            />
            <Form.Control.Feedback type="invalid">
              {formDataError.commissionRate}
            </Form.Control.Feedback>
          </Form.Group>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default NewCommissionRateRangeDialog;