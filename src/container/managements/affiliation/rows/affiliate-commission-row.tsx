import { FC, Fragment, useEffect, useState } from "react";
import { But<PERSON> } from "react-bootstrap";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import { TAffiliateCommission } from "../../../../types/affiliation/affiliate-commission";
import { EApprovalStatus } from "../../../../components/enums/approval-status-enum";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import CommissionApproveButton from "../commission-approve-button";
import CommissionRejectButton from "../commission-reject-button";

interface AffiliateCommissionRowProps {
  inputCommission: TAffiliateCommission,
  shouldShowAffiliateDetail: boolean,
  setErr: any
}

const AffiliateCommissionRow: FC<AffiliateCommissionRowProps> = ({ inputCommission, shouldShowAffiliateDetail, setErr }) => {
  const [commission, setCommission] = useState(inputCommission);
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    setCommission(inputCommission);
  }, [inputCommission]);

  const affiliateName = () => {
    if (!shouldShowAffiliateDetail || !commission.affiliate || !commission.affiliate.user) return;
    return (<td>{`${commission.affiliate.user.firstName} ${commission.affiliate.user.lastName}`}</td>);
  }

  const onFinishedApproveCommission = ({ success, updatedCommission }: { success: boolean; updatedCommission: TAffiliateCommission; }) => {
    if (success) {
      setCommission(updatedCommission);
      setErr({});
    }
  }

  const formattedCommissionStatus = (status: EApprovalStatus | undefined) => {
    if (!status) return (<span className="badge bg-warning-transparent">Unknown</span>);

    if (status === EApprovalStatus.PENDING)
      return (<span className="badge bg-warning-transparent">Pending</span>)
    else if (status === EApprovalStatus.ORDER_REFUNDED)
      return (<span className="badge bg-warning-transparent">Order Refunded</span>)
    else if (status === EApprovalStatus.AMOUNT_ADJUSTED)
      return (<span className="badge bg-warning-transparent">Adjusted</span>)
    else if (status === EApprovalStatus.APPROVED)
      return (<span className="badge bg-success-transparent">Approved</span>)
    else
      return (<span className="badge bg-danger-transparent">Rejected</span>)
  }

  return (
    <Fragment key={commission.id}>
      {isLoading && <LoadingOverlay />}

      <tr>
        {affiliateName()}
        <td>{format(commission.createdAt, 'EEE, MMM dd, yyyy, hh:mm aa')}</td>
        <td>{commission.finalAmount}</td>
        <td style={{ textAlign: "center" }}>{formattedCommissionStatus(commission.status)}</td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <Button
            hidden={!hasPermission(ACTION.READ, RESOURCE.AFFILIATION)}
            variant="primary-light"
            className="btn btn-primary-light btn-sm ms-2"
            onClick={() => { navigate(`/commission-details/${commission.id}`); }}
          >
            View
          </Button>
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <CommissionApproveButton
            commission={commission}
            setIsLoading={setIsLoading}
            onFinished={onFinishedApproveCommission}
          />
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <CommissionRejectButton
            commission={commission}
            setIsLoading={setIsLoading}
            onFinished={onFinishedApproveCommission}
          />
        </td>
      </tr>
    </Fragment>
  );
}

export default AffiliateCommissionRow;