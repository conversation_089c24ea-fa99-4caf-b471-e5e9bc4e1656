import React, { FC, Fragment, useEffect, useState } from "react"
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import { <PERSON><PERSON>, <PERSON>ton, Card, Col, Form, Row } from "react-bootstrap";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import Card<PERSON>eader<PERSON>ithBack from "../../../../components/table-title/card-header-with-back";
import { useNavigate, useParams } from "react-router-dom";
import { getAllErrorMessages } from "../../../../utils/errors";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import { useLazyGetAffiliateTierByIdQuery, useCreateAffiliateTierMutation, useUpdateAffiliateTierMutation, useDeleteAffiliateTierByIdMutation } from "../../../../services/affiliation/affiliatie-tier";
import CommissionConfigCard from "./commission-config-card";

interface AffiliateTierDetailProps { }

interface ErrorType {
  messages?: string[];
}

const AffiliateTierDetail: FC<AffiliateTierDetailProps> = () => {
  const { id } = useParams();

  const [isLoading, setIsLoading] = useState(false);
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [err, setErr] = useState<ErrorType>({});
  const [tier, setTier] = useState<TAffiliateTier>();
  const [detailsFormData, setDetailsFormData] = useState<Partial<TAffiliateTier>>({});

  const [getTierById] = useLazyGetAffiliateTierByIdQuery();
  const [createTier] = useCreateAffiliateTierMutation();
  const [updateTier] = useUpdateAffiliateTierMutation();
  const [deleteTier] = useDeleteAffiliateTierByIdMutation();

  const navigate = useNavigate();

  useEffect(() => {
    setIsAdd(false);
    setIsEdit(false);

    if (id === "new") {
      setIsAdd(true);
    } else {
      setIsEdit(true);
      loadTier();
    }
  }, [id]);

  const loadTier = () => {
    if (!id) return;
    setIsLoading(true);
    setErr({});

    getTierById({ id: id })
      .unwrap()
      .then((res) => {
        prepareReceivedData(res);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => setIsLoading(false));
  };

  const prepareReceivedData = (data: TAffiliateTier) => {
    const details: Partial<TAffiliateTier> = {
      ...data,
    };

    setDetailsFormData(details);
    setTier(data);
  };

  const handleAddFormSubmit = async () => {
    setIsLoading(true)
    try {
      await createTier({
        tier: detailsFormData.tier || 0,
        title: detailsFormData.title || "",
        requiredSoldItem: detailsFormData.requiredSoldItem || 0,
        requiredGmv: detailsFormData.requiredGmv || 0,
        requiredRefUsed: detailsFormData.requiredRefUsed || 0,
        requiredNewCustomers: detailsFormData.requiredNewCustomers || 0,
        defaultCommission: detailsFormData.defaultCommission || 0,
        defaultDiscount: detailsFormData.defaultDiscount || 0
      }).unwrap();

      navigate('/managements-affiliate-tiers');
    } catch (error) {
      setErr(getAllErrorMessages(error));
    } finally {
      setIsLoading(false);
    }
  }

  const handleUpdateFormSubmit = async () => {
    if (!id) return;

    setIsLoading(true);
    try {
      await updateTier({
        id,
        tier: detailsFormData.tier || 0,
        title: detailsFormData.title || "",
        requiredSoldItem: detailsFormData.requiredSoldItem || 0,
        requiredGmv: detailsFormData.requiredGmv || 0,
        requiredRefUsed: detailsFormData.requiredRefUsed || 0,
        requiredNewCustomers: detailsFormData.requiredNewCustomers || 0,
        defaultCommission: detailsFormData.defaultCommission || 0,
        defaultDiscount: detailsFormData.defaultDiscount || 0
      }).unwrap();

      navigate('/managements-affiliate-tiers');
    } catch (error) {
      setErr(getAllErrorMessages(error));
    } finally {
      setIsLoading(false);
    }
  }

  const handleDeleteClick = () => {
    if (!id) return;

    deleteSweetAlert({
      id,
      deleteAction: deleteTier,
      prepareAction: () => setIsLoading(true),
      finishAction: () => navigate("/managements-affiliate-tiers"),
      finalAction: () => setIsLoading(false),
    });
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}


      <Form>

        <Card>
          <Card.Header>
            <CardHeaderWithBack
              title={`${isAdd ? "Add" : "Edit"} Tier`}
              route=''
            />
          </Card.Header>
          <Card.Body>
            {err?.messages?.map((message: string, index: number) => (
              <Alert key={index} variant="danger">
                {message}
              </Alert>
            ))}
          </Card.Body>
        </Card>

        <Row>
          <Col xl={5}>
            <Card>
              <Card.Body>
                <Row>
                  <Col xl={4}>
                    <Form.Group className="mb-4">
                      <Form.Label>Tier Number</Form.Label>
                      <Form.Control
                        type="text"
                        value={detailsFormData.tier || 0}
                        onChange={(e) => {
                          if (!Number.isNaN(e.target.value)) {
                            setDetailsFormData((prev) => ({
                              ...prev,
                              tier: parseInt(e.target.value),
                            }))
                          }
                        }}
                      />
                    </Form.Group>
                  </Col>
                  <Col xl={8}>
                    <Form.Group className="mb-4">
                      <Form.Label>Title</Form.Label>
                      <Form.Control
                        type="text"
                        value={detailsFormData.title || ""}
                        onChange={(e) =>
                          setDetailsFormData((prev) => ({
                            ...prev,
                            title: e.target.value,
                          }))
                        }
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-4">
                  <Form.Label>Required Number of New Customers</Form.Label>
                  <Form.Control
                    type="text"
                    value={detailsFormData.requiredNewCustomers || 0}
                    onChange={(e) => {
                      if (!Number.isNaN(e.target.value)) {
                        setDetailsFormData((prev) => ({
                          ...prev,
                          requiredNewCustomers: parseInt(e.target.value),
                        }))
                      }
                    }}
                  />
                </Form.Group>
              </Card.Body>
            </Card>

            <Card>
              <Card.Body>

                <Form.Group className="mb-4">
                  <Row xl={12}>
                    <Col xl={isAdd ? 12 : 7}>
                      <Form.Label>Discount Rate (%)</Form.Label>
                      <Form.Control
                        type="number"
                        value={(detailsFormData.defaultDiscount || 0) * 100}
                        onChange={(e) =>
                          setDetailsFormData((prev) => ({
                            ...prev,
                            defaultDiscount: parseFloat(e.target.value) / 100,
                          }))
                        }
                      />
                    </Col>

                    {isEdit && (
                      <Col xl={5}>
                        <br />
                        <div className="d-flex justify-content-end">
                          <Button
                            hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
                            variant="primary-light"
                            onClick={() => navigate("discount-products")}
                            style={{ width: '260px' }}
                          >
                            Set Discounted Products <span className="ri-edit-line fs-14" />
                          </Button>
                        </div>
                      </Col>
                    )}
                  </Row>
                </Form.Group>
              </Card.Body>
            </Card>
          </Col>

          <Col xl={7}>
            {
              (isEdit && tier) && <CommissionConfigCard
                tier={tier}
                setIsLoading={setIsLoading}
              />
            }
          </Col>
        </Row>

        <Card>
          <div className="px-4 py-3 d-sm-flex justify-content-end">
            {isAdd ? (
              <Button
                hidden={!hasPermission(ACTION.CREATE, RESOURCE.AFFILIATION)}
                variant="primary-light"
                onClick={handleAddFormSubmit}
              >
                Create<i className="bi bi-plus-lg ms-2"></i>
              </Button>
            ) : isEdit ? (
              <div>
                <Button
                  hidden={!hasPermission(ACTION.DELETE, RESOURCE.AFFILIATION)}
                  className="mx-2"
                  variant="danger-light"
                  onClick={handleDeleteClick}
                >
                  Delete<i className="bi bi-trash ms-2"></i>
                </Button>
                <Button
                  hidden={!hasPermission(ACTION.UPDATE, RESOURCE.AFFILIATION)}
                  variant="success-light"
                  onClick={handleUpdateFormSubmit}
                >
                  Save<i className="bi bi-download ms-2"></i>
                </Button>
              </div>
            ) : null}
          </div>
        </Card>

      </Form>
    </Fragment>
  );
};

export default AffiliateTierDetail;