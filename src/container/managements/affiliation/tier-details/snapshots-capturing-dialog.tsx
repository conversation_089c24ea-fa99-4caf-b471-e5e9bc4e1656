import { FC, useState } from "react";
import { Button, Col, Form, InputGroup, Modal, Row } from "react-bootstrap";
import { useUpdateSnapshotsMutation } from "../../../../services/affiliation/affiliate";
import { ErrorType } from "../../../../utils/error_type";
import { getAllErrorMessages } from "../../../../utils/errors";
import ReactDatePicker from "react-datepicker";
import { format } from "date-fns";

interface SnapshotsCapturingDialogProps {
  affiliateId: string;
  isShow: boolean;
  setIsLoading: (isLoading: boolean) => void;
  setErr: (error: ErrorType) => void;
  onHide: (success: boolean) => void;
}

const SnapshotsCapturingDialog: FC<SnapshotsCapturingDialogProps> = ({ affiliateId, isShow, setIsLoading, setErr, onHide }) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());

  const [updateSnapshots] = useUpdateSnapshotsMutation();

  const handleDateChanged = (date: Date) => {
    setSelectedDate(date);
  }

  const handleCaptureSnapshots = (e) => {
    if (e) e.preventDefault();

    setIsLoading(true);
    setErr({});
    updateSnapshots({
      affiliateId,
      date: format(selectedDate, 'yyyy-MM-dd')
    })
      .unwrap()
      .then((res) => {
        console.log('snapshots:', res);
        onHide(true);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
        onHide(false);
      })
      .finally(() => setIsLoading(false));
  };

  return (
    <Modal show={isShow} onHide={() => { onHide(false) }}>
      <Form onSubmit={handleCaptureSnapshots}>
        <Modal.Header closeButton>
          <Modal.Title>Capture Snapshots</Modal.Title>
        </Modal.Header>

        <Modal.Body>
          <Row>
            <Col>
              <Form.Label>Select the month to capture</Form.Label>
            </Col>
          </Row>

          <Row>
            <Col>
              <InputGroup className="input-group">
                <InputGroup.Text className="input-group-text text-muted">
                  {" "}
                  <i className="ri-time-line"></i>{" "}
                </InputGroup.Text>
                <ReactDatePicker
                  selected={selectedDate}
                  onChange={handleDateChanged}
                  dateFormat="MM/yyyy"
                  showMonthYearPicker
                />
              </InputGroup>
            </Col>
          </Row>
        </Modal.Body>

        <Modal.Footer>
          <Button variant="primary" type='submit'>OK</Button>
        </Modal.Footer>
      </Form>
    </Modal>
  );
}

export default SnapshotsCapturingDialog;