import React, { FC, Fragment, useEffect, useState, useMemo } from "react";
import {
  Button,
  Card,
  Col,
  Form,
  Row,
  Table,
  InputGroup,
} from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useLazyGetUserCartQuery } from "../../../services/cart";
import { useLazySelectUserQuery } from "../../../services/user";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import Swal from "sweetalert2";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import { debounce } from "lodash";

interface ManagementCartCreateProps {}

const ManagementCartCreate: FC<ManagementCartCreateProps> = () => {
  const navigate = useNavigate();
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [searchTermFormData, setSearchTermFormData] = useState<string>("");
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isDebouncing, setIsDebouncing] = useState(false);
  const [users, setUsers] = useState<any[]>([]);

  const [getUserCart] = useLazyGetUserCartQuery();
  const [selectUser] = useLazySelectUserQuery();

  // Debounced search function with proper cleanup
  const debouncedSearch = useMemo(
    () =>
      debounce((searchTerm: string) => {
        setIsDebouncing(false);
        if (searchTerm.trim()) {
          setPage(1);
          loadData(1, searchTerm);
        } else {
          loadData(1, "");
        }
      }, 300), // Reduced from 500ms to 300ms for better responsiveness
    []
  );

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const loadData = async (currentPage: number, term: string) => {
    if (currentPage === 1 && term === "") {
      setIsInitialLoading(true);
    } else {
      setIsSearching(true);
    }
    try {
      const result: any = await selectUser({
        page: currentPage,
        search: term,
      }).unwrap();
      setUsers(result.data || []);
      setTotalPages(result?.meta?.lastPage || 1);
    } catch (error) {
      console.error("Failed to search users:", error);
      setUsers([]);
    } finally {
      setIsSearching(false);
      setIsInitialLoading(false);
    }
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    loadData(1, searchTermFormData);
  };

  const handleClearSearch = () => {
    setSearchTermFormData("");
    setPage(1);
    loadData(1, "");
  };

  // Load initial data when component mounts
  useEffect(() => {
    loadData(1, "");
  }, []);

  const handleCreateCart = async (userId?: string) => {
    const targetUserId = userId || selectedUserId;

    if (!targetUserId) {
      await Swal.fire({
        title: "Error!",
        text: "Please select a user first.",
        icon: "error",
      });
      return;
    }

    if (!hasPermission(ACTION.CREATE, RESOURCE.CART)) {
      await Swal.fire({
        title: "Error!",
        text: "You don't have permission to create carts.",
        icon: "error",
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await getUserCart(targetUserId).unwrap();

      // Navigate directly to cart details page without showing success popup
      navigate(`/managements-carts/${result.id}`);
    } catch (error) {
      console.error("Failed to create/get cart:", error);
      await Swal.fire({
        title: "Error!",
        text: "Failed to create or get cart. Please try again.",
        icon: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Create Cart"
                route="/managements-carts"
              />
            </Card.Header>
            <Card.Body>
              <div className="app-container">
                <Form onSubmit={handleSearchSubmit} className="mb-3">
                  <InputGroup>
                    <Form.Control
                      type="text"
                      placeholder="Search by name or email..."
                      value={searchTermFormData}
                      onChange={(e) => {
                        setSearchTermFormData(e.target.value);
                        setIsDebouncing(true);
                        debouncedSearch(e.target.value);
                      }}
                    />
                    <Button
                      variant="light"
                      className="btn btn-light btn-sm"
                      onClick={handleClearSearch}
                      type="button"
                    >
                      X
                    </Button>
                    <Button variant="primary" type="submit">
                      Search
                    </Button>
                  </InputGroup>

                  {isDebouncing && (
                    <div className="mt-2">
                      <small className="text-muted">
                        <i className="bx bx-time me-1"></i>
                        Typing... Search will trigger in a moment
                      </small>
                    </div>
                  )}
                </Form>

                {isInitialLoading && (
                  <div className="text-center mb-3">
                    <div
                      className="spinner-border spinner-border-sm"
                      role="status"
                    >
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    <span className="ms-2">Loading users...</span>
                  </div>
                )}

                {users.length === 0 && !isSearching && !isInitialLoading && (
                  <div className="text-center mb-3">
                    <p className="text-muted">
                      No users found. Try searching for a user by name or email.
                    </p>
                  </div>
                )}

                {isSearching && !isInitialLoading && (
                  <div className="text-center mb-3">
                    <div
                      className="spinner-border spinner-border-sm"
                      role="status"
                    >
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    <span className="ms-2">Searching users...</span>
                  </div>
                )}

                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Avatar</th>
                      <th>Email</th>
                      <th>Name</th>
                      <th>Status</th>
                      <th>Phone</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.map((user: any) => (
                      <tr key={user.id}>
                        <td style={{ textAlign: "center", padding: "10px" }}>
                          <p className="avatar avatar-xxl my-auto">
                            {(user.avatarUrl || user.avatarId) && (
                              <img
                                src={
                                  user.avatarUrl || user.avatarMedia?.url || ""
                                }
                                style={{
                                  display: "block",
                                  margin: "0 auto",
                                  maxWidth: "100%",
                                  height: "100%",
                                  objectFit: "cover",
                                }}
                              />
                            )}
                          </p>
                        </td>
                        <td>{user.email}</td>
                        <td>{`${user.firstName || ""} ${
                          user.lastName || ""
                        }`}</td>
                        <td>
                          {user.active ? (
                            <span className="badge bg-success-transparent rounded-pill">
                              Active
                            </span>
                          ) : (
                            <span className="badge bg-danger-transparent rounded-pill">
                              Deactive
                            </span>
                          )}
                        </td>
                        <td>{user.phone}</td>
                        <td className="text-center">
                          <Button
                            variant="primary-light"
                            onClick={() => {
                              setSelectedUserId(user.id);
                              handleCreateCart(user.id);
                            }}
                          >
                            Select
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>

                <PaginationBar
                  page={page}
                  setPage={(newPage) => {
                    setPage(newPage);
                    loadData(newPage, searchTermFormData);
                  }}
                  lastPage={totalPages}
                />

                <div className="d-flex justify-content-end mt-3">
                  <Button
                    variant="outline-secondary"
                    onClick={() => navigate("/managements-carts")}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ManagementCartCreate;
