import React, { FC, Fragment, useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Card,
  Col,
  Form,
  Row,
  Table,
  Modal,
  Badge,
  Alert,
} from "react-bootstrap";
import LazyVariantSelect from "../../../components/lazy-select/lazy-variant-select";
import {
  useAddProductToCartMutation,
  useRemoveCartSectionMutation,
  useUpdateCartSectionQuantityMutation,
} from "../../../services/cart";
import {
  TCart,
  TCartSection,
  TCartItem,
  TAddProductToCartRequest,
} from "../../../types/cart";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import Swal from "sweetalert2";
import moment from "moment";
import AddProductToCartModal from "./components/AddProductToCartModal";

interface CartItemManagementProps {
  cart: TCart;
  onCartUpdated: (updatedCart: TCart) => void;
  onRefreshCart?: () => void;
}

const CartItemManagement: FC<CartItemManagementProps> = ({
  cart,
  onCartUpdated,
  onRefreshCart,
}) => {
  const [showAddProductModal, setShowAddProductModal] = useState(false);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);

  const [addProductToCart, { isLoading: isAddingProduct }] =
    useAddProductToCartMutation();
  const [removeCartSection, { isLoading: isRemovingSection }] =
    useRemoveCartSectionMutation();
  const [updateCartSectionQuantity, { isLoading: isUpdatingSectionQuantity }] =
    useUpdateCartSectionQuantityMutation();

  const [updatingSectionId, setUpdatingSectionId] = useState<string | null>(
    null
  );

  useEffect(() => {
    if (cart) {
      const recalculatedCart = recalculateCartTotals(cart);
      if (
        recalculatedCart.itemCount !== cart.itemCount ||
        recalculatedCart.totalValue !== cart.totalValue ||
        recalculatedCart.cartSections.length !== cart.cartSections.length
      ) {
        onCartUpdated(recalculatedCart);
      }
    }
  }, [cart]);

  const recalculateCartTotals = (updatedCart: TCart): TCart => {
    if (!updatedCart || !updatedCart.cartSections) {
      return updatedCart;
    }

    const validSections = updatedCart.cartSections.filter(
      (section: TCartSection) =>
        section &&
        section.cartItems &&
        Array.isArray(section.cartItems) &&
        section.cartItems.length > 0
    );

    const totalItems = validSections.reduce(
      (sum: number, section: TCartSection) => {
        if (!section || typeof section.quantity !== "number") {
          return sum;
        }
        return sum + section.quantity;
      },
      0
    );

    const totalValue = validSections.reduce(
      (sum: number, section: TCartSection) => {
        if (
          !section ||
          !section.cartItems ||
          !Array.isArray(section.cartItems)
        ) {
          return sum;
        }
        const sectionTotal = section.cartItems.reduce(
          (sectionSum: number, item: TCartItem) => {
            if (!item) {
              return sectionSum;
            }
            const price = parseFloat(item.price) || 0;
            return sum + price * section.quantity;
          },
          0
        );

        return sum + sectionTotal;
      },
      0
    );

    return {
      ...updatedCart,
      itemCount: totalItems,
      totalValue: totalValue,
      cartSections: validSections.map((section: TCartSection) => {
        if (!section.cartItems || !Array.isArray(section.cartItems)) {
          return {
            ...section,
            total: "0.00",
            quantity: 0,
          };
        }

        const sectionTotal = section.cartItems.reduce(
          (sum: number, item: TCartItem) => {
            if (!item) {
              return sum;
            }
            const price = parseFloat(item.price) || 0;
            return sum + price * section.quantity;
          },
          0
        );

        const sectionQuantity = section.quantity || 0;

        return {
          ...section,
          total: sectionTotal.toFixed(2),
          quantity: sectionQuantity,
        };
      }),
    };
  };

  const handleAddProduct = async () => {
    if (!selectedVariant) {
      await Swal.fire({
        title: "Error!",
        text: "Please select a product variant.",
        icon: "error",
      });
      return;
    }

    if (!hasPermission(ACTION.CREATE, RESOURCE.CART)) {
      await Swal.fire({
        title: "Error!",
        text: "You don't have permission to add items to cart.",
        icon: "error",
      });
      return;
    }

    try {
      const requestData: TAddProductToCartRequest = {
        variantId: selectedVariant.id,
        quantity: 1,
        notes: "Admin added product",
      };

      const response = await addProductToCart({
        cartId: cart.id,
        data: requestData,
      }).unwrap();

      if (response && (response as any).success) {
        setShowAddProductModal(false);
        setSelectedVariant(null);

        const newSection = (response as any).data;

        const updatedCart = {
          ...cart,
          cartSections: Array.isArray(cart.cartSections)
            ? [...cart.cartSections]
            : [],
        };

        const existingSectionIndex = updatedCart.cartSections.findIndex(
          (section) => section.id === newSection.id
        );

        if (existingSectionIndex >= 0) {
          updatedCart.cartSections[existingSectionIndex] = { ...newSection };
        } else {
          updatedCart.cartSections.push({ ...newSection });
        }

        const recalculatedCart = recalculateCartTotals(updatedCart);
        onCartUpdated(recalculatedCart);
      } else {
        throw new Error("Add product failed");
      }
    } catch (error) {
      await Swal.fire({
        title: "Error!",
        text: "Failed to add product to cart. Please try again.",
        icon: "error",
      });
    }
  };

  const handleSectionQuantityChange = async (
    section: TCartSection,
    newQuantity: number
  ) => {
    if (!hasPermission(ACTION.UPDATE, RESOURCE.CART)) {
      return;
    }

    if (newQuantity < 0) return;

    try {
      setUpdatingSectionId(section.id);
      const response = await updateCartSectionQuantity({
        request: {
          cartSectionId: section.id,
          quantity: newQuantity,
          notes: "Admin updated section quantity",
        },
      }).unwrap();

      if (response && (response as any).success && (response as any).data) {
        const updatedSection = (response as any).data;

        const updatedCart = {
          ...cart,
          cartSections: Array.isArray(cart.cartSections)
            ? [...cart.cartSections]
            : [],
        };

        const sectionIndex = updatedCart.cartSections.findIndex(
          (s) => s.id === updatedSection.id
        );

        if (sectionIndex >= 0) {
          if (newQuantity === 0) {
            updatedCart.cartSections.splice(sectionIndex, 1);
          } else {
            updatedCart.cartSections[sectionIndex] = {
              ...updatedSection,
            };
          }

          const recalculatedCart = recalculateCartTotals(updatedCart);
          onCartUpdated(recalculatedCart);
        } else {
          throw new Error("Cart section not found");
        }
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      console.error("Failed to update section quantity:", error);
      await Swal.fire({
        title: "Error!",
        text: "Failed to update section quantity. Please try again.",
        icon: "error",
      });
    } finally {
      setUpdatingSectionId(null);
    }
  };

  const handleRemoveSection = async (section: TCartSection) => {
    if (!hasPermission(ACTION.DELETE, RESOURCE.CART)) {
      return;
    }

    try {
      const result = await Swal.fire({
        title: "Are you sure?",
        text: "Are you sure you want to delete this section? This action cannot be undone.",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
      });

      if (!result.isConfirmed) {
        return;
      }

      const response = await removeCartSection({
        cartSectionId: section.id,
        data: { notes: "Admin removed section" },
      }).unwrap();

      if (response && response.message) {
        const updatedCart = {
          ...cart,
          cartSections: Array.isArray(cart.cartSections)
            ? [...cart.cartSections]
            : [],
        };
        updatedCart.cartSections = updatedCart.cartSections.filter(
          (cartSection) => cartSection.id !== section.id
        );

        const recalculatedCart = recalculateCartTotals(updatedCart);
        onCartUpdated(recalculatedCart);
      } else {
        throw new Error("Invalid response from server");
      }
    } catch (error) {
      await Swal.fire({
        title: "Error!",
        text: "Failed to remove cart section. Please try again.",
        icon: "error",
      });
    }
  };

  const openAddProductModal = () => {
    setShowAddProductModal(true);
  };

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY HH:mm:ss");
  };

  return (
    <Fragment>
      <Row className="mb-3">
        <Col>
          <div className="d-flex gap-2">
            <Button
              variant="primary"
              onClick={() => openAddProductModal()}
              disabled={
                !hasPermission(ACTION.CREATE, RESOURCE.CART) ||
                isAddingProduct ||
                isRemovingSection ||
                !!updatingSectionId
              }
            >
              {isAddingProduct ? (
                <>
                  <div
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                  >
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  Adding...
                </>
              ) : (
                <>
                  <i className="bx bx-plus me-1"></i>
                  Add Product
                </>
              )}
            </Button>
          </div>
        </Col>
      </Row>

      {cart.cartSections && cart.cartSections.length > 0 ? (
        cart.cartSections.map((section: TCartSection) => (
          <Card key={section.id} className="mb-3 border">
            <Card.Header>
              <div className="d-flex justify-content-between align-items-center mb-3">
                <div>
                  <h6 className="mb-0">
                    {section.bundleId ? (
                      <i className="bx bx-package me-2"></i>
                    ) : (
                      <i className="bx bx-box me-2"></i>
                    )}
                    {section.title ||
                      `${
                        section.section.charAt(0).toUpperCase() +
                        section.section.slice(1)
                      } Items`}
                  </h6>
                  {section.bundleId && (
                    <small className="text-muted">
                      Bundle ID: {section.bundleId}
                    </small>
                  )}
                  <small className="text-muted d-block">
                    <i className="bx bx-package me-1"></i>
                    {section.cartItems.length} product(s) in this section
                    {section.cartItems.length > 1 && (
                      <span className="text-success ms-2">
                        <i className="bx bx-check-circle"></i> Multi-product
                        section
                      </span>
                    )}
                  </small>
                </div>
              </div>

              <div className="d-flex justify-content-between align-items-center p-3 bg-light rounded">
                <div className="d-flex align-items-center">
                  <span className="fw-semibold me-3">
                    <i className="bx bx-hash me-1"></i>
                    Quantity:
                  </span>
                  <div className="d-flex align-items-center gap-2">
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      onClick={() =>
                        handleSectionQuantityChange(
                          section,
                          section.quantity - 1
                        )
                      }
                      disabled={
                        section.quantity <= 1 ||
                        !hasPermission(ACTION.UPDATE, RESOURCE.CART) ||
                        isRemovingSection ||
                        updatingSectionId === section.id ||
                        isUpdatingSectionQuantity
                      }
                      title="Decrease section quantity"
                    >
                      {updatingSectionId === section.id ? (
                        <div
                          className="spinner-border spinner-border-sm"
                          role="status"
                        >
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      ) : (
                        <i className="bx bx-minus"></i>
                      )}
                    </Button>
                    <Badge
                      bg="primary"
                      className="px-3"
                      style={{
                        height: 32,
                        display: "inline-flex",
                        alignItems: "center",
                        justifyContent: "center",
                        fontSize: "1rem",
                        borderRadius: "0.5rem",
                      }}
                    >
                      {section.quantity}
                    </Badge>
                    <Button
                      variant="outline-secondary"
                      size="sm"
                      onClick={() =>
                        handleSectionQuantityChange(
                          section,
                          section.quantity + 1
                        )
                      }
                      disabled={
                        !hasPermission(ACTION.UPDATE, RESOURCE.CART) ||
                        isRemovingSection ||
                        updatingSectionId === section.id ||
                        isUpdatingSectionQuantity
                      }
                      title="Increase section quantity"
                    >
                      {updatingSectionId === section.id ? (
                        <div
                          className="spinner-border spinner-border-sm"
                          role="status"
                        >
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      ) : (
                        <i className="bx bx-plus"></i>
                      )}
                    </Button>
                  </div>
                </div>
                <div className="ms-3">
                  <Button
                    variant="outline-danger"
                    size="sm"
                    onClick={() => handleRemoveSection(section)}
                    disabled={
                      !hasPermission(ACTION.DELETE, RESOURCE.CART) ||
                      isRemovingSection ||
                      !!updatingSectionId
                    }
                    title="Delete this section"
                  >
                    {isRemovingSection ? (
                      <div
                        className="spinner-border spinner-border-sm"
                        role="status"
                      >
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    ) : (
                      <>
                        <i className="bx bx-trash me-1"></i>
                        Delete Section
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </Card.Header>
            <Card.Body>
              <Table className="table table-bordered text-nowrap border-bottom">
                <thead className="table-dark">
                  <tr>
                    <th>
                      <i className="bx bx-image me-1"></i>Image
                    </th>
                    <th>
                      <i className="bx bx-box me-1"></i>Product
                    </th>
                    <th>
                      <i className="bx bx-tag me-1"></i>Variant
                    </th>
                    <th>
                      <i className="bx bx-barcode me-1"></i>SKU
                    </th>
                    <th className="text-center">
                      <i className="bx bx-dollar me-1"></i>Unit Price
                    </th>
                    <th className="text-center">
                      <i className="bx bx-calculator me-1"></i>Item Total
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {section.cartItems.map((item: TCartItem) => (
                    <tr key={item.id}>
                      <td className="text-center">
                        <span className="avatar avatar-lg bd-gray-200">
                          {item.image ? (
                            <img src={item.image} alt="" />
                          ) : (
                            <i className="bx bx-image fs-4"></i>
                          )}
                        </span>
                      </td>
                      <td>{item.productName}</td>
                      <td>{item.variantName}</td>
                      <td>{item.sku}</td>
                      <td className="text-center">
                        ${parseFloat(item.price).toFixed(2)}
                      </td>
                      <td className="text-center">
                        $
                        {(parseFloat(item.price) * section.quantity).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>

              {section.lastModifiedByAdminId && (
                <Alert variant="info" className="mt-3">
                  <small>
                    <strong>Last Admin Action:</strong>{" "}
                    {section.lastAdminAction} |<strong>Modified:</strong>{" "}
                    {section.lastAdminModifiedAt
                      ? formatDate(section.lastAdminModifiedAt)
                      : "N/A"}
                    {section.lastAdminModificationNotes && (
                      <>
                        {" "}
                        | <strong>Notes:</strong>{" "}
                        {section.lastAdminModificationNotes}
                      </>
                    )}
                  </small>
                </Alert>
              )}
            </Card.Body>
          </Card>
        ))
      ) : (
        <Card className="border">
          <Card.Body className="text-center text-muted">
            <i className="bx bx-cart bx-lg mb-2"></i>
            <div>This cart is empty</div>
          </Card.Body>
        </Card>
      )}

      <AddProductToCartModal
        isOpen={showAddProductModal}
        onClose={() => setShowAddProductModal(false)}
        cartId={cart.id}
        onProductAdded={() => {
          setShowAddProductModal(false);
          if (onRefreshCart) {
            onRefreshCart();
          }
        }}
      />

      <Modal
        show={false}
        onHide={() => setShowAddProductModal(false)}
        size="lg"
      >
        <Modal.Header closeButton>
          <Modal.Title>Add Product to Cart (Legacy)</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Select Product Variant</Form.Label>
                  <LazyVariantSelect
                    selectionFunctionQuery={{ availableForSale: true }}
                    getSelectedOptions={(value) => setSelectedVariant(value)}
                    formatOptionLabel={(value) => (
                      <div className="d-flex align-items-center">
                        <span className="avatar avatar-sm bd-gray-200 me-2">
                          <img src={value.value.image?.src} alt="" />
                        </span>
                        <div>
                          <div className="fw-semibold">
                            {value.value.product?.title}
                          </div>
                          <div className="text-muted small">
                            {value.value.title}
                          </div>
                          <div className="text-muted small">
                            SKU: {value.value.sku}
                          </div>
                        </div>
                        <div className="ms-auto">
                          <Badge bg="success">${value.value.price}</Badge>
                        </div>
                      </div>
                    )}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="secondary"
            onClick={() => setShowAddProductModal(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleAddProduct}
            disabled={
              !selectedVariant || isAddingProduct || !!updatingSectionId
            }
          >
            {isAddingProduct ? (
              <>
                <div
                  className="spinner-border spinner-border-sm me-2"
                  role="status"
                >
                  <span className="visually-hidden">Loading...</span>
                </div>
                Adding...
              </>
            ) : (
              "Add Product"
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Fragment>
  );
};

export default CartItemManagement;
