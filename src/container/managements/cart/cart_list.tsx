import React, { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, Col, Form, Row, Table } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import {
  useLazyListCartsQuery,
  useDeleteCartMutation,
} from "../../../services/cart";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { debounce } from "lodash";
import moment from "moment";
import { TCart } from "../../../types/cart";
import Swal from "sweetalert2";

interface ManagementCartProps {}

const ManagementCart: FC<ManagementCartProps> = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20);
  const [search, setSearch] = useState("");
  const setDebouncedSearch = debounce((value) => setSearch(value), 500);

  const [isLoading, setIsLoading] = useState(false);

  const [trigger] = useLazyListCartsQuery();
  const [deleteCart] = useDeleteCartMutation();

  const [carts, setCarts] = useState<TCart[]>([]);
  const [deletingCartId, setDeletingCartId] = useState<string | null>(null);
  const [viewingCartId, setViewingCartId] = useState<string | null>(null);

  useEffect(() => {
    setIsLoading(true);
    trigger({ page, limit, search })
      .unwrap()
      .then((res: any) => {
        setCarts(res.data || []);
        setLastPage(res?.meta?.lastPage);
        setTotal(res?.meta?.total);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page, limit, search]);

  const formatDate = (dateString: string) => {
    return moment(dateString).format("MMM DD, YYYY HH:mm");
  };

  const getCustomerName = (cart: TCart) => {
    if (!cart.user) return "N/A";

    const { firstName, lastName, fullname } = cart.user;
    if (fullname && fullname.trim()) {
      return fullname;
    }
    return `${firstName} ${lastName}`.trim() || "N/A";
  };

  const handleDeleteCart = async (cartId: string) => {
    if (!hasPermission(ACTION.DELETE, RESOURCE.CART)) {
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "Are you sure you want to delete this cart? This action cannot be undone.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      setDeletingCartId(cartId);
      try {
        await deleteCart(cartId).unwrap();

        await Swal.fire(
          "Deleted!",
          "Cart has been deleted successfully.",
          "success"
        );

        const refreshResult = await trigger({ page, limit, search });
        if (refreshResult.data) {
          setCarts(refreshResult.data.data || []);
          setLastPage(refreshResult.data?.meta?.lastPage);
          setTotal(refreshResult.data?.meta?.total);
        }
      } catch (error) {
        await Swal.fire(
          "Error!",
          "Failed to delete cart. Please try again.",
          "error"
        );
      } finally {
        setDeletingCartId(null);
      }
    }
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <div className="d-flex justify-content-between align-items-center">
                <CardHeaderWithBack title="Cart Management" route="" />
                <div className="px-4 justify-content-end">
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.CART)}
                    variant="primary-light"
                    onClick={() => navigate("/managements-carts/create")}
                  >
                    Create Cart<i className="bi bi-plus-lg ms-2"></i>
                  </Button>
                </div>
              </div>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <Form.Group className="mb-3">
                  <Form.Control
                    type="search"
                    placeholder="Search by user email or name..."
                    onChange={(e) => setDebouncedSearch(e.target.value)}
                  />
                </Form.Group>

                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th className="text-center">Customer</th>
                      <th className="text-center">Items</th>
                      <th className="text-center">Total Value</th>
                      <th className="text-center">Last Activity</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {carts.map((cart: TCart) => (
                      <tr key={cart.id}>
                        <td>
                          {cart.user ? (
                            <div className="d-flex align-items-center">
                              <div className="me-2">
                                {cart.user.avatarUrl || cart.user.avatarId ? (
                                  <div className="avatar avatar-sm rounded-circle">
                                    <img
                                      src={cart.user.avatarUrl || ""}
                                      style={{
                                        display: "block",
                                        width: "100%",
                                        height: "100%",
                                        objectFit: "cover",
                                      }}
                                      alt={`${getCustomerName(cart)} avatar`}
                                    />
                                  </div>
                                ) : (
                                  <div className="avatar avatar-sm bg-primary rounded-circle">
                                    <span className="avatar-initials">
                                      {getCustomerName(cart)
                                        .charAt(0)
                                        .toUpperCase()}
                                    </span>
                                  </div>
                                )}
                              </div>
                              <div>
                                <div className="fw-semibold">
                                  {getCustomerName(cart)}
                                </div>
                                <div className="text-muted small">
                                  {cart.user.email || "N/A"}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="text-muted">User not available</div>
                          )}
                        </td>
                        <td className="text-center">
                          <div className="d-flex align-items-center justify-content-center">
                            <div>
                              <div className="text-muted">
                                {cart.cartSections.length} sections
                              </div>
                              <div className="text-muted">
                                {cart.itemCount} products
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="text-center">
                          <div className="fw-semibold text-success">
                            $ {(cart.totalValue || 0).toFixed(2)}
                          </div>
                        </td>
                        <td className="text-center">
                          <div className="text-muted">
                            {formatDate(cart.lastActivity)}
                          </div>
                        </td>
                        <td className="text-center">
                          <div className="btn-group">
                            {hasPermission(ACTION.READ, RESOURCE.CART) && (
                              <Button
                                variant="outline-primary"
                                size="sm"
                                disabled={viewingCartId === cart.id}
                                onClick={() => {
                                  if (cart.id) {
                                    setViewingCartId(cart.id);
                                    navigate(`/managements-carts/${cart.id}`);
                                  }
                                }}
                              >
                                {viewingCartId === cart.id ? (
                                  <>
                                    <div
                                      className="spinner-border spinner-border-sm me-1"
                                      role="status"
                                    >
                                      <span className="visually-hidden">
                                        Loading...
                                      </span>
                                    </div>
                                    Loading...
                                  </>
                                ) : (
                                  <>
                                    <i className="bx bx-show me-1"></i>
                                    View
                                  </>
                                )}
                              </Button>
                            )}
                            {hasPermission(ACTION.DELETE, RESOURCE.CART) && (
                              <Button
                                variant="outline-danger"
                                size="sm"
                                onClick={() => handleDeleteCart(cart.id)}
                                disabled={deletingCartId === cart.id}
                              >
                                <i className="bx bx-trash me-1"></i>
                                {deletingCartId === cart.id
                                  ? "Deleting..."
                                  : "Delete"}
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>

                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={lastPage}
                  total={total}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ManagementCart;
