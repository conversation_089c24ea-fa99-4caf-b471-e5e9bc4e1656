/* Add Product To Cart Modal Styles */

.product-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border: 1px solid #e9ecef;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.product-card .card-img-top {
  transition: opacity 0.3s ease;
}

.product-card:hover .card-img-top {
  opacity: 0.9;
}

.product-card .card-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #495057;
  line-height: 1.3;
  min-height: 2.6rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Larger font size for product names in categories and collections tabs */
.tab-pane[data-rr-ui-event-key="categories"] .product-card .card-title,
.tab-pane[data-rr-ui-event-key="collections"] .product-card .card-title,
.categories-products .product-card .card-title,
.collections-products .product-card .card-title {
  font-size: 1.1rem;
  font-weight: 600;
  min-height: 3rem;
}

.product-card .card-body {
  padding: 1rem;
}

.product-card .form-select {
  font-size: 0.85rem;
}

.product-card .btn {
  font-size: 0.85rem;
  font-weight: 500;
  border-radius: 6px;
}

/* Tab Navigation */
.nav-pills .nav-link {
  border-radius: 8px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s ease;
}

.nav-pills .nav-link:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.nav-pills .nav-link.active {
  background-color: #0d6efd;
  color: white;
}

.nav-pills .nav-link i {
  font-size: 1.1rem;
}

/* Category and Collection Lists */
.category-item,
.collection-item {
  transition: all 0.2s ease;
  border-radius: 6px;
  margin-bottom: 2px;
}

.category-item:hover,
.collection-item:hover {
  background-color: #f8f9fa !important;
  color: #495057 !important;
}

.category-item.active,
.collection-item.active {
  background-color: #0d6efd !important;
  color: white !important;
}

.category-item.active:hover,
.collection-item.active:hover {
  background-color: #0b5ed7 !important;
}

/* Search Input */
.search-input-group .form-control {
  border-radius: 8px 0 0 8px;
  border-right: none;
}

.search-input-group .input-group-text {
  border-radius: 0;
  border-left: none;
  border-right: none;
  background-color: #f8f9fa;
  color: #6c757d;
}

.search-input-group .btn {
  border-radius: 0 8px 8px 0;
}

/* Loading States */
.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.loading-spinner .spinner-border {
  width: 2rem;
  height: 2rem;
  margin-bottom: 1rem;
}

/* Empty States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  color: #6c757d;
  text-align: center;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state .empty-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.empty-state .empty-description {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Product Grid */
.products-grid {
  gap: 1rem;
}

.products-grid .col {
  margin-bottom: 1rem;
}

/* Modal Customizations */
.add-product-modal .modal-dialog {
  max-width: 90vw;
}

.add-product-modal .modal-body {
  padding: 1.5rem;
}

.add-product-modal .tab-content {
  min-height: 500px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .add-product-modal .modal-dialog {
    max-width: 95vw;
    margin: 0.5rem;
  }

  .product-card .card-title {
    font-size: 0.85rem;
    min-height: 2.2rem;
  }

  .nav-pills {
    flex-direction: column;
    gap: 0.5rem;
  }

  .nav-pills .nav-link {
    text-align: center;
    padding: 0.75rem 1rem;
  }

  .products-grid .col {
    margin-bottom: 0.75rem;
  }
}

@media (max-width: 576px) {
  .add-product-modal .modal-body {
    padding: 1rem;
  }

  .product-card .card-body {
    padding: 0.75rem;
  }

  .product-card .card-title {
    font-size: 0.8rem;
    min-height: 2rem;
  }
}

/* Badge Styles */
.product-card .badge {
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

/* Form Controls */
.product-card .form-label {
  font-size: 0.8rem;
  font-weight: 500;
  color: #495057;
  margin-bottom: 0.25rem;
}

.product-card .form-text {
  font-size: 0.75rem;
}

/* Scrollbar Styling */
.modal-body::-webkit-scrollbar,
.card-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track,
.card-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb,
.card-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover,
.card-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation for product cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-card {
  animation: fadeInUp 0.3s ease-out;
}

/* Focus states */
.category-item:focus,
.collection-item:focus {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

.product-card:focus-within {
  outline: 2px solid #0d6efd;
  outline-offset: 2px;
}

/* Utility classes */
.cursor-pointer {
  cursor: pointer;
}

.text-truncate-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.text-truncate-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
