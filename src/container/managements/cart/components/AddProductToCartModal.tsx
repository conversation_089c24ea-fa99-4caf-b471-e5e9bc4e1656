import React, { FC, useState, useEffect, useMemo } from "react";
import {
  Modal,
  Button,
  Form,
  Row,
  Col,
  Card,
  Badge,
  Nav,
  Tab,
  InputGroup,
  Spinner,
  Alert,
} from "react-bootstrap";
import { debounce } from "lodash";
import "./AddProductToCartModal.css";
import { useLazyListProductQuery } from "../../../../services/product";
import { useLazyListCollectionProductQuery } from "../../../../services/collection";
import {
  useLazyGetCategoryProductsQuery,
  useLazyListShopCategoriesQuery,
  useLazyListShopCollectionsQuery,
} from "../../../../services/app-product";
import { useAddProductToCartMutation } from "../../../../services/cart";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import Swal from "sweetalert2";

interface AddProductToCartModalProps {
  isOpen: boolean;
  onClose: () => void;
  cartId: string;
  onProductAdded: () => void;
}

interface ProductCardProps {
  product: TProduct;
  onAddToCart: (product: TProduct, variant: any, quantity: number) => void;
}

const ProductCard: FC<ProductCardProps> = ({ product, onAddToCart }) => {
  const [selectedVariant, setSelectedVariant] = useState<any>(
    product.variants?.[0] || null
  );

  useEffect(() => {
    if (product.variants?.length > 0) {
      setSelectedVariant(product.variants[0]);
    }
  }, [product]);

  const handleAddToCart = () => {
    if (selectedVariant) {
      onAddToCart(product, selectedVariant, 1);
    }
  };

  return (
    <Card className="h-100 product-card">
      <div className="position-relative">
        {product.image?.src ? (
          <Card.Img
            variant="top"
            src={product.image.src}
            alt={product.title}
            style={{ height: "200px", objectFit: "cover" }}
          />
        ) : (
          <div
            className="d-flex align-items-center justify-content-center bg-light"
            style={{ height: "200px" }}
          >
            <i className="bx bx-image fs-1 text-muted"></i>
          </div>
        )}
        <Badge
          bg={product.status === "active" ? "success" : "secondary"}
          className="position-absolute top-0 end-0 m-2"
        >
          {product.status}
        </Badge>
      </div>
      <Card.Body className="d-flex flex-column">
        <Card.Title className="fs-5 mb-2" title={product.title}>
          {product.title.length > 50
            ? `${product.title.substring(0, 50)}...`
            : product.title}
        </Card.Title>

        {product.variants && product.variants.length > 1 && (
          <Form.Group className="mb-2">
            <Form.Label className="small">Variant:</Form.Label>
            <Form.Select
              size="sm"
              value={selectedVariant?.id || ""}
              onChange={(e) => {
                const variant = product.variants?.find(
                  (v) => v.id === e.target.value
                );
                setSelectedVariant(variant || null);
              }}
            >
              {product.variants.map((variant) => (
                <option key={variant.id} value={variant.id}>
                  {variant.title} - ${variant.price}
                </option>
              ))}
            </Form.Select>
          </Form.Group>
        )}

        {selectedVariant && (
          <div className="mb-2">
            <Badge bg="primary" className="me-2">
              ${selectedVariant.price}
            </Badge>
            {selectedVariant.sku && (
              <small className="text-muted">SKU: {selectedVariant.sku}</small>
            )}
          </div>
        )}

        <Button
          variant={selectedVariant?.availableForSale ? "primary" : "secondary"}
          size="sm"
          className="mt-auto"
          onClick={handleAddToCart}
          disabled={!selectedVariant?.availableForSale}
        >
          {selectedVariant?.availableForSale ? "Add to Cart" : "Out of Stock"}
        </Button>
      </Card.Body>
    </Card>
  );
};

const AddProductToCartModal: FC<AddProductToCartModalProps> = ({
  isOpen,
  onClose,
  cartId,
  onProductAdded,
}) => {
  const [activeTab, setActiveTab] = useState<
    "search" | "categories" | "collections"
  >("search");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedCollection, setSelectedCollection] = useState<string | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [searchProducts, setSearchProducts] = useState<TProduct[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [collections, setCollections] = useState<any[]>([]);
  const [categoryProducts, setCategoryProducts] = useState<TProduct[]>([]);
  const [collectionProducts, setCollectionProducts] = useState<TProduct[]>([]);

  const [listProductsApi] = useLazyListProductQuery();
  const [listCategoriesApi] = useLazyListShopCategoriesQuery();
  const [listCollectionsApi] = useLazyListShopCollectionsQuery();
  const [getCategoryProductsApi] = useLazyGetCategoryProductsQuery();
  const [getCollectionProductsApi] = useLazyListCollectionProductQuery();
  const [addProductToCart] = useAddProductToCartMutation();

  const debouncedSearch = useMemo(
    () =>
      debounce(async (query: string) => {
        setLoading(true);
        setError(null);

        try {
          const response = await listProductsApi({
            search: query || undefined,
            page: 1,
            limit: query ? 20 : 50,
          }).unwrap();

          const products = response.data || [];
          setSearchProducts(products);
        } catch (err: any) {
          console.error("Search error:", err);
          const errorMessage =
            err.data?.message ||
            err.message ||
            "Search failed. Please try again.";
          setError(errorMessage);
          setSearchProducts([]);
        } finally {
          setLoading(false);
        }
      }, 500),
    [listProductsApi]
  );

  useEffect(() => {
    if (activeTab === "categories" && categories.length === 0) {
      loadCategories();
    }
  }, [activeTab]);

  useEffect(() => {
    if (activeTab === "collections" && collections.length === 0) {
      loadCollections();
    }
  }, [activeTab]);

  const loadCategories = async () => {
    try {
      const response = await listCategoriesApi({
        page: 1,
        limit: 100,
      }).unwrap();

      const categoriesData = response.data || [];
      setCategories(categoriesData);

      if (categoriesData.length > 0 && !selectedCategory) {
        const firstCategory = categoriesData[0];
        setSelectedCategory(firstCategory.id);
        loadCategoryProducts(firstCategory.id);
      }
    } catch (err: any) {
      console.error("Load categories error:", err);
      setError("Failed to load categories");
    }
  };

  const loadCollections = async () => {
    try {
      const response = await listCollectionsApi({
        page: 1,
        limit: 100,
      }).unwrap();

      const collectionsData = response.data || [];
      setCollections(collectionsData);

      if (collectionsData.length > 0 && !selectedCollection) {
        const firstCollection = collectionsData[0];
        setSelectedCollection(firstCollection.id);
        loadCollectionProducts(firstCollection.id);
      }
    } catch (err: any) {
      console.error("Load collections error:", err);
      setError("Failed to load collections");
    }
  };

  useEffect(() => {
    if (activeTab === "search") {
      debouncedSearch(searchQuery);
    }
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, activeTab, debouncedSearch]);

  useEffect(() => {
    if (activeTab === "search" && searchQuery === "") {
      debouncedSearch("");
    }
  }, [activeTab, searchQuery, debouncedSearch]);

  const loadCategoryProducts = async (categoryId: string) => {
    setLoading(true);
    try {
      const response = await getCategoryProductsApi({
        id: categoryId,
        page: 1,
        limit: 20,
      }).unwrap();
      setCategoryProducts(response.data || []);
    } catch (err) {
      console.error("Failed to load category products:", err);
      setCategoryProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const loadCollectionProducts = async (collectionId: string) => {
    setLoading(true);
    try {
      const response = await getCollectionProductsApi({
        page: 1,
        limit: 20,
        collectionIds: [collectionId],
      }).unwrap();

      const products = response.data || [];
      setCollectionProducts(products);
    } catch (err) {
      console.error("Failed to load collection products:", err);
      setCollectionProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    loadCategoryProducts(categoryId);
  };

  const handleCollectionSelect = (collectionId: string) => {
    setSelectedCollection(collectionId);
    loadCollectionProducts(collectionId);
  };

  const handleAddToCart = async (
    _product: TProduct,
    variant: any,
    quantity: number
  ) => {
    if (!hasPermission(ACTION.CREATE, RESOURCE.CART)) {
      Swal.fire(
        "Permission Denied",
        "You don't have permission to add products to cart.",
        "error"
      );
      return;
    }

    try {
      await addProductToCart({
        cartId,
        data: {
          variantId: variant.id,
          quantity,
          notes: "Admin added product via modal",
        },
      }).unwrap();

      onProductAdded();
    } catch (err: any) {
      console.error("Add to cart failed:", err);
      Swal.fire(
        "Error",
        err.data?.message || "Failed to add product to cart",
        "error"
      );
    }
  };

  const handleTabChange = (tab: string | null) => {
    if (!tab) return;

    setActiveTab(tab as "search" | "categories" | "collections");
    setError(null);
    if (tab === "search") {
      setSelectedCategory(null);
      setSelectedCollection(null);
    } else if (tab === "categories") {
      setSearchQuery("");
      setSelectedCollection(null);
    } else if (tab === "collections") {
      setSearchQuery("");
      setSelectedCategory(null);
    }
  };

  const renderProducts = () => {
    let products: TProduct[] = [];

    if (activeTab === "search") {
      products = searchProducts;
    } else if (activeTab === "categories") {
      products = categoryProducts;
    } else if (activeTab === "collections") {
      products = collectionProducts;
    }

    if (loading) {
      return (
        <div className="loading-spinner">
          <Spinner animation="border" role="status">
            <span className="visually-hidden">Loading...</span>
          </Spinner>
          <div>Loading products...</div>
        </div>
      );
    }

    if (products.length === 0) {
      let emptyMessage = "No products found";
      let emptyIcon = "bx-package";

      if (activeTab === "search" && searchQuery.length === 0) {
        emptyMessage = "Loading all products...";
        emptyIcon = "bx-search";
      } else if (
        activeTab === "search" &&
        searchQuery.length > 0 &&
        searchQuery.length < 2
      ) {
        emptyMessage = "Enter at least 2 characters to search";
        emptyIcon = "bx-search";
      } else if (activeTab === "search" && searchQuery.length >= 2) {
        emptyMessage = `No products found for "${searchQuery}"`;
        emptyIcon = "bx-search";
      } else if (activeTab === "categories" && selectedCategory) {
        emptyMessage = "No products in this category";
        emptyIcon = "bx-category";
      } else if (activeTab === "collections" && selectedCollection) {
        emptyMessage = "No products in this collection";
        emptyIcon = "bx-collection";
      }

      return (
        <div className="empty-state">
          <i className={`bx ${emptyIcon}`}></i>
          <div className="empty-title">{emptyMessage}</div>
          {activeTab === "search" && searchQuery.length >= 2 && (
            <div className="empty-description">
              Try adjusting your search terms or browse by categories
            </div>
          )}
        </div>
      );
    }

    return (
      <Row className="g-3">
        {products.map((product) => (
          <Col key={product.id} xs={12} sm={6} md={4} lg={3}>
            <ProductCard product={product} onAddToCart={handleAddToCart} />
          </Col>
        ))}
      </Row>
    );
  };

  return (
    <Modal
      show={isOpen}
      onHide={onClose}
      size="xl"
      centered
      className="add-product-modal"
    >
      <Modal.Header closeButton>
        <Modal.Title>
          <i className="bx bx-cart-add me-2"></i>
          Add Product to Cart
        </Modal.Title>
      </Modal.Header>
      <Modal.Body style={{ maxHeight: "70vh", overflowY: "auto" }}>
        {error && (
          <Alert variant="danger" dismissible onClose={() => setError(null)}>
            <i className="bx bx-error me-2"></i>
            {error}
          </Alert>
        )}

        <Tab.Container activeKey={activeTab} onSelect={handleTabChange}>
          <Nav variant="pills" className="mb-4 justify-content-center">
            <Nav.Item>
              <Nav.Link eventKey="search">
                <i className="bx bx-search me-2"></i>
                Search Products
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="categories">
                <i className="bx bx-category me-2"></i>
                Browse Categories
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="collections">
                <i className="bx bx-collection me-2"></i>
                Browse Collections
              </Nav.Link>
            </Nav.Item>
          </Nav>

          <Tab.Content>
            <Tab.Pane eventKey="search">
              <Form.Group className="mb-4">
                <InputGroup className="search-input-group">
                  <InputGroup.Text>
                    <i className="bx bx-search"></i>
                  </InputGroup.Text>
                  <Form.Control
                    type="text"
                    placeholder="Search products by name, SKU, or description... (leave empty to browse all products)"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="border-start-0"
                  />
                  {searchQuery && (
                    <Button
                      variant="outline-secondary"
                      onClick={() => setSearchQuery("")}
                      className="border-start-0"
                    >
                      <i className="bx bx-x"></i>
                    </Button>
                  )}
                </InputGroup>
                <Form.Text className="text-muted">
                  <i className="bx bx-info-circle me-1"></i>
                  Leave empty to browse all products, or enter text to search
                </Form.Text>
              </Form.Group>
              <div className="products-grid">{renderProducts()}</div>
            </Tab.Pane>

            <Tab.Pane eventKey="categories">
              <Row>
                <Col md={4}>
                  <Card className="h-100">
                    <Card.Header className="bg-light">
                      <h6 className="mb-0">
                        <i className="bx bx-category me-2"></i>
                        Categories
                      </h6>
                    </Card.Header>
                    <Card.Body
                      style={{ maxHeight: "500px", overflowY: "auto" }}
                    >
                      {categories.length === 0 ? (
                        <div className="empty-state">
                          <i className="bx bx-category"></i>
                          <div className="empty-title">No Categories</div>
                          <div className="empty-description">
                            No categories available
                          </div>
                        </div>
                      ) : (
                        categories.map((category) => (
                          <div
                            key={category.id}
                            className={`category-item p-3 border-bottom cursor-pointer ${
                              selectedCategory === category.id ? "active" : ""
                            }`}
                            onClick={() => handleCategorySelect(category.id)}
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                              if (e.key === "Enter" || e.key === " ") {
                                handleCategorySelect(category.id);
                              }
                            }}
                          >
                            <div className="fw-semibold">{category.name}</div>
                            <small
                              className={
                                selectedCategory === category.id
                                  ? "text-white-50"
                                  : "text-muted"
                              }
                            >
                              Level {category.level}
                            </small>
                          </div>
                        ))
                      )}
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={8}>
                  {selectedCategory ? (
                    <div className="products-grid categories-products">
                      {renderProducts()}
                    </div>
                  ) : (
                    <div className="empty-state">
                      <i className="bx bx-category"></i>
                      <div className="empty-title">Select a Category</div>
                      <div className="empty-description">
                        Choose a category from the list to view products
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
            </Tab.Pane>

            <Tab.Pane eventKey="collections">
              <Row>
                <Col md={4}>
                  <Card className="h-100">
                    <Card.Header className="bg-light">
                      <h6 className="mb-0">
                        <i className="bx bx-collection me-2"></i>
                        Collections
                      </h6>
                    </Card.Header>
                    <Card.Body
                      style={{ maxHeight: "500px", overflowY: "auto" }}
                    >
                      {collections.length === 0 ? (
                        <div className="empty-state">
                          <i className="bx bx-collection"></i>
                          <div className="empty-title">No Collections</div>
                          <div className="empty-description">
                            No collections available
                          </div>
                        </div>
                      ) : (
                        collections.map((collection) => (
                          <div
                            key={collection.id}
                            className={`collection-item p-3 border-bottom cursor-pointer ${
                              selectedCollection === collection.id
                                ? "active"
                                : ""
                            }`}
                            onClick={() =>
                              handleCollectionSelect(collection.id)
                            }
                            tabIndex={0}
                            role="button"
                            onKeyDown={(e) => {
                              if (e.key === "Enter" || e.key === " ") {
                                handleCollectionSelect(collection.id);
                              }
                            }}
                          >
                            <div className="fw-semibold">
                              {collection.title}
                            </div>
                            {collection.description && (
                              <small
                                className={
                                  selectedCollection === collection.id
                                    ? "text-white-50"
                                    : "text-muted"
                                }
                              >
                                {collection.description.length > 50
                                  ? `${collection.description.substring(
                                      0,
                                      50
                                    )}...`
                                  : collection.description}
                              </small>
                            )}
                          </div>
                        ))
                      )}
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={8}>
                  {selectedCollection ? (
                    <div className="products-grid collections-products">
                      {renderProducts()}
                    </div>
                  ) : (
                    <div className="empty-state">
                      <i className="bx bx-collection"></i>
                      <div className="empty-title">Select a Collection</div>
                      <div className="empty-description">
                        Choose a collection from the list to view products
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
            </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="secondary" onClick={onClose}>
          Close
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default AddProductToCartModal;
