import React, {<PERSON>} from "react";
import {<PERSON>} from "react-router-dom";

export interface ChatBubbleProps {
    message: TChatbotMessage
    annotation?: string
    onDoubleClick?: () => void
}

export const ChatBubble: FC<ChatBubbleProps> = ({ message,annotation, onDoubleClick }) => {
    const {
        role,
        content,
        userName = "",
        sentAt,
        avatarUrl = "",
        userId = ""
    } = message;

    const isUser = role === "user";
    const timestamp = sentAt
        ? new Date(sentAt).toLocaleTimeString([], {
            hour: "numeric",
            minute: "2-digit",
        })
        : "";

    const profilePath = `/managements-users/details/${userId}`

    return (
        <div
            className={`d-flex mb-2 ${
                isUser ? "justify-content-start gap-1" : "justify-content-end gap-1"
            }`}
            style = {{
                opacity: message.pending ? 0.5 : 1,
                filter: message.pending? "blur(0.5px)" : "none"
            }}
            onDoubleClick={onDoubleClick}
        >
            {isUser ? (
                <>
                    {avatarUrl && (
                        <img
                            src={avatarUrl}
                            alt={`${userName} avatar`}
                            className="rounded-circle"
                            style={{ width: 32, height: 32, objectFit: "cover" }}
                        />
                    )}
                    <div className="d-flex flex-column">
                        <div className="mb-1">
                            <Link
                                to={profilePath}
                                className="fw-bold text-decoration-none text-reset"
                            >
                                {userName}
                            </Link>
                            <span className="text-muted small ms-2">{timestamp}</span>
                        </div>
                        <span
                            className="d-inline-block px-3 py-2 rounded-3 bg-primary text-white"
                            style={{ maxWidth: "75%" }}
                        >
              {content}
            </span>
                    </div>
                </>
            ) : (
                <>
                    <div className="d-flex flex-column align-items-end">
                        <div className="mb-1">
                            <span className="text-muted small">{timestamp}</span>
                            <span className="fw-bold ms-2">{userName}</span>
                        </div>
                        <span
                            className={`d-inline-block px-3 py-2 rounded-3 ${
                                role === "bot" ? "bg-light" : "bg-secondary text-white"
                            } text-end`}
                            style={{ maxWidth: "75%" }}
                        >
                            {content}
                        </span>

                        {annotation && (
                            <small className="text-muted fst-italic d-block mt-1 ms-1">
                                {annotation}
                            </small>
                        )}

                    </div>
                    {avatarUrl && (
                        <img
                            src={avatarUrl}
                            alt={`${userName} avatar`}
                            className="rounded-circle"
                            style={{ width: 32, height: 32, objectFit: "cover" }}
                        />
                    )}
                </>
            )}
        </div>
    );
}
