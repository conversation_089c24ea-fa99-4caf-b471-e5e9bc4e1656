import React, { FC, Fragment, useMemo, useCallback, useRef, useEffect, useState } from "react"
import {
    Button,
    Form,
    InputGroup,
    ListGroup,
    Spinner
} from "react-bootstrap"
import { useListChatbotRoomsQuery } from "../../../services/chatbot/chatbot.ts"
import {groupByDate, truncateSentence} from "./utils.ts";


interface ChatRoomsListProps {
    selectedRoom: TChatBotRoom | null
    onRoomSelect: (room: TChatBotRoom) => void
    roomsPageSize?: number
}



const ChatRoomsList: FC<ChatRoomsListProps> = ({
    selectedRoom,
    onRoomSelect,
}) => {

    const roomsPageSize = 10
    const [currentPage, setCurrentPage] = useState(1)
    const [searchQuery, setSearchQuery] = useState("")
    const [allRooms, setAllRooms] = useState<TChatBotRoom[]>([])
    const [hasMorePages, setHasMorePages] = useState(true)
    const observerTarget = useRef<HTMLDivElement>(null)
    const scrollContainerRef = useRef<HTMLDivElement>(null)

    const {
        data: roomsData,
        isLoading: roomsLoading,
        isFetching,
        refetch: refetchRooms,
    } = useListChatbotRoomsQuery({
        page: currentPage,
        limit: roomsPageSize
    }, {
        skip: !hasMorePages && currentPage > 1
    })

    const handleRefresh = useCallback(() => {
        setAllRooms([])
        setCurrentPage(1)
        setHasMorePages(true)
        refetchRooms()
    }, [refetchRooms])

    useEffect(() => {
        if (roomsData?.data) {
            const totalPages = Number(roomsData.meta?.lastPage) || 1
            const newRooms = roomsData.data

            if (currentPage === 1) {
                setAllRooms(newRooms)
            } else {
                setAllRooms(prev => {
                    const existingIds = new Set(prev.map(room => room.id))
                    const uniqueNewRooms = newRooms.filter(room => !existingIds.has(room.id))
                    return [...prev, ...uniqueNewRooms]
                })
            }

            setHasMorePages(currentPage < totalPages)
        }
    }, [roomsData, currentPage])

    const loadMore = useCallback(() => {
        if (searchQuery.trim()) return
        if (!isFetching && hasMorePages) {
            setCurrentPage(prev => prev + 1)
        }
    }, [isFetching, hasMorePages, searchQuery])

    const filteredRooms = useMemo(() => {
        if (!searchQuery.trim()) return allRooms
        const query = searchQuery.toLowerCase()
        return allRooms.filter(room =>
            room.userName.toLowerCase().includes(query) ||
            room.name?.toLowerCase().includes(query)
        )
    }, [allRooms, searchQuery])

    useEffect(() => {

        if (searchQuery.trim()) return

        const observer = new IntersectionObserver(
            (entries) => {
                const [entry] = entries
                if (entry.isIntersecting && hasMorePages && !isFetching) {
                    loadMore()
                }
            },
            {
                threshold: 0.1,
                rootMargin: '100px'
            }
        )

        const target = observerTarget.current
        if (target) {
            observer.observe(target)
        }

        return () => {
            if (target) {
                observer.unobserve(target)
            }
        }
    }, [loadMore, hasMorePages, isFetching, searchQuery])

    const roomsByDate = useMemo(
        () => {
            const groupedRooms = groupByDate(
                filteredRooms,
                room => room.latestMessageAt
            )

            return Object
                .entries(groupedRooms)
                .map(([label, rooms]) => {
                    const sortedRooms = [...rooms].sort(
                        (a, b) =>
                            new Date(b.latestMessageAt).getTime() -
                            new Date(a.latestMessageAt).getTime()
                    )

                    return {
                        label,
                        rooms: sortedRooms,
                        timeStamp: new Date(sortedRooms[0].latestMessageAt).getTime(),
                    }
                })
                .sort((a, b) => b.timeStamp - a.timeStamp)
        },
        [filteredRooms]
    )


    const isInitialLoading = roomsLoading && currentPage === 1

    return (
        <div className="chat-info border flex-shrink-0 d-flex flex-column"
             style={{ width: "300px", minWidth: "300px", maxHeight: "100vh", overflow: "hidden" }}>

            {/* heading */}
            <div className="d-flex align-items-center justify-content-between w-100 p-3 border-bottom">
                <h5 className="fw-semibold mb-0">Messages</h5>

                <div className="d-flex align-items-center gap-1">
                    <Button
                        variant="outline-primary"
                        size="sm"
                        aria-label="Refresh"
                        disabled={isFetching}
                        onClick={handleRefresh}
                        className="d-flex align-items-center justify-content-center"
                        style={{width: 32}}
                    >
                        {isFetching ? (
                            <Spinner animation="border" size="sm"/>
                        ) : (
                            <i className="ti ti-refresh"/>
                        )}
                    </Button>
                </div>

            </div>

            {/* search box */}
            <div className="chat-search p-3 border-bottom">
                <InputGroup>
                    <Form.Control placeholder="Search by user..." value={searchQuery}
                                  onChange={e => setSearchQuery(e.target.value)}/>
                    <Button variant="primary">
                        <i className="ri-search-line"/>
                    </Button>
                </InputGroup>
            </div>

            {/* ROOMS LIST */}
            <div
                ref={scrollContainerRef}
                className="flex-grow-1 overflow-auto"
                style={{maxHeight: "100vh"}}
            >
                {isInitialLoading ? (
                    <div className="d-flex justify-content-center align-items-center p-4">
                        <Spinner animation="border" size="sm"/>
                        <span className="ms-2">Loading chat rooms...</span>
                    </div>
                ) : (
                    <ListGroup variant="flush" className="mb-0">
                        {roomsByDate.map(({label, rooms}) => (
                            <Fragment key={label}>
                                {/* date header */}
                                <ListGroup.Item
                                    className="bg-body-secondary text-uppercase fw-semibold small"
                                    style={{fontSize: "0.75rem"}}
                                >
                                    {label}
                                </ListGroup.Item>

                                {/* rooms under that date */}
                                {rooms.map((room) => (
                                    <ListGroup.Item
                                        key={room.id}
                                        action
                                        active={room.id === selectedRoom?.id}
                                        onClick={() => onRoomSelect(room)}
                                        className="d-flex align-items-start"
                                    >
                                        {/* name + time */}
                                        <div className="flex-fill">
                                            <p className="mb-0 fw-semibold">
                                                {room.userName}
                                                <span className="float-end text-muted fw-normal fs-11">
                                                    {new Date(room.latestMessageAt).toLocaleTimeString([], {
                                                        hour: "numeric",
                                                        minute: "2-digit",
                                                    })}
                                                </span>
                                            </p>
                                            <p className="fs-12 mb-0 text-truncate">…
                                                {truncateSentence(room.lastMessage ?? "", 5)}
                                            </p>
                                        </div>
                                    </ListGroup.Item>
                                ))}
                            </Fragment>
                        ))}

                        {/* Loading indicator for subsequent pages */}
                        {isFetching && currentPage > 1 && (
                            <ListGroup.Item className="text-center p-3">
                                <Spinner animation="border" size="sm"/>
                                <span className="ms-2">Loading more rooms...</span>
                            </ListGroup.Item>
                        )}

                        {/* Load More Button - moved to bottom */}
                        {hasMorePages && !isFetching && !searchQuery.trim() && allRooms.length > 0 && (
                            <ListGroup.Item className="text-center p-3">
                                <Button
                                    variant="outline-primary"
                                    size="sm"
                                    onClick={loadMore}
                                    className="d-flex align-items-center justify-content-center mx-auto"
                                >
                                    <i className="ti ti-plus me-1"/>
                                    Load more
                                </Button>
                            </ListGroup.Item>
                        )}

                        {/* Observer target for infinite scrolling */}
                        <div ref={observerTarget} style={{height: '1px'}}/>

                        {/* No rooms message */}
                        {!isInitialLoading && allRooms.length === 0 && (
                            <ListGroup.Item className="text-muted text-center">
                                No chat rooms found
                            </ListGroup.Item>
                        )}

                        {/* End of results indicator */}
                        {!hasMorePages && allRooms.length > 0 && !isFetching && (
                            <ListGroup.Item className="text-center text-muted small p-2">
                                No more rooms to load
                            </ListGroup.Item>
                        )}
                    </ListGroup>
                )}
            </div>
        </div>
    )
}

export default ChatRoomsList
