import {FC, Fragment, useEffect, useState} from "react";
import {useLazyGetChatbotRoomQuery} from "../../../services/chatbot/chatbot.ts";
import ChatbotRoom from "./chatbot_room.tsx";
import {Spinner} from "react-bootstrap";

const ChatWithBot: FC = () => {
    const [getOrCreateBotRoom] = useLazyGetChatbotRoomQuery()
    const [ roomId, setRoomId] = useState<string | null>(null)
    const [isLoading, setIsLoading] = useState(true)

    useEffect( () => {
        const fetchChatRoom = async () => {
            try {
                setIsLoading(true)
                const room = await getOrCreateBotRoom().unwrap()
                setRoomId(room.id)
            } catch (error) {
                console.error("Failed to get chat room:", error)
            } finally {
                setIsLoading(false)
            }
        }

        fetchChatRoom()
    }, [getOrCreateBotRoom])



    return (
        <div className="d-flex w-100" style={{height: "100vh",}}>
                <div className="flex-grow-1 main-chart-wrapper pt-0 p-2 gap-2 d-lg-flex" style={{minWidth: 0}}>
                    {isLoading && (
                        <div><Spinner/> Loading chat room</div>
                    )}
                    {!isLoading && !roomId && (
                        <div> Failed to load chat room</div>
                    )}

                    {!isLoading && roomId  &&
                        (
                            <Fragment>
                                <ChatbotRoom roomId={roomId}/>
                            </Fragment>
                        )
                    }
                </div>
        </div>
    )
}
export default ChatWithBot;
