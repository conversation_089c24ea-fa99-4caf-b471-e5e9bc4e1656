import React, {FC, useState, Fragment} from "react"
import { useParams, useNavigate} from "react-router-dom"

import ChatRoomsList from "./chat_rooms_list.tsx";
import ChatbotRoom from "./chatbot_room.tsx";

const Chatbot: FC = () => {
    const [selectedRoom, setSelectedRoom] = useState<TChatBotRoom | null>(null)

    const { roomId = ''} = useParams<{ roomId?: string }>() ?? {}
    const navigate = useNavigate()

    const selectRoom = async (room: TChatBotRoom) => {
        if (room.id !== roomId) {
            navigate(`/chatbot/${room.id}`, { replace: false });
        }
        setSelectedRoom(room)
    }

    return (
        <Fragment>
            <div className="d-flex w-100" style={{height: "100vh", }}>
                <div className="flex-grow-1 main-chart-wrapper pt-0 p-2 gap-2 d-lg-flex" style={{minWidth: 0}}>

                    <div className="chat-info border flex-shrink-0 d-flex flex-column"
                         style={{width: "300px", minWidth: "300px", maxHeight: "100vh"}}>
                        <ChatRoomsList
                            selectedRoom={selectedRoom}
                            onRoomSelect={selectRoom}
                        />
                    </div>

                    <ChatbotRoom
                        roomId={roomId}
                    />

                </div>
            </div>
        </Fragment>
    )
}

export default Chatbot
