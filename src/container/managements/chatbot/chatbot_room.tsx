import React, {FC, Fragment, useEffect, useRef, useState, useMemo, useCallback} from "react"
import {Button, Form, InputGroup, ListGroup, Spinner, Alert} from "react-bootstrap"
import {ChatBubble} from "./chat_bubble.tsx"
import {ChatMessage as IvsChatMessage, ChatRoom, ChatToken, SendMessageRequest} from "amazon-ivs-chat-messaging";
import {
    useGetChatbotMessagesQuery,
    useAnnotateChatbotMessageMutation,
    useLazyGetChatbotMessagesQuery,
    useTakeOverChatbotRoomMutation,
    useStopTakingOverChatbotRoomMutation,
    useCreateChatBotMessageMutation,
    useGetChatbotRoomByIdQuery,
} from "../../../services/chatbot/chatbot.ts"
import {useCreateChatTokenMutation} from "../../../services/chat/chat.ts";
import { store } from "../../../services/rtk/store.ts"
import {groupByDate, toMillis} from "./utils.ts";

type ChatbotRoomProps = {
    roomId: string
    onRoomChange?: (room: TChatBotRoom | null) => void
}

const ChatbotRoom: FC<ChatbotRoomProps> = ({
    roomId,
    onRoomChange
}) => {
    const CHAT_PAGE_SIZE = 10
    const [chatPage, setChatPage] = useState(1)
    const [hasMoreChatPage, setHasMoreChatPage] = useState(true)
    const [chatLoading, setChatLoading] = useState(false)
    const [chatError, setChatError] = useState<string | null>(null)
    const [selectedRoom, setSelectedRoom] = useState<TChatBotRoom | null>(null)
    const [input, setInput] = useState("")
    const [messages, setMessages] = useState<TChatbotMessage[]>([])
    const [annotatingId, setAnnotatingId] = useState<string | null>(null)
    const [annotationText, setAnnotationText] = useState("")
    const scrollRef = useRef<HTMLDivElement>(null)
    const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true)
    const [isStaffResponding, setIsStaffResponding] = useState(false)
    const [ivsRoom, setIvsRoom] = useState<ChatRoom | null>(null)
    const [isLoadingRoom, setIsLoadingRoom] = useState(false)
    const currentRoomIdRef = useRef<string>("")
    const previousScrollHeight = useRef<number>(0)

    const [takeOver, { isLoading: takingOver }] = useTakeOverChatbotRoomMutation()
    const [stopTakingOver, { isLoading: stopping }] = useStopTakingOverChatbotRoomMutation()
    const [getChatMessages] = useLazyGetChatbotMessagesQuery()
    const [getChatToken] = useCreateChatTokenMutation()
    const [sendMessage, { isLoading: isSending }] = useCreateChatBotMessageMutation()
    const [annotate, { isLoading: isAnnotating }] = useAnnotateChatbotMessageMutation()

    const {
        data: fetchedRoom,
        isLoading: isLoadingRoomData,
        isError: isRoomError,
        error: roomError
    } = useGetChatbotRoomByIdQuery(roomId, {
        skip: !roomId,
        refetchOnMountOrArgChange: true
    })

    const { data: fetched, isFetching: messagesLoading, error: messageError} =
        useGetChatbotMessagesQuery(
            { id: roomId, params: {page: 1, limit: CHAT_PAGE_SIZE} },
            { skip: !roomId, refetchOnFocus: true, refetchOnReconnect: true }
        )

    const currentAdmin = store.getState().auth?.user
    const currentAdminId = currentAdmin?.id ?? ""

    const canSend = !!selectedRoom && (
        isStaffResponding || selectedRoom.adminId === currentAdminId
    )

    const tokenProvider = async (roomId: string): Promise<ChatToken> => {
        const token = await getChatToken(roomId).unwrap()
        return token
    }

    const getMessageKey = (message: TChatbotMessage): string => {
        return message.ivsChatMessageId || message.id
    }

    const deduplicateMessages = (messages: TChatbotMessage[]): TChatbotMessage[] => {
        const seen = new Set<string>()
        return messages.filter(message => {
            const key = getMessageKey(message)
            if (seen.has(key)) {
                return false
            }
            seen.add(key)
            return true
        })
    }

    const mountRoom = async (room: TChatBotRoom) => {
        setIsLoadingRoom(true)
        currentRoomIdRef.current = room.id

        setSelectedRoom(room);
        onRoomChange?.(room)

        setMessages([]);
        setChatPage(1);
        setHasMoreChatPage(true);
        setShouldScrollToBottom(true);
        setIsStaffResponding(!!room.isStaffResponding);

        ivsRoom?.disconnect();
        setIvsRoom(null)

        try {
            const chatIVSRoom = new ChatRoom({
                regionOrUrl: import.meta.env.VITE_AWS_SNS_REGION,
                tokenProvider: () => tokenProvider(room.id),
            })
            setIvsRoom(chatIVSRoom)
            await chatIVSRoom.connect()
        } catch (error) {
            console.error("Failed to connect to IVS room:", error)
            setChatError("Failed to connect to chat room")
        } finally {
            setIsLoadingRoom(false)
        }
    }

    const refreshRoom = async () => {
        if (!selectedRoom) return
        await mountRoom(selectedRoom)
    }

    const toggleTakeOver = async () => {
        if (!selectedRoom || !ivsRoom) return

        try {
            if (isStaffResponding) {
                const request = new SendMessageRequest("Zurno Customer Service has left the chat");
                request.attributes = {
                    display_name: "System",
                    avatar_url: "",
                    type: "system",
                }

                try {
                    const response = await ivsRoom.sendMessage(request)
                    const { id: messageId } = response
                    const now = new Date().toISOString()
                    const systemMessage: TChatbotMessage = {
                        id: messageId,
                        roomId: selectedRoom.id,
                        content: "Zurno Customer Service has left the chat",
                        role: 'system',
                        sentAt: now,
                        userName: "System",
                        avatarUrl: "",
                        pending: true,
                        ivsChatMessageId: messageId,
                    }

                    setMessages((prev) => {
                        const exists = prev.some((message) => getMessageKey(message) === getMessageKey(systemMessage))
                        return exists ? prev : [...prev, systemMessage]
                    })

                    await sendMessage({
                        roomId: selectedRoom.id,
                        content: "Zurno Customer Service has left the chat"
                    }).unwrap()

                } catch (error) {
                    console.error("Failed to send leave message:", error)
                }

                await stopTakingOver({ roomId: selectedRoom.id }).unwrap()
                setIsStaffResponding(false)

            } else {
                await takeOver({ roomId: selectedRoom.id }).unwrap()
                setIsStaffResponding(true)

                const request = new SendMessageRequest("Zurno Customer Service has entered the chat");
                request.attributes = {
                    display_name: "System",
                    avatar_url: "",
                    type: "system",
                }

                try {
                    const response = await ivsRoom.sendMessage(request)
                    const { id: messageId } = response
                    const now = new Date().toISOString()
                    const systemMessage: TChatbotMessage = {
                        id: messageId,
                        roomId: selectedRoom.id,
                        content: "Zurno Customer Service has entered the chat",
                        role: "system",
                        sentAt: now,
                        userName: "System",
                        avatarUrl: "",
                        pending: true,
                        ivsChatMessageId: messageId
                    }

                    setMessages((prev) => {
                        const exists = prev.some((message) => getMessageKey(message) === getMessageKey(systemMessage))
                        return exists ? prev : [...prev, systemMessage]
                    })

                    await sendMessage({
                        roomId: selectedRoom.id,
                        content: "Zurno Customer Service has entered the chat"
                    }).unwrap()

                } catch (error) {
                    console.error("Failed to send enter message:", error)
                }
            }
        } catch(err) {
            console.error("Toggle takeover failed:", err)
            setChatError("Failed to change takeover status")
        }
    }

    const fetchChats = async (roomId: string, page: number, limit: number) => {
        if (currentRoomIdRef.current !== roomId) {
            return
        }

        setChatLoading(true)
        try {
            if (scrollRef.current) {
                previousScrollHeight.current = scrollRef.current.scrollHeight
            }

            const response = await getChatMessages(
                {id: roomId, params: { page: page, limit: limit }},
            ).unwrap()

            if (currentRoomIdRef.current !== roomId) {
                return
            }

            setMessages(prev => {
                const incomingMessages = response.messages
                const combined = [...incomingMessages, ...prev]
                return deduplicateMessages(combined)
            })

            if (response.nextPageUrl === null) {
                setHasMoreChatPage(false)
            }

        } catch (error) {
            if (currentRoomIdRef.current === roomId) {
                setChatError('An error occurred while fetching data')
            }
        } finally {
            setChatLoading(false)
        }
    }

    const chatObserver = useRef<IntersectionObserver | null>(null)

    const loadMoreObserverRef = useCallback(
        (node: HTMLDivElement) => {
            if (chatLoading) return
            if (chatObserver.current) chatObserver.current.disconnect()
            chatObserver.current = new IntersectionObserver(
                (entries) => {
                    if (entries[0].isIntersecting && hasMoreChatPage) {
                        setChatPage((prevPage) => prevPage + 1)
                    }
                },
                {
                    threshold: 0.5,
                    root: scrollRef.current,
                    rootMargin: '20px 0px 0px 0px'
                }
            )
            if (node) chatObserver.current.observe(node)
        },
        [chatLoading, hasMoreChatPage]
    )

    const ivsChatMessageToChatbot = (
        ivsChatMessage: IvsChatMessage,
        roomId: string,
    ): TChatbotMessage => {
        const { id, content, sendTime, sender, attributes } = ivsChatMessage
        let role: TChatbotRole
        let displayName: string

        switch (sender.attributes?.type) {
            case "assistant":
                role = "bot"
                displayName = sender.attributes?.role ?? "bot"
                break
            case "user":
                role = "user"
                const firstName = sender.attributes?.firstName?.trim()
                const lastName = sender.attributes?.lastName?.trim()
                displayName = (firstName || lastName)
                    ? `${firstName ?? ''} ${lastName ?? ''}`.trim()
                    : sender.attributes?.display_name
                    ?? sender.attributes?.email
                    ?? "Unknown sender"
                break
            case "admin":
                role = "staff"
                displayName = attributes?.display_name ?? "Unknown sender"
                break
            default:
                role = "system"
                displayName = "Unknown sender"
        }

        const avatarUrl = sender.attributes?.avatar_url ?? ""
        const userId = sender.userId

        return {
            id: id,
            roomId,
            content,
            role,
            sentAt: sendTime.toISOString(),
            userName: displayName,
            avatarUrl,
            attributes,
            assistantId: null,
            adminId: role === "staff" ? currentAdminId : null,
            annotatedChatMessageId: null,
            ivsChatMessageId: id,
            userId: role === "user" ? userId : null
        };
    };

    const pushAndSend = async () => {
        if (!input.trim() || !roomId || !ivsRoom) return

        const draft = input.trim()
        setInput("")

        try {
            const request = new SendMessageRequest(draft);
            request.attributes = {
                display_name: store.getState().auth.user?.name ?? "Staff",
                avatar_url: "",
                type: "staff",
            }

            const response = await ivsRoom.sendMessage(request)
            const {id: messageId} = response
            const now = new Date().toISOString()
            const optimistic: TChatbotMessage = {
                id: messageId,
                roomId,
                content: draft,
                role: "staff",
                sentAt: now,
                userName: currentAdmin?.username ?? "Unknown Staff",
                avatarUrl: "",
                pending: true,
                ivsChatMessageId: messageId
            }

            setMessages((prev) => {
                const exists = prev.some((message) => getMessageKey(message) === getMessageKey(optimistic))
                return exists ? prev : [...prev, optimistic]
            })

            await sendMessage({ roomId, content: draft }).unwrap()

        } catch (error) {
            console.error("Failed to send message:", error)
            setInput(draft) // Restore input on error
            setChatError("Failed to send message")
        }
    }

    const beginAnnotate = (id: string) => {
        setAnnotatingId(id)
        setAnnotationText("")
    }

    const commitAnnotation = async () => {
        if (!roomId || !annotatingId || !annotationText.trim()) return
        try {
            await annotate({ roomId, originalMessageId: annotatingId, content: annotationText.trim() }).unwrap()
            setMessages(prev => prev.map(message =>
                message.id === annotatingId ? { ...message, _localAnnotation: annotationText.trim() } : message
            ))
        } catch (error) {
            console.error(error)
        } finally {
            setAnnotatingId(null)
            setAnnotationText("")
        }
    }

    const cancelAnnotation = () => {
        setAnnotatingId(null)
        setAnnotationText("")
    }

    const saveAnnotation = async () => {
        if (!annotationText.trim() || isAnnotating) return
        try {
            await commitAnnotation()
        } catch (err) {
            console.error("Annotation failed", err)
        }
    }

    const annotations = useMemo(() => {
        const map: Record<string, string> = {}
        messages.forEach(message => {
            if (message.annotatedChatMessageId) {
                map[message.annotatedChatMessageId] = message.content
            }
        })
        return map
    }, [messages])

    const visibleMessages = messages.filter(message => message.annotatedChatMessageId === null)

    const chatMessagesByDate = useMemo(
        () => {
            const messagesInRoom = visibleMessages.filter(message => message.roomId === selectedRoom?.id)
            const groupedMessages = groupByDate(messagesInRoom, message => message.sentAt)
            return Object
                .entries(groupedMessages)
                .map(([label, messages]) => ({
                    label,
                    messages: messages,
                    timestamp: Math.max(...messages.map(message => toMillis(message.sentAt))),
                }))
                .sort((a, b) => a.timestamp - b.timestamp)
        },
        [visibleMessages, selectedRoom?.id]
    )

    useEffect(() => {
        if (!roomId || selectedRoom?.id === roomId) return

        if (isLoadingRoomData) return

        if (isRoomError) {
            console.error("Failed to fetch room:", roomError)
            setChatError("Failed to load chat room")
            onRoomChange?.(null)
            return
        }

        if (fetchedRoom) {
            console.log("Mounting room with data:", fetchedRoom)
            mountRoom(fetchedRoom)
        }
    }, [roomId, selectedRoom?.id, fetchedRoom, isLoadingRoomData, isRoomError])

    useEffect(() => {
        if (!selectedRoom || isLoadingRoom || selectedRoom.id !== currentRoomIdRef.current) return
        fetchChats(selectedRoom.id, chatPage, CHAT_PAGE_SIZE)
    }, [chatPage, selectedRoom?.id]);

    useEffect(() => {
        if (!fetched || currentRoomIdRef.current !== roomId) return

        const incomingMessages = fetched.messages
            .filter(message => message.content?.trim())

        setMessages(prev => {
            if (chatPage === 1) {
                const mergedMessages = [...prev]

                incomingMessages.forEach(messageFromApi => {
                    const optimisticIndex = mergedMessages.findIndex(
                        message => message.ivsChatMessageId && message.ivsChatMessageId === messageFromApi.ivsChatMessageId
                    )
                    if (optimisticIndex !== -1) {
                        mergedMessages[optimisticIndex] = { ...messageFromApi, pending: false }
                    } else {
                        const existsIndex = mergedMessages.findIndex(
                            message => getMessageKey(message) === getMessageKey(messageFromApi)
                        )
                        if (existsIndex === -1) {
                            mergedMessages.push(messageFromApi)
                        }
                    }
                })

                setShouldScrollToBottom(true)
                const sorted = mergedMessages.sort((a, b) => toMillis(a.sentAt) - toMillis(b.sentAt))
                return deduplicateMessages(sorted)
            }

            return prev
        })

    }, [fetched, roomId])

    useEffect(() => {
        if (!shouldScrollToBottom) return
        scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight, behavior: 'smooth' })
        setShouldScrollToBottom(false)
    }, [messages, shouldScrollToBottom])

    useEffect(() => {
        if (!ivsRoom || !selectedRoom) return

        const unsubscribe = ivsRoom.addListener('message', (incomingMessage) => {
            setMessages((prev) => {
                const chatbotMessage = ivsChatMessageToChatbot(incomingMessage, selectedRoom.id)

                const existingIndex = prev.findIndex(message =>
                    getMessageKey(message) === getMessageKey(chatbotMessage)
                )

                if (existingIndex !== -1) {
                    const clone = [...prev]
                    clone[existingIndex] = { ...chatbotMessage, pending: false }
                    return clone
                }

                return [...prev, chatbotMessage]
            })
            setShouldScrollToBottom(true)
        })

        return () => { unsubscribe() }
    }, [ivsRoom, selectedRoom?.id]);

    useEffect(() => {
        if (scrollRef.current && previousScrollHeight.current > 0 && chatPage > 1) {
            const container = scrollRef.current
            const newScrollHeight = container.scrollHeight
            const scrollDiff = newScrollHeight - previousScrollHeight.current
            const bufferOffset = 100
            container.scrollTop = container.scrollTop + scrollDiff + bufferOffset
            previousScrollHeight.current = 0
        }
    }, [messages, chatPage])

    if (isLoadingRoomData) {
        return (
            <div className="main-chat-area border flex-grow-1 d-flex flex-column align-items-center justify-content-center">
                <Spinner animation="border" />
                <p className="mt-3">Loading chat room...</p>
            </div>
        )
    }

    if (isRoomError) {
        return (
            <div className="main-chat-area border flex-grow-1 d-flex flex-column align-items-center justify-content-center">
                <Alert variant="danger">
                    Failed to load chat room. Please try again.
                </Alert>
            </div>
        )
    }

    return (
        <div className="main-chat-area border flex-grow-1 d-flex flex-column" style={{minWidth: 0}}>
            {/* Header */}
            <div className="d-flex align-items-center justify-content-between p-3 border-bottom">
                <h6 className="mb-0">
                    {selectedRoom ? (
                        <div className="d-flex flex-column">
                            <span className="fw-semibold"> {selectedRoom.userName} </span>
                        </div>
                    ) : (
                        <span className="text-muted">Select a room to start</span>
                    )}
                </h6>

                {selectedRoom && (
                    <div className="d-flex gap-2">
                        {selectedRoom.adminId !== currentAdminId && (
                            <Button
                                variant={isStaffResponding ? "secondary" : "success"}
                                size="sm"
                                disabled={takingOver || stopping}
                                onClick={toggleTakeOver}
                            >
                                {isStaffResponding ? "Stop Taking Over" : "Take Over"}
                            </Button>
                        )}

                        <Button
                            variant="outline-primary"
                            size="sm"
                            onClick={refreshRoom}
                            disabled={chatLoading || messagesLoading}
                            title="Refresh messages"
                        >
                            <i className="ri-refresh-line me-1"/>
                            Refresh
                        </Button>
                    </div>
                )}
            </div>

            {/* Messages */}
            <div ref={scrollRef} className="flex-grow-1 overflow-auto p-3">
                {!selectedRoom && !isLoadingRoom && (
                    <p className="text-center text-muted mb-0">
                        Choose a chat room on the left
                    </p>
                )}

                {(selectedRoom && (messagesLoading || isLoadingRoom) && chatMessagesByDate.length === 0) && (
                    <div className="text-center py-4">
                        <Spinner animation="border" size="sm"/>
                    </div>
                )}

                {(messageError || chatError) && selectedRoom ? (
                    <Alert variant="danger" className="m-3">
                        {chatError || "Couldn't load messages."}
                    </Alert>
                ) : null}

                {/* Load More Indicator at the top */}
                {selectedRoom && hasMoreChatPage && (
                    <div ref={loadMoreObserverRef} className="text-center py-3">
                        {chatLoading ? (
                            <div className="d-flex align-items-center justify-content-center">
                                <Spinner animation="border" size="sm" className="me-2"/>
                                <small className="text-muted">Loading older messages...</small>
                            </div>
                        ) : (
                            <small className="text-muted">Scroll up to load more messages</small>
                        )}
                    </div>
                )}

                <ListGroup variant="flush">
                    {chatMessagesByDate.map(({label, messages}) => (
                        <Fragment key={label}>
                            <ListGroup.Item className="text-center bg-transparent border-0 py-1">
                                <small
                                    className="text-muted text-uppercase d-inline-block px-2 py-1 rounded-2e"
                                    style={{
                                        backgroundColor: 'rgba(114, 105, 239, 0.15)',
                                        color: '#7269ef',
                                        fontSize: '0.75rem'
                                    }}>
                                    {label}
                                </small>
                            </ListGroup.Item>
                            {messages.map((message) => (
                                <Fragment key={message.id}>
                                    <div className="border-b py-4">
                                        <ChatBubble
                                            message={message}
                                            annotation={annotations[message.id]}
                                            onDoubleClick={
                                                message.role === "bot"
                                                    ? () => beginAnnotate(message.id)
                                                    : undefined
                                            }
                                        />
                                    </div>

                                    {/* Inline annotation editor */}
                                    {annotatingId === message.id && (
                                        <Form
                                            className="my-2"
                                            onSubmit={(e) => {
                                                e.preventDefault()
                                                commitAnnotation()
                                            }}
                                        >
                                            <InputGroup>
                                                <Form.Control
                                                    placeholder="Add annotation…"
                                                    value={annotationText}
                                                    onChange={(e) => setAnnotationText(e.target.value)}
                                                    disabled={isAnnotating}
                                                    onKeyDown={(e) => {
                                                        if (e.key === "Enter") {
                                                            e.preventDefault()
                                                            commitAnnotation()
                                                        } else if (e.key === "Escape") {
                                                            e.preventDefault()
                                                            cancelAnnotation()
                                                        }
                                                    }}
                                                />
                                                <Button
                                                    type="button"
                                                    variant="primary"
                                                    disabled={isAnnotating || !annotationText.trim()}
                                                    onClick={saveAnnotation}
                                                >
                                                    Save
                                                </Button>
                                                <Button
                                                    type="button"
                                                    variant="secondary"
                                                    disabled={isAnnotating}
                                                    onClick={cancelAnnotation}
                                                >
                                                    Cancel
                                                </Button>
                                            </InputGroup>
                                        </Form>
                                    )}
                                </Fragment>
                            ))}
                        </Fragment>
                    ))}
                </ListGroup>
            </div>

            {/* Footer / compose box */}
            <div className="border-top p-3 d-flex gap-2">
                <Form.Control
                    placeholder="Type a message…"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    disabled={!canSend}
                    onKeyDown={(e) => {
                        if (e.key === "Enter" && !e.shiftKey && canSend) {
                            e.preventDefault()
                            pushAndSend()
                        }
                    }}
                />
                <Button onClick={pushAndSend} disabled={!input.trim() || !canSend || isSending}>
                    <i className="ri-send-plane-2-line me-1"/> Send
                </Button>
            </div>
        </div>
    )
}

export default ChatbotRoom
