export const truncateSentence = (text: string, maxWords: number = 5) => {
    if (!text) return ""
    const words = text.trim().split(/\s+/)
    if (words.length <= maxWords) return text
    return words.slice(-maxWords).join(" ")
}

export const groupByDate = <T,>(
    items: T[],
    getDate: (item: T) => string | undefined
): Record<string, T[]> => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const MS_IN_DAY = 86_400_000

    return items.reduce<Record<string, T[]>>((lbls, item) => {
        const time = getDate(item)
        const date = time ? new Date(time) : new Date(0)
        const day = new Date(date.getFullYear(), date.getMonth(), date.getDate())
        const difference = today.getTime() - day.getTime()

        let label: string
        if (difference === 0) label = "Today"
        else if (difference === MS_IN_DAY) label = "Yesterday"
        else {
            label = date.toLocaleDateString(undefined, {
                year: "numeric",
                month: "short",
                day: "numeric",
            })
        }
        (lbls[label] ??= []).push(item)
        return lbls
    }, {})
}

export const toMillis = (date?: string) => {
    const time = date ? Date.parse(date) : NaN
    return Number.isNaN(time) ? 0 : time
}
