import { FC, Fragment, useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>on, <PERSON>ert, <PERSON><PERSON>, Card, Col, Form, InputGroup, OverlayTrigger, Row, Tooltip } from "react-bootstrap";
import GooglePlacesAutocomplete from "react-google-places-autocomplete";
import { Link, useNavigate, useParams } from "react-router-dom";

import { useUploadMultipleMutation } from "../../../../../services/media";
import {
  useCreatePostMutation,
  useDeletePostLocaleTranslationMutation,
  useLazyDeletePostByIdQuery,
  useUpdatePostMutation
} from "../../../../../services/post/post";
import { useLazyFilterStoreNameQuery, useLazyGetPlaceDetailByPlaceIdQuery } from "../../../../../services/store/store";

import axios from "axios";
import { debounce } from "lodash";
import InputGroupText from "react-bootstrap/esm/InputGroupText";
import ReactQuill from "react-quill";
import LogoDropzone from "../../../../../components/dropzone/logo_dropzone";
import MediaDropzone from "../../../../../components/dropzone/media_dropzone";
import LazySelect from "../../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../../components/table-title/card-header-with-back";
import { useLazySelectPostCategoryQuery } from "../../../../../services/post-category";
import { store } from "../../../../../services/rtk/store";
import { hasPermission } from "../../../../../utils/authorization";
import { EAdminCountries } from "../../../../../utils/constant/admin";
import { ACTION, RESOURCE } from "../../../../../utils/constant/authorization";
import { TRACKING_ACTION } from "../../../../../utils/constant/tracking";
import { DEFAULT_LOCALE, LANGUAGES, TRANSLATIONS, TRANSLATIONS_ENUM } from "../../../../../utils/constant/translation";
import { getAllErrorMessages } from "../../../../../utils/errors";
import PostComments from "../../zurno-post/details/post_comments";
import moment from "moment";
import DatePicker from 'react-datepicker'

interface ClassifiedPostDetailsProps {
  post: TPost | null
}

const ClassifiedPostDetails: FC<
  ClassifiedPostDetailsProps
> = ({ post }) => {
  const { id } = useParams();

  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});

  const [detailsFormData, setDetailsFormData] = useState<any>({});

  const [thumbnailUploaded, setThumbnailUploaded] = useState<
    TMedia | { url: string } | null
  >(null);
  const [mediasUploaded, setMediasUploaded] = useState<TMedia[]>([]);
  const [mediasUploading, setMediasUploading] = useState<any>([]);

  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [isInvalidYoutubeUrl, setIsInvalidYoutubeUrl] = useState(false);

  const [locale, setLocale] = useState<TRANSLATIONS_ENUM | string>(DEFAULT_LOCALE)
  const [translations, setTranslations] = useState<any>({})
  const [interactions, setInteractions] = useState<any | null>(null)
  // const [postCategoriesOptions, setPostCategoriesOptions] =
  //   useState<{ label: string; value: TPostCategory }[]>([]);

  const [selectStoreNames] = useLazyFilterStoreNameQuery();
  const [createPost] = useCreatePostMutation();
  const [updatePost] = useUpdatePostMutation();
  const [deletePost] = useLazyDeletePostByIdQuery();
  const [deleteTranslations] = useDeletePostLocaleTranslationMutation()
  const [upload] = useUploadMultipleMutation();
  const [getPlaceDetail] = useLazyGetPlaceDetailByPlaceIdQuery();
  // const [getGPTTranslation] = useLazyGptTranslateQuery()

  const navigate = useNavigate();

  useEffect(() => {
    setIsAdd(false);
    setIsEdit(false);

    if (id == 'new') {
      setIsAdd(true)
    } else {
      setIsEdit(true);
    }

  }, [id])

  const prepareOption = (label: string, value: any) => {
    return {
      label: label,
      value: value,
    };
  };

  const prepareReceivedData = (data) => {
    const details: any = {
      id: data.id,

      title: data.title,
      price: data.price,
      description: data.description,

      isDraft: data.isDraft,
      isFavourite: data.isFavourite,
      isUnlist: data.isUnlist,
      expired: data.expired,

      countryId: data.countryId,
      stateId: data.stateId,
      cityId: data.cityId,

      latitude: data.latitude,
      longitude: data.longitude,
      address: data.address,
      address2: data.address2,
      zipcode: data.zipcode,

      user: data.user,
      contactName: data.contactName,
      phone: data.phone,
      email: data.email,

      url: data.url,
      vietId: data.vietId,

      categories: data.categories,
    };

    // if (data.categories) {
    //   details.categories = data.categories?.map((cat: TPostCategory) => {
    //     return prepareOption(cat?.name, cat);
    //   });
    // }

    if (data.thumbnailId) {
      setThumbnailUploaded(data.thumbnail);
    }

    if (data.medias) {
      setMediasUploaded(data.medias);
    }

    if (data.youtubeUrl) {
      setYoutubeUrl(data.youtubeUrl);
    }

    if (data.store) {
      details.store = data.store;
      details.storeName = data.store.name || "";
      details.storePhone = data.store.phoneNumber || "";
      details.storeLocation = data.store.address || "";
    }

    details.originLocale = data.originLocale
    if (data.translations?.length > 0) {
      const translates = {}
      data.translations.map((translate) => {
        translates[translate.locale] = {
          ...translates[translate.locale],
          [translate.field]: translate,
        }
      })

      setLocale(data.translations[0].locale)
      setTranslations(translates)
    }

    if (data.interactions) {
      setInteractions(data.interactions)
    }

    setDetailsFormData(details);
  }

  useEffect(() => {
    // if (isEdit) {
    //   setIsLoading(true);
    //   getDataToUpdate(id || "")
    //     .unwrap()
    //     .then((res) => {
    //       prepareReceivedData(res.data);
    //     })
    //     .catch((error) => {
    //       setErr(getAllErrorMessages(error));
    //     })
    //     .finally(() => {
    //       setIsLoading(false);
    //     });
    // }
    if (isEdit && post) {
      prepareReceivedData(post)
    }
  }, [isEdit, post]);

  const handleAddFormChange = (event: any) => {
    if (event) {
      event.preventDefault();
    }

    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.value;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData);

    if (err?.validationErrors) {
      if (err.validationErrors[fieldName]) {
        delete err.validationErrors[fieldName];
      }
    }
  };

  const handleOptionsFormChange = (option: any, action: any) => {
    const fieldName = action.name;
    const fieldValue = option?.value || "";

    const newFormData: any = { ...detailsFormData };

    switch (fieldName) {
      case "user": {
        if (fieldValue) {
          newFormData["user"] = fieldValue;
          newFormData["contactName"] =
            fieldValue.firstName + " " + fieldValue.lastName;
          newFormData["phone"] = fieldValue.phone;
          newFormData["email"] = fieldValue.email;
        } else {
          newFormData["user"] = null;
        }
        break;
      }
      case "store": {
        if (fieldValue) {
          newFormData["store"] = fieldValue;
          newFormData["storeName"] = fieldValue.name;
          newFormData["storePhone"] = fieldValue.phoneNumber;
          newFormData["storeLocation"] = fieldValue.address;

          newFormData.address = fieldValue.address

          if (fieldValue.user && !newFormData["user"]) {
            newFormData["user"] = fieldValue.user;
            newFormData["contactName"] =
              fieldValue.user.firstName + " " + fieldValue.user.lastName;
            newFormData["phone"] = fieldValue.user.phone;
            newFormData["email"] = fieldValue.user.email;
          }
        } else {
          newFormData["store"] =
            newFormData["storeName"] =
            newFormData["storePhone"] =
            newFormData["storeLocation"] =
            null;
        }
        break;
      }
      default: {
        newFormData[fieldName] = fieldValue;
        break;
      }
    }

    if (fieldName == "country") {
      newFormData["state"] = "";
      newFormData["city"] = "";
    }
    if (fieldName == "state") {
      newFormData["city"] = "";
    }

    setDetailsFormData(newFormData);
  };

  const handCheckFormChange = (event: any) => {
    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.checked ? 1 : 0;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData);
  };

  // const handleCategoriesOptionsFormChange = (option: any) => {
  //   const newFormData: any = { ...detailsFormData };
  //   newFormData.categories = option;
  //   setDetailsFormData(newFormData);
  // };

  const handleThumbnailUpload = async (file: File) => {
    try {
      const [uploadedLogo] = await upload({ file: [file] }).unwrap();
      setThumbnailUploaded(uploadedLogo);
    } catch (error: any) {
      setErr(getAllErrorMessages(error));
    }
  };

  const removeThumbnail = () => {
    setThumbnailUploaded(null);
  };

  const handleFilesMediaChange = async (fileList: File[]) => {
    const validFiles = fileList.filter((file) => file);
    if (validFiles.length > 0) {
      upload({ file: validFiles })
        .unwrap()
        .then((uploadedFiles) => {
          setMediasUploaded((prev) => [...prev, ...uploadedFiles]);
          setMediasUploading([]);
        })
        .catch((error) => {
          const formattedErrors = error?.data?.errors?.reduce(
            (acc: any, curr: any) => {
              acc[curr.field] = curr.message;
              return acc;
            },
            {}
          );
          setErr(formattedErrors);
        });
    }
  };

  const removeFile = (index: number) => {
    if (index < mediasUploading.length) {
      setMediasUploading((prev) => prev.filter((_, i) => i !== index));
    } else {
      const uploadedIndex = index - mediasUploading.length;
      setMediasUploaded((prev) => prev.filter((_, i) => i !== uploadedIndex));
    }
  };

  useEffect(() => {
    handleFilesMediaChange(mediasUploading);
  }, [mediasUploading]);

  const debouncedHandleYoutubeFetch = useCallback(
    debounce(async (url) => {
      const pattern =
        /^.*(?:(?:youtu\.be\/|v\/|vi\/|u\/\w\/|embed\/|shorts\/)|(?:(?:watch)?\?v(?:i)?=|\&v(?:i)?=))([^#\&\?]*).*/;
      const matches = url.match(pattern);

      if (!matches) {
        setIsInvalidYoutubeUrl(true);
        setIsLoading(false);
        return;
      }

      axios
        .get(
          `https://www.googleapis.com/youtube/v3/videos?id=${matches[1]}&key=${import.meta.env.VITE_GOOGLE_PLACES_API_KEY
          }&part=snippet`
        )
        .then((res) => {
          if (res.data.items.length > 0) {
            setYoutubeUrl(
              `https://www.youtube.com/watch?v=${res.data.items[0].id}`
            );

            setThumbnailUploaded(res.data.items[0].snippet.thumbnails.high);

            setDetailsFormData((prev) => {
              return {
                ...prev,
                title: res.data.items[0].snippet.title,
                description: res.data.items[0].snippet.description,
              };
            });
          } else {
            setIsInvalidYoutubeUrl(true);
          }
        })
        .catch((error) => setErr(getAllErrorMessages(error)))
        .finally(() => {
          setIsLoading(false);
        });
    }, 1000),
    []
  );

  const handleYoutubeUrlFetch = async (event: any) => {
    if (event) {
      event.preventDefault();
    }

    if (youtubeUrl) {
      setIsLoading(true);
      debouncedHandleYoutubeFetch(youtubeUrl);
    } else {
      setIsInvalidYoutubeUrl(true);
    }
  }

  const [quillInput, setQuillInput] = useState(false)
  const handleTranslationChange = (field: string, value: string) => {
    if (!quillInput && field == 'description') { return }

    const newTranslations = { ...translations }

    newTranslations[locale] = {
      ...newTranslations[locale]
    }
    if (field == 'title & description') {
      const lineBreakIdx = value?.indexOf('\n')
      newTranslations[locale].title = {
        ...newTranslations[locale].title,
        locale,
        field: 'title',
        value: value?.slice(0, lineBreakIdx)
      }

      newTranslations[locale].description = {
        ...newTranslations[locale].description,
        locale,
        field: 'description',
        value: value?.slice(lineBreakIdx)
      }

    } else {
      newTranslations[locale][field] = {
        ...newTranslations[locale][field],
        locale,
        field,
        value,
      }
    }

    setTranslations(newTranslations)
  }

  // const handleTranslateClick = () => {
  //   setQuillInput(false)
  //   setIsLoading(true)
  //   console.log( `${detailsFormData.title}\n ${detailsFormData.description}`);

  //   getGPTTranslation({
  //     text: `${detailsFormData.title}\n ${detailsFormData.description}`,
  //     translateTo: locale as TRANSLATIONS_ENUM,
  //   })
  //     .then((res) => {
  //       console.log(res.data);

  //       handleTranslationChange('title & description', res.data?.text || "")
  //     })
  //     .catch((res) => getAllErrorMessages(res))
  //     .finally(() => setIsLoading(false))
  // }

  const handleAddressChange = (place: any) => {
    getPlaceDetail(place?.value?.place_id)
      .unwrap()
      .then((placeDetail) => {
        setDetailsFormData((prevData: any) => {
          return {
            ...prevData,
            address: place.label,
            // placeId: place?.value?.place_id,
            latitude: placeDetail.lat,
            longitude: placeDetail.lng,
            zipcode: placeDetail.zipCode,
            country: placeDetail.country,
            state: placeDetail.state,
            city: placeDetail.city,
          };
        });
      })
      .catch((error) => {
        const formattedErrors = error?.data?.errors?.reduce(
          (acc: any, curr: any) => {
            acc[curr.field] = curr.message;
            return acc;
          },
          {}
        );
        setErr(formattedErrors);
      });
  };

  const prepareSubmitForm = (detailsFormData: any) => {
    const preparedForm: any = {
      title: detailsFormData?.title,
      price: detailsFormData?.price,
      description: detailsFormData?.description,

      isDraft: Number(detailsFormData?.isDraft) || 0,
      isFavourite: Number(detailsFormData?.isFavourite) || 0,
      isUnlist: Number(detailsFormData?.isUnlist) || 0,
      expired: Number(detailsFormData?.expired) || 0,

      latitude: detailsFormData?.latitude || detailsFormData?.store?.latitude,
      longitude: detailsFormData?.longitude || detailsFormData?.store?.longitude,
      address: detailsFormData?.address || detailsFormData?.store?.address,
      address2: detailsFormData?.address2 || detailsFormData?.store?.address2,
      zipcode: detailsFormData?.zipcode || detailsFormData?.store?.zipCode,

      countryId: detailsFormData?.countryId || detailsFormData?.store?.countryId,
      stateId: detailsFormData?.stateId || detailsFormData?.store?.stateId,
      cityId: detailsFormData?.cityId || detailsFormData?.store?.cityId,

      country: detailsFormData?.country,
      state: detailsFormData?.state,
      city: detailsFormData?.city,

      userId: detailsFormData?.user?.id,
      contactName: detailsFormData?.contactName,
      phone: detailsFormData?.phone,
      email: detailsFormData?.email,

      url: detailsFormData?.url,
      vietId: Number(detailsFormData?.vietId),

      categoryIds: detailsFormData?.categories?.map((op: any) => {
        return op.id;
      }),

      storeId: detailsFormData?.store?.id,
    };


    if (thumbnailUploaded) {
      if (thumbnailUploaded["id"]) {
        preparedForm.thumbnailId = thumbnailUploaded["id"];
      } else {
        preparedForm.thumbnailUrl = thumbnailUploaded.url;
      }
    }

    if (mediasUploaded) {
      preparedForm.mediaIds = mediasUploaded.map((media) => media.id);
    }

    if (youtubeUrl) {
      preparedForm.youtubeUrl = youtubeUrl;
    }

    preparedForm.originLocale = detailsFormData.originLocale
    if (translations) {
      preparedForm.translations = Object.values(translations).flatMap((translates: any) => Object.values(translates));
    }

    const currentAdmin = store.getState().auth.user
    preparedForm.createdByAdminId = currentAdmin?.id

    return preparedForm;
  };

  const handleAddFormSubmit = (event: any, scheduled?: boolean) => {
    if (event) {
      event.preventDefault();
    }

    const newPost = prepareSubmitForm(detailsFormData);

    if (scheduled) {
      newPost.isDraft = 1
      newPost.scheduledAt = detailsFormData.scheduledAt
    }

    setIsLoading(true);
    createPost({ ...newPost })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        navigate("/managements-marketplace/post");
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleUpdateFormSubmit = (event: any, scheduled?: boolean) => {
    if (event) { event.preventDefault(); }

    const updatedPost = prepareSubmitForm(detailsFormData);

    if (scheduled) {
      updatedPost.isDraft = 1
      updatedPost.scheduledAt = detailsFormData.scheduledAt
    }

    setIsLoading(true);
    updatePost({ id: detailsFormData.id, ...updatedPost })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        navigate("/managements-marketplace/post");
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleDeleteClick = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: deletePost,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => navigate("/managements-marketplace/post")),
      finalAction: (() => setIsLoading(false)),
    })
  };

  const handleTranslationDelete = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: () => deleteTranslations({ postId: id || "", locale: locale as TRANSLATIONS_ENUM }),
      confirmText: `Do you want to delete the ${LANGUAGES[locale]} translation?`,
      finishText: `${LANGUAGES[locale]} translation deleted!`,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => {
        const newTranslations = { ...translations }
        delete newTranslations[locale]
        setLocale(Object.keys(newTranslations)[0])
        setTranslations(newTranslations)
      }
      ),
      finalAction: (() => setIsLoading(false)),
    })
  }
  const [selectPostCategories] = useLazySelectPostCategoryQuery()


  const admin = store.getState().auth.user as TAdmin
  const adminCountries = (admin?.isSuperAdmin || !admin?.countries)
    ? Object.values(EAdminCountries)
    : admin?.countries

  const [expandState, setExpandState] = useState(true)
  const expandSize = expandState ? 3 : 1
  const calculateLeftSize = () => {
    return isAdd
      ? 6
      : Math.ceil((12 - expandSize) / 2)
  }
  const calculateRightSize = () => {
    return isAdd
      ? 6
      : Math.floor((12 - expandSize) / 2)
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title="Post Details"
            route="/managements-marketplace/post"
          ></CardHeaderWithBack>
          {interactions && <Card.Subtitle>
            <Row>
              <Col>
                <div>
                  <span className="ri-eye-line mx-1" />
                  {interactions[TRACKING_ACTION.VIEW_POST]?.count || 0}
                </div>
                <div>
                  <span className="ri-share-line mx-1" />
                  {interactions[TRACKING_ACTION.SHARE]?.count || 0}
                </div>
              </Col>
              <Col>
                <div>
                  <span className="ri-heart-line mx-1" />
                  {interactions[TRACKING_ACTION.ADD_WISHLIST]?.count || 0}
                </div>
                <div>
                  <span className="ri-phone-line mx-1" />
                  {interactions[TRACKING_ACTION.CLICK_CALL_ON_POST]?.count || 0}
                </div>
              </Col>
            </Row>
          </Card.Subtitle>
          }
        </Card.Header>
        <div>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}
        </div>
        <Card.Body>
          <Row>
            <Col xl={2} lg={4} md={4} sm={6} className="text-center">
              {/* <Form.Label>Thumbnail</Form.Label> */}
              <LogoDropzone
                uploadedLogo={thumbnailUploaded}
                uploadFunction={handleThumbnailUpload}
                removeLogo={removeThumbnail}
                styles={{
                  dropzone: {
                    height: "200px",
                  },
                  preview: {
                    width: "100px",
                    height: "100px",
                  },
                }}
              />
            </Col>
            <Col xl={10} lg={8} md={8} sm={12} className="mb-3 text-center">
              {/* <Form.Label>Upload Media Files</Form.Label> */}
              <MediaDropzone
                uploadedFiles={mediasUploaded.map((media) => ({
                  url: media.url,
                }))}
                uploadFunction={setMediasUploading}
                removeFile={removeFile}
                style={{
                  minHeight: "200px",
                }}
              />
            </Col>
          </Row>
        </Card.Body>
      </Card>
      <Row>
        <Col lg={calculateLeftSize()}>
          <Card>
            <Card.Body>
              <InputGroup>
                <Form.Control
                  placeholder="Youtube URL"
                  value={youtubeUrl || ""}
                  onChange={((e) => {
                    setIsInvalidYoutubeUrl(false)
                    setYoutubeUrl(e.target.value)
                  })}
                  isInvalid={isInvalidYoutubeUrl}
                />
                <Button onClick={handleYoutubeUrlFetch}                    >
                  Fetch
                </Button>
              </InputGroup>
              <div
                hidden={!isInvalidYoutubeUrl}
                className="text-danger small"
                role="alert">
                Not a valid Youtube URL
              </div>
            </Card.Body>
          </Card>
          <Card className="custom-card">
            <Card.Header hidden={isAdd}>
              <InputGroup>
                <InputGroupText>Locale</InputGroupText>
                <Form.Select
                  value={detailsFormData.originLocale || ""}
                  name="originLocale"
                  onChange={handleAddFormChange}
                >
                  <option value=''></option>
                  {TRANSLATIONS.map((translation, index) => (
                    <option key={index} value={translation}>
                      {LANGUAGES[translation]}
                    </option>
                  ))}
                </Form.Select>
              </InputGroup>
            </Card.Header>
            <Card.Body>
              <Form.Label>Title*</Form.Label>
              <Form.Control
                className="mb-3"
                type="text"
                required
                name="title"
                placeholder="Title"
                value={detailsFormData?.title || ""}
                onChange={handleAddFormChange}
                isInvalid={err?.validationErrors?.title}
              />
              <Form.Control.Feedback className="invalid-feedback">
                {err?.validationErrors?.title}
              </Form.Control.Feedback>
              <Form.Label>Description</Form.Label>
              <ReactQuill
                theme="snow"
                value={detailsFormData?.description || ""}
                onChange={(value) => setDetailsFormData((prev) => ({
                  ...prev,
                  description: value
                }))}
              />
              {/* <label
                  htmlFor="product-description-add"
                  className="form-label mt-1 fs-12 op-5 text-muted mb-0"
                >
                  *Description should not exceed 500 letters
                </label> */}
            </Card.Body>
            {Object.entries(translations).length > 0 &&
              // <Card.Body>
              <Accordion hidden={!hasPermission(ACTION.ACCESS_TRANSLATION, RESOURCE.POST)}>
                <Accordion.Item eventKey="0">
                  <Accordion.Header>Translations</Accordion.Header>
                  <Accordion.Body>
                    <InputGroup>
                      <InputGroupText>Locale</InputGroupText>
                      <Form.Select
                        onChange={(e) => setLocale(e.target.value)}
                        value={locale}
                      >
                        {Object.keys(translations).map((translation, index) => (
                          <option key={index} value={translation}>
                            {LANGUAGES[translation]}
                          </option>
                        ))}
                      </Form.Select>
                      {/* <Button onClick={handleTranslateClick}>Translate</Button> */}
                      <Button
                        variant="danger-light"
                        className="btn btn-sm"
                        onClick={handleTranslationDelete}
                      >Delete</Button>
                    </InputGroup>
                  </Accordion.Body>
                  <Accordion.Body>
                    <Form.Label>Title</Form.Label>
                    <Form.Control
                      className="mb-3"
                      type="text"
                      placeholder="Title"
                      value={translations?.[locale]?.title?.value || ""}
                      onChange={(e) => handleTranslationChange('title', e.target.value)
                      }
                    // isInvalid={err?.validationErrors?.title}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.title}
                    </Form.Control.Feedback>
                    <Form.Label>Description</Form.Label>
                    <ReactQuill
                      theme="snow"
                      value={translations?.[locale]?.description?.value || ""}
                      onChange={(value) => handleTranslationChange('description', value)}
                      onKeyDown={() => setQuillInput(true)}
                    />
                    {/* <label
                  htmlFor="product-description-add"
                  className="form-label mt-1 fs-12 op-5 text-muted mb-0"
                >
                  *Description should not exceed 500 letters
                </label> */}
                  </Accordion.Body>
                </Accordion.Item>
              </Accordion>
              // </Card.Body>
            }
          </Card>
        </Col>
        <Col lg={calculateRightSize()}>
          <Card>
            <Card.Body>
              <Col className="mb-3">
                <Form.Label>Price</Form.Label>
                <Form.Control
                  type="number"
                  name="price"
                  placeholder="Price"
                  value={detailsFormData?.price || ""}
                  onChange={handleAddFormChange}
                  isInvalid={err?.validationErrors?.price}
                />
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.price}
                </Form.Control.Feedback>
              </Col>
              <Col className="mb-3">
                <Form.Label>Categories</Form.Label>
                <LazySelect
                  isMulti
                  selectionFunction={selectPostCategories}
                  label={(value) => value.name}
                  initialSelectedOptions={detailsFormData?.categories}
                  getSelectedOptions={(value) => setDetailsFormData((prev) => ({
                    ...prev,
                    categories: value
                  }))}
                />
                {/* <Select
                  required
                  isSearchable
                  isMulti
                  name="categories"
                  placeholder="Categories"
                  // classNamePrefix="Select2"
                  options={postCategoriesOptions}
                  value={detailsFormData?.categories}
                  onChange={handleCategoriesOptionsFormChange}
                // isInvalid={err?.validationErrors?.categories}
                /> */}
                {/* <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.categories}
                  </Form.Control.Feedback> */}
              </Col>
              <Col className="mb-3">
                <Row>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="isDraft"
                      label="Draft"
                      checked={detailsFormData?.isDraft ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.isDraft}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.isDraft}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="isFavourite"
                      label="Favourite"
                      checked={detailsFormData?.isFavourite ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.isFavourite}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.isFavourite}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="isUnlist"
                      label="Unlist"
                      checked={detailsFormData?.isUnlist ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.isUnlist}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.isUnlist}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="expired"
                      label="Expired"
                      checked={detailsFormData?.expired ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.expired}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.expired}
                    </Form.Control.Feedback>
                  </Col>
                </Row>
              </Col>
            </Card.Body>
          </Card>
          <Card>
            <Card.Body>
              <Card className="mb-4">
                <Card.Header>
                  <Card.Title
                    className="fw-bold"
                    style={{ color: "#818995", fontSize: "1rem" }}
                  >
                    Contact Information
                  </Card.Title>
                </Card.Header>
                {/* <Select
                      isSearchable
                      isClearable
                      // classNamePrefix="Select2"
                      name="user"
                      placeholder="User"
                      options={userOptions}
                      value={
                        detailsFormData?.user
                          ? prepareOption(
                            detailsFormData.user?.firstName +
                            " " +
                            detailsFormData.user?.lastName,
                            detailsFormData.user
                          )
                          : ""
                      }
                      onChange={handleOptionsFormChange}
                    /> */}
                <Card.Body>
                  <Row className="align-items-center">
                    <Col xs={1} className="d-flex justify-content-center">
                      <i
                        className="bx bx-user-circle"
                        style={{ fontSize: "24px", color: "#818995" }}
                      ></i>
                    </Col>
                    <Col xs={10} className="ps-0">
                      <Form.Control
                        type="text"
                        name="contactName"
                        placeholder="Contact Name"
                        value={detailsFormData?.contactName || ""}
                        onChange={handleAddFormChange}
                        className="border-0"
                      />
                    </Col>
                  </Row>
                  <Row className="align-items-center mt-3">
                    <Col xs={1} className="d-flex justify-content-center">
                      <i
                        className="bx bx-phone"
                        style={{ fontSize: "24px", color: "#818995" }}
                      ></i>
                    </Col>
                    <Col xs={10} className="ps-0">
                      <Form.Control
                        type="text"
                        name="phone"
                        placeholder="Phone"
                        value={detailsFormData?.phone || ""}
                        onChange={handleAddFormChange}
                        className="border-0"
                      />
                    </Col>
                  </Row>
                  <Row className="align-items-center mt-3">
                    <Col xs={1} className="d-flex justify-content-center">
                      <i
                        className="bx bx-envelope"
                        style={{ fontSize: "24px", color: "#818995" }}
                      ></i>
                    </Col>
                    <Col xs={10} className="ps-0">
                      <Form.Control
                        type="text"
                        name="email"
                        placeholder="Email"
                        value={detailsFormData?.email || ""}
                        onChange={handleAddFormChange}
                        className="border-0"
                      />
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
              <Card>
                <Card.Header>
                  <Card.Title
                    className="fw-bold"
                    style={{ color: "#818995", fontSize: "1rem" }}
                  >
                    Store Information
                  </Card.Title>
                </Card.Header>
                <Card.Body>
                  <Row className="align-items-center">
                    <Col xs={1} className="d-flex justify-content-center">
                      <i
                        className="bx bx-store-alt"
                        style={{ fontSize: "24px", color: "#818995" }}
                      ></i>
                    </Col>
                    <Col xs={10} className="ps-0">
                      <LazySelect
                        selectionFunction={selectStoreNames}
                        label={(value) => value.name}
                        initialSelectedOptions={detailsFormData?.store}
                        getSelectedOptions={(selected) => {
                          handleOptionsFormChange({ value: selected }, { name: 'store' });
                        }}
                      />

                      {/* <Form.Control
                            type="text"
                            name="storeName"
                            placeholder="Store Name"
                            disabled={true}
                            value={detailsFormData?.storeName || ""}
                            onChange={handleAddFormChange}
                            className="border-0"
                          /> */}
                    </Col>
                  </Row>
                  <Row className="align-items-center mt-3">
                    <Col xs={1} className="d-flex justify-content-center">
                      <i
                        className="bx bx-phone"
                        style={{ fontSize: "24px", color: "#818995" }}
                      ></i>
                    </Col>
                    <Col xs={10} className="ps-0">
                      <Form.Control
                        type="text"
                        name="storePhone"
                        placeholder="Store Phone"
                        disabled={true}
                        value={detailsFormData?.storePhone || ""}
                        onChange={handleAddFormChange}
                        className="border-0"
                      />
                    </Col>
                  </Row>
                  <Row className="align-items-center mt-3">
                    <Col xs={1} className="d-flex justify-content-center">
                      <i
                        className="bx bx-location-plus"
                        style={{ fontSize: "24px", color: "#818995" }}
                      ></i>
                    </Col>
                    <Col xs={10} className="ps-0">
                      <Form.Control
                        type="text"
                        name="storeLocation"
                        placeholder="Location"
                        disabled={true}
                        value={detailsFormData?.storeLocation || ""}
                        onChange={handleAddFormChange}
                        className="border-0"
                      />
                    </Col>
                  </Row>
                </Card.Body>
              </Card>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>Select Address</Form.Label>
                  <GooglePlacesAutocomplete
                    apiKey={import.meta.env.VITE_GOOGLE_PLACES_API_KEY}
                    apiOptions={{ language: "en" }}
                    selectProps={{
                      placeholder: "Address",
                      onChange: handleAddressChange,
                      value: detailsFormData?.address
                        ? prepareOption(detailsFormData?.address, detailsFormData?.address)
                        : null,
                      className: err?.address ? "is-invalid form-control" : "",
                    }}
                    autocompletionRequest={{
                      componentRestrictions: { country: adminCountries },
                    }}
                    debounce={500}
                    withSessionToken={true}
                  />
                  {err?.address && (
                    <div className="invalid-feedback d-block">
                      {err?.address}
                    </div>
                  )}
                </Form.Group>
              </Col>
              {/* <Form.Label>Address</Form.Label>
                <Form.Control
                  type="text"
                  name="address"
                  placeholder="Address"
                  className="mb-3"
                  value={detailsFormData?.address || ""}
                  onChange={handleAddFormChange}
                  isInvalid={err?.validationErrors?.address}
                />
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.address}
                </Form.Control.Feedback> */}
              {/* <Col className="mb-3">
                    <Form.Label>Address 2</Form.Label>
                    <Form.Control
                      type="text"
                      name="address2"
                      placeholder="Address 2"
                      value={detailsFormData?.address2 || ""}
                      onChange={handleAddFormChange}
                      isInvalid={err?.validationErrors?.address2}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.address2}
                    </Form.Control.Feedback>
                  </Col> */}
              <Col>
                <Row>
                  {/* <Col xl={4} className="mb-3">
                        <Form.Label>Latitude</Form.Label>
                        <Form.Control
                          disabled
                          type="text"
                          name="latitude"
                          placeholder="Latitude"
                          value={detailsFormData?.latitude || ""}
                          onChange={handleAddFormChange}
                          isInvalid={err?.validationErrors?.latitude}
                        />
                        <Form.Control.Feedback className="invalid-feedback">
                          {err?.validationErrors?.latitude}
                        </Form.Control.Feedback>
                      </Col>
                      <Col xl={4} className="mb-3">
                        <Form.Label>Longitude</Form.Label>
                        <Form.Control
                          disabled
                          type="text"
                          name="longitude"
                          placeholder="Longitude"
                          value={detailsFormData?.longitude || ""}
                          onChange={handleAddFormChange}
                          isInvalid={err?.validationErrors?.longitude}
                        />
                        <Form.Control.Feedback className="invalid-feedback">
                          {err?.validationErrors?.longitude}
                        </Form.Control.Feedback>
                      </Col> */}
                  <Col xl={4} className="mb-3">
                    <Form.Label>Zip Code</Form.Label>
                    <Form.Control
                      // disabled
                      type="text"
                      name="zipcode"
                      placeholder="Zip Code"
                      value={detailsFormData?.zipcode || detailsFormData?.store?.zipCode || ""}
                      onChange={handleAddFormChange}
                      isInvalid={err?.validationErrors?.zipcode}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.zipcode}
                    </Form.Control.Feedback>
                  </Col>
                </Row>
              </Col>
              {/* <Col className="mb-3">
                    <Row>
                      <Col xl={4} className="mb-3">
                        <Form.Label>Country</Form.Label>
                        <Select
                          isSearchable
                          classNamePrefix="Select2"
                          name="country"
                          placeholder="Country"
                          options={countryOptions}
                          value={
                            detailsFormData?.country
                              ? prepareOption(
                                detailsFormData.country?.name,
                                detailsFormData.country
                              )
                              : ""
                          }
                          onChange={handleOptionsFormChange}
                        // isInvalid={err?.validationErrors?.country}
                        />
                        <Form.Control.Feedback className="invalid-feedback">
                          {err?.validationErrors?.country}
                        </Form.Control.Feedback>
                      </Col>
                      <Col xl={4} className="mb-3">
                        <Form.Label>State</Form.Label>
                        <Select
                          isDisabled={!detailsFormData.country}
                          isSearchable
                          classNamePrefix="Select2"
                          name="state"
                          placeholder="State"
                          options={stateFormOptions}
                          value={
                            detailsFormData?.state
                              ? prepareOption(
                                detailsFormData.state?.name,
                                detailsFormData.state
                              )
                              : ""
                          }
                          onChange={handleOptionsFormChange}
                        // isInvalid={err?.validationErrors?.state}
                        />
                        <Form.Control.Feedback className="invalid-feedback">
                          {err?.validationErrors?.state}
                        </Form.Control.Feedback>
                      </Col>
                      <Col xl={4} className="mb-3">
                        <Form.Label>City</Form.Label>
                        <Select
                          isDisabled={!detailsFormData.state}
                          isSearchable
                          classNamePrefix="Select2"
                          name="city"
                          placeholder="City"
                          options={cityFormOptions}
                          value={
                            detailsFormData?.city
                              ? prepareOption(
                                detailsFormData.city?.name,
                                detailsFormData.city
                              )
                              : ""
                          }
                          onChange={handleOptionsFormChange}
                        // isInvalid={err?.validationErrors?.city}
                        />
                        <Form.Control.Feedback className="invalid-feedback">
                          {err?.validationErrors?.city}
                        </Form.Control.Feedback>
                      </Col>
                    </Row>
                  </Col> */}
              <Col className="mb-3" hidden={isAdd}>
                <Form.Label>URL</Form.Label>
                <InputGroup>
                  <InputGroupText
                    style={{
                      maxWidth: "700px",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {detailsFormData?.url}
                  </InputGroupText>
                  <Link
                    to={detailsFormData?.url}
                    target="_blank"
                  >
                    <OverlayTrigger overlay={<Tooltip>Open Link</Tooltip>} >
                      <Button variant="primary-light">
                        <span className="ri-external-link-line fs-14"></span>
                      </Button>
                    </OverlayTrigger>
                  </Link>
                </InputGroup>
              </Col>
              {/* <Col className="mb-3">
                <Form.Label>Viet ID</Form.Label>
                <Form.Control
                  type="number"
                  name="vietId"
                  placeholder="Viet ID"
                  value={detailsFormData?.vietId || 0}
                  onChange={handleAddFormChange}
                  isInvalid={err?.validationErrors?.vietId}
                />
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.vietId}
                </Form.Control.Feedback>
              </Col> */}
            </Card.Body>
          </Card>
        </Col>
        {isEdit &&
          <Col lg={expandSize}>
            <PostComments
              expandState={expandState}
              setExpandState={setExpandState}
              style={{ height: 'calc(100vh* 2/3 )' }}
            />
          </Col>
        }
      </Row >
      <Card className="custom-card">
        <Card.Header>
          <Card.Title>
            <InputGroup>
              <InputGroup.Text className="bg-info">
                <i className="bx bx-calendar" />
              </InputGroup.Text>
              <DatePicker
                className="bg-info-transparent "
                dateFormat="MMMM d, yyyy h:mm aa"
                showTimeSelect
                minDate={new Date()}
                minTime={
                  moment(detailsFormData.scheduledAt).startOf('day').diff(new Date()) > 0
                    ? moment(new Date()).startOf('day').toDate() : moment(new Date()).toDate()
                }
                maxTime={moment(new Date()).endOf('day').toDate()}
                timeIntervals={15}
                shouldCloseOnSelect={false}
                selected={detailsFormData.scheduledAt}
                todayButton="Select Today"
                isClearable
                onChange={(date) => setDetailsFormData((prev) => ({
                  ...prev, scheduledAt: date
                }))}
              />
            </InputGroup>
          </Card.Title>
          {
            isAdd ? (
              <Card.Subtitle>
                {detailsFormData.scheduledAt
                  ?
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.POST)}
                    className="me-2"
                    variant="info-light"
                    onClick={(event) => handleAddFormSubmit(event, true)}
                  >
                    Schedule<i className="bi bi-calendar-event ms-2"></i>
                  </Button>
                  :
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.POST)}
                    variant="primary-light"
                    onClick={handleAddFormSubmit}
                  >
                    Add<i className="bi bi-plus-lg ms-2"></i>
                  </Button>
                }
              </Card.Subtitle>
            ) : isEdit ? (
              <Card.Subtitle>
                <Button
                  hidden={!hasPermission(ACTION.DELETE, RESOURCE.POST)}
                  className="mx-2"
                  variant="danger-light"
                  onClick={handleDeleteClick}
                >
                  Delete<i className="bi bi-trash ms-2"></i>
                </Button>
                {detailsFormData.scheduledAt
                  ?
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.POST)}
                    className="me-2"
                    variant="info-light"
                    onClick={(event) => handleUpdateFormSubmit(event, true)}
                  >
                    Schedule<i className="bi bi-calendar-event ms-2"></i>
                  </Button>
                  :
                  <Button
                    hidden={!hasPermission(ACTION.UPDATE, RESOURCE.POST)}
                    variant="success-light"
                    onClick={handleUpdateFormSubmit}
                  >
                    Save<i className="bi bi-download ms-2"></i>
                  </Button>
                }
              </Card.Subtitle>
            ) : null
          }
        </Card.Header>
      </Card>
    </Fragment >
  );
};

export default ClassifiedPostDetails;
