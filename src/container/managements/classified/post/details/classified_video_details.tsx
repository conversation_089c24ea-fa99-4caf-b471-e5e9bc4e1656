import { FC, Fragment, useEffect, useRef, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Form, InputGroup, OverlayTrigger, Row, Table, Tooltip } from "react-bootstrap";
import { Link, useNavigate, useParams } from "react-router-dom";

import { useUploadMultipleMutation } from "../../../../../services/media";
import {
  useCreatePostMutation,
  useLazyDeletePostByIdQuery,
  useDeletePostLocaleTranslationMutation,
  // useLazyGetDataToCreateVideoPostQuery,
  // useLazyGetDataToUpdateVideoPostQuery,
  useUpdatePostMutation,
  useGeneratePostThumbnailMutation
} from "../../../../../services/post/post";

import { Timeline, TimelineAction, TimelineRow } from "@xzdarcy/react-timeline-editor";
import InputGroupText from "react-bootstrap/esm/InputGroupText";
import ReactQuill from "react-quill";
import LazySelect from "../../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../../components/table-title/card-header-with-back";
// import { useLazySelectCollectionQuery } from "../../../../../services/collection";
import { useLazySelectPostCategoryQuery } from "../../../../../services/post-category";
// import { useLazySelectProductQuery } from "../../../../../services/product";
import { useLazySelectProductVariantQuery } from "../../../../../services/product-variant";
import { store } from "../../../../../services/rtk/store";
import { hasPermission } from "../../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../../utils/constant/authorization";
import { EPostType } from "../../../../../utils/constant/post";
import { DEFAULT_LOCALE, LANGUAGES, TRANSLATIONS, TRANSLATIONS_ENUM } from "../../../../../utils/constant/translation";
import { getAllErrorMessages } from "../../../../../utils/errors";
import '../../zurno-post/details/video.module.css';
import DatePicker from 'react-datepicker'
import moment from "moment";
import axios, { AxiosRequestConfig } from "axios";
import { TRACKING_ACTION } from "../../../../../utils/constant/tracking";
import PostComments from "../../zurno-post/details/post_comments";
import VideoDropzone from "../../zurno-post/details/video_dropzone";
import Swal from "sweetalert2";
import { NumericFormat } from "react-number-format";

interface ClassifiedVideoDetailsProps {
  post: TPost | null
}

const ClassifiedVideoDetails: FC<
  ClassifiedVideoDetailsProps
> = ({ post }) => {
  const { id } = useParams();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});

  const [detailsFormData, setDetailsFormData] = useState<any>({});

  const [video, setVideo] = useState<TMedia | null>(null)
  const [videoFile, setVideoFile] = useState<File | null>(null);
  // const [mediasUploaded, setMediasUploaded] = useState<TMedia[]>([]);
  // const [mediasUploading, setMediasUploading] = useState<any>([]);
  const [thumbnailUploaded, setThumbnailUploaded] = useState<TMedia | null>(null);
  const [videoTrim, setVideoTrim] = useState({ start: '', end: '', })

  const [youtubeUrl, setYoutubeUrl] = useState("");

  const [locale, setLocale] = useState<TRANSLATIONS_ENUM | string>(DEFAULT_LOCALE)
  const [translations, setTranslations] = useState<any>({})

  // const [selectProduct] = useLazySelectProductQuery();
  // const [selectCollection] = useLazySelectCollectionQuery();
  const [resourceType, setResourceType] = useState("");
  const [product, setProduct] = useState<TProduct>();
  const [collection, setCollection] = useState<TCollection>();

  const [interactions, setInteractions] = useState<any | null>(null)
  const [addedToCart, setAddedToCart] = useState<{
    parentResourceId: string,
    resourceId: string,
    count: number,
    variant: TProductVariant,
  }[]>([])

  // const [getDataToCreate] = useLazyGetDataToCreateVideoPostQuery();
  // const [getDataToUpdate] = useLazyGetDataToUpdateVideoPostQuery();
  const [createPost] = useCreatePostMutation();
  const [updatePost] = useUpdatePostMutation();
  const [deletePost] = useLazyDeletePostByIdQuery();

  const [upload] = useUploadMultipleMutation();

  const navigate = useNavigate();
  const returnRoute = '/managements-marketplace/post'

  useEffect(() => {
    setIsAdd(false);
    setIsEdit(false);

    if (id == 'new-video') {
      setIsAdd(true)
    } else {
      setIsEdit(true);
    }
  }, [id]);

  const prepareReceivedData = (data) => {
    const details: any = {
      id: data.id,

      title: data.title,
      price: data.price,
      description: data.description,

      isFavourite: data.isFavourite,
      isUnlist: data.isUnlist,
      isDraft: data.isDraft,
      expired: data.expired,

      url: data.url,

      categories: data.categories,
      scheduledAt: data.scheduledAt ? new Date(data.scheduledAt) : null,

      type: data.type,

      countryId: data.countryId,
      stateId: data.stateId,
      cityId: data.cityId,

      latitude: data.latitude,
      longitude: data.longitude,
      address: data.address,
      address2: data.address2,
      zipcode: data.zipcode,

      user: data.user,
      contactName: data.contactName,
      phone: data.phone,
      email: data.email,

      vietId: data.vietId,
    };

    // if (data.thumbnailId) {
    //   setVideo(data.thumbnail);

    // }

    if (data.medias) {
      const videoMedia = data.medias.find((media) => media.type == 'video')
      setVideo(videoMedia)
      setVideoTrim({
        start: videoMedia?.start || '',
        end: videoMedia?.end || '',
      })
    }

    if (data.thumbnailId) {
      setThumbnailUploaded(data.thumbnail);
    }

    if (data.timelines) {
      const newEditorData = editorData

      for (const time of data.timelines) {


        // const rowData = newEditorData.find((row) => row.id == time.variantId)

        const newAction = {
          id: time.id,
          start: time.start,
          end: time.end,
          effectId: '',
          maxEnd: videoDuration,
          variant: time.variant,
        }

        newEditorData[0].actions.push(newAction)

        // if (!rowData) {
        //   newEditorData.push({
        //     id: time.variantId,
        //     actions: [newAction]
        //   })
        // } else {
        //   rowData.actions.push(newAction)
        //   newEditorData.map((row) => row.id != time.variantId ? row : {
        //     ...rowData,
        //     actions: rowData.actions
        //   })
        // }
      }

      setEditorData(newEditorData)
      setEditorChange(!editorChange)
    }

    if (data.youtubeUrl) {
      setYoutubeUrl(data.youtubeUrl);
    }

    if (data.productId) {
      setResourceType("product");
      setProduct(data.product);
    } else if (data.collectionId) {
      setResourceType('collection')
      setCollection(data.collection)
    }

    if (data.store) {
      details.store = data.store;
      details.storeName = data.store.name || "";
      details.storePhone = data.store.phoneNumber || "";
      details.storeLocation = data.store.address || "";
    }

    details.originLocale = data.originLocale
    if (data.translations?.length > 0) {
      const translates = {}
      data.translations.map((translate) => {
        translates[translate.locale] = {
          ...translates[translate.locale],
          [translate.field]: translate,
        }
      })

      setLocale(data.translations[0].locale)
      setTranslations(translates)
    }

    if (data.interactions) {
      setInteractions(data.interactions)
    }

    if (data.addedToCart) {
      let addedToCartData = [...data.addedToCart]

      if (data.timelines) {
        // remove timeline variant duplicates
        const timelineVariants: TProductVariant[] = []
        for (const timeline of data.timelines) {
          if (!timelineVariants.find(variant => variant.id == timeline.variantId)) {
            timelineVariants.push(timeline.variant)
          }
        }

        // // convert timeline [{...variant,product}] to [{...product,variants}]
        // // add onTimeline annotation to know the variant is on timeline
        // let timelineProducts: any[] = []
        // for (const variant of timelineVariants) {
        //   const product = timelineProducts.find(prod => prod.id == variant.productId)
        //   if (!product) {
        //     timelineProducts.push({
        //       ...variant.product,
        //       variants: [{ ...variant, onTimeline: true }]
        //     })
        //   } else {
        //     timelineProducts = timelineProducts.map(prod => prod.id != variant.productId ? prod : ({
        //       ...prod,
        //       variants: [...prod.variants, { ...variant, onTimeline: true }]
        //     }))
        //   }
        // }

        // // merge timeline products with addedToCart products
        // for (const product of timelineProducts) {
        //   if (!addedToCartData.find(datum => datum.product?.id == product.id)) {
        //     addedToCartData.push({
        //       count: 0,
        //       product
        //     })
        //   } else {
        //     addedToCartData = addedToCartData.map(datum => datum.product?.id != product.id ? datum : ({
        //       ...datum,
        //       product: {
        //         ...datum.product,
        //         variants: datum.product?.variants
        //           ? [...datum.variants, ...product.variants]
        //           : [...product.variants]
        //       }
        //     }))
        //   }
        // }

        // merge timeline variants with addedToCart variants
        for (const variant of timelineVariants) {
          if (!addedToCartData.find(datum => datum.resourceId == variant.id)) {
            addedToCartData.push({
              resourceId: variant.id,
              count: 0,
              variant,
            })
          }
        }
      }

      setAddedToCart(addedToCartData)
    }

    setDetailsFormData(details);
  };

  useEffect(() => {
    switch (true) {
      // case isAdd: {
      //   setIsLoading(true);
      //   getDataToCreate({})
      //     .unwrap()
      //     .then((res) => {
      //       res
      //       // prepareReceivedInfo(res.info);
      //     })
      //     .catch((error) => {
      //       setErr(getAllErrorMessages(error));
      //     })
      //     .finally(() => {
      //       setIsLoading(false);
      //     });
      //   break;
      // }
      case isEdit: {
        if (post) { prepareReceivedData(post) }
        // setIsLoading(true);
        // getDataToUpdate(id || "")
        //   .unwrap()
        //   .then((res) => {
        //     // prepareReceivedInfo(res.info);
        //     prepareReceivedData(res.data);
        //   })
        //   .catch((error) => {
        //     setErr(getAllErrorMessages(error));
        //   })
        //   .finally(() => {
        //     setIsLoading(false);
        //   });
        break;
      }
    }
  }, [isAdd, isEdit]);

  const handleDetailsFormChange = (event: any) => {
    if (event) {
      event.preventDefault();
    }

    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.value;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData);

    if (err?.validationErrors) {
      if (err.validationErrors[fieldName]) {
        delete err.validationErrors[fieldName];
      }
    }
  };

  const handCheckFormChange = (event: any) => {
    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.checked ? 1 : 0;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    if (fieldName == 'isDraft') {
      newFormData.scheduledAt = null
    }

    setDetailsFormData(newFormData);
  };

  // const [isUploading, setIsUploading] = useState(false)
  // const handleThumbnailUpload = async (file: File) => {
  //   try {
  //     setIsUploading(true)
  //     let uploadedLogo
  //     await upload({ file: [file] })
  //       .unwrap()
  //       .then((video) => [uploadedLogo] = video)
  //       .finally(() => { setIsUploading(false) })
  //       ;
  //     setVideoFile(uploadedLogo);
  //   } catch (error: any) {
  //     setErr(getAllErrorMessages(error));
  //   }
  // };

  const removeThumbnail = () => {
    setVideo(null)
    setVideoFile(null);
  };

  // const handleFilesMediaChange = async (fileList: File[]) => {
  //   const validFiles = fileList.filter((file) => file);
  //   if (validFiles.length > 0) {
  //     upload({ file: validFiles })
  //       .unwrap()
  //       .then((uploadedFiles) => {
  //         setMediasUploaded((prev) => [...prev, ...uploadedFiles]);
  //         setMediasUploading([]);
  //       })
  //       .catch((error) => {
  //         const formattedErrors = error?.data?.errors?.reduce(
  //           (acc: any, curr: any) => {
  //             acc[curr.field] = curr.message;
  //             return acc;
  //           },
  //           {}
  //         );
  //         setErr(formattedErrors);
  //       });
  //   }
  // };

  // const removeFile = (index: number) => {
  //   if (index < mediasUploading.length) {
  //     setMediasUploading((prev) => prev.filter((_, i) => i !== index));
  //   } else {
  //     const uploadedIndex = index - mediasUploading.length;
  //     setMediasUploaded((prev) => prev.filter((_, i) => i !== uploadedIndex));
  //   }
  // };

  // useEffect(() => {
  //   handleFilesMediaChange(mediasUploading);
  // }, [mediasUploading]);

  const [quillInput, setQuillInput] = useState(false)
  const handleTranslationChange = (field: string, value: string) => {
    if (!quillInput && field == 'description') { return }

    const newTranslations = { ...translations }

    newTranslations[locale] = {
      ...newTranslations[locale]
    }
    if (field == 'title & description') {
      const lineBreakIdx = value?.indexOf('\n')
      newTranslations[locale].title = {
        ...newTranslations[locale].title,
        locale,
        field: 'title',
        value: value?.slice(0, lineBreakIdx)
      }

      newTranslations[locale].description = {
        ...newTranslations[locale].description,
        locale,
        field: 'description',
        value: value?.slice(lineBreakIdx)
      }

    } else {
      newTranslations[locale][field] = {
        ...newTranslations[locale][field],
        locale,
        field,
        value,
      }
    }

    setTranslations(newTranslations)
  }

  const [missingVideo, setMissingVideo] = useState(false)

  const prepareSubmitForm = async (detailsFormData: any) => {
    const preparedForm: any = {
      type: detailsFormData?.type || EPostType.VIDEO,

      title: detailsFormData?.title,
      price: detailsFormData?.price,
      description: detailsFormData?.description,

      isFavourite: Number(detailsFormData?.isFavourite) || 0,
      isUnlist: Number(detailsFormData?.isUnlist) || 0,
      isDraft: Number(detailsFormData?.isDraft) || 0,
      expired: Number(detailsFormData?.expired) || 0,

      categoryIds: detailsFormData?.categories?.map((op: any) => {
        return op.id;
      }),

      storeId: detailsFormData?.store?.id,

      resourceType: resourceType,
      productId: product?.id,
      collectionId: collection?.id,

      latitude: detailsFormData?.latitude || detailsFormData?.store?.latitude,
      longitude: detailsFormData?.longitude || detailsFormData?.store?.longitude,
      address: detailsFormData?.address || detailsFormData?.store?.address,
      address2: detailsFormData?.address2 || detailsFormData?.store?.address2,
      zipcode: detailsFormData?.zipcode || detailsFormData?.store?.zipCode,

      countryId: detailsFormData?.countryId || detailsFormData?.store?.countryId,
      stateId: detailsFormData?.stateId || detailsFormData?.store?.stateId,
      cityId: detailsFormData?.cityId || detailsFormData?.store?.cityId,

      country: detailsFormData?.country,
      state: detailsFormData?.state,
      city: detailsFormData?.city,
      userId: detailsFormData?.user?.id,
      contactName: detailsFormData?.contactName,
      phone: detailsFormData?.phone,
      email: detailsFormData?.email,

      url: detailsFormData?.url,
      vietId: Number(detailsFormData?.vietId),
    };

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    preparedForm.timelines = []
    for (const data of editorData.flatMap(data => data.actions)) {
      preparedForm.timelines.push({
        id: uuidRegex.test(data.id) ? data.id : null,
        // @ts-ignore
        variantId: data.variant.id,
        start: data.start,
        end: data.end,
      })
    }

    if (videoFile) {
      const [uploadedVideo] = await upload({ file: [videoFile] }).unwrap()
      preparedForm.mediaIds = [uploadedVideo.id]
    }

    preparedForm.video = videoTrim

    if (thumbnailUploaded) {
      preparedForm.thumbnailId = thumbnailUploaded.id;
    }

    // if (mediasUploaded) {
    //   preparedForm.mediaIds = mediasUploaded.map((media) => media.id);
    // }

    if (youtubeUrl) {
      preparedForm.youtubeUrl = youtubeUrl;
    }

    preparedForm.originLocale = detailsFormData.originLocale
    if (translations) {
      preparedForm.translations = Object.values(translations).flatMap((translates: any) => Object.values(translates));
    }

    const currentAdmin = store.getState().auth.user
    preparedForm.createdByAdminId = currentAdmin?.id

    return preparedForm;
  };


  const handleAddFormSubmit = async (event: any, scheduled?: boolean) => {
    if (event) { event.preventDefault(); }

    if (!video && !videoFile) {
      setMissingVideo(true)
      return
    }

    setIsLoading(true);

    const newPost = await prepareSubmitForm(detailsFormData);

    if (scheduled) {
      newPost.isDraft = 1
      newPost.scheduledAt = detailsFormData.scheduledAt
    }

    createPost({ ...newPost })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        navigate(returnRoute);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleUpdateFormSubmit = async (event: any, scheduled?: boolean) => {
    if (event) { event.preventDefault(); }

    if (!video && !videoFile) {
      setMissingVideo(true)
      return
    }

    setIsLoading(true);

    const updatedPost = await prepareSubmitForm(detailsFormData);

    if (scheduled) {
      updatedPost.isDraft = 1
      updatedPost.scheduledAt = detailsFormData.scheduledAt
    }

    updatePost({ id: detailsFormData.id, ...updatedPost })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        navigate(returnRoute);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleDeleteClick = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: deletePost,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => navigate(returnRoute)),
      finalAction: (() => setIsLoading(false)),
    })
  };

  const [deleteTranslations] = useDeletePostLocaleTranslationMutation()
  const handleTranslationDelete = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: () => deleteTranslations({ postId: id || "", locale: locale as TRANSLATIONS_ENUM }),
      confirmText: `Do you want to delete the ${LANGUAGES[locale]} translation?`,
      finishText: `${LANGUAGES[locale]} translation deleted!`,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => {
        const newTranslations = { ...translations }
        delete newTranslations[locale]
        setLocale(Object.keys(newTranslations)[0])
        setTranslations(newTranslations)
      }
      ),
      finalAction: (() => setIsLoading(false)),
    })
  }

  const [selectPostCategories] = useLazySelectPostCategoryQuery()

  // const [media, setMedia] = useState<TMedia | null>(null)
  // const [videos, setVideos] = useState<TMedia[]>([])
  const playerStateRef = useRef<any>(null)
  const timelineStateRef = useRef<any>(null);
  // const [isPlaying, setIsPlaying] = useState(false);
  const [time, setTime] = useState(0);

  const [scale, setScale] = useState(30);
  const [videoDuration, setVideoDuration] = useState(0)
  const scaleCount = Math.ceil(videoDuration / scale) || 10
  const scaleSplitCount = 10
  const scaleWidthCalculation = ((document.getElementById('timeline-card')?.offsetWidth || 100) - 60) / scaleCount
  const scaleWidth = Math.max(scaleWidthCalculation, 80)

  useEffect(() => {
    setMissingVideo(false)

    if (videoDuration <= 60) {
      setScale(10)
    } else {
      setScale(30)
    }

    const newData =
      editorData.map((row) => ({
        ...row,
        actions: row.actions.map((action) => ({
          ...action,
          end: action.end > videoDuration ? videoDuration : action.end,
          maxEnd: videoDuration
        }))
      }))

    setEditorData(newData)
    setEditorChange(!editorChange)
  }, [videoDuration])

  useEffect(() => {
    if (!timelineStateRef.current) return;
    const engine = timelineStateRef.current;
    // engine.listener.on("play", () => { setIsPlaying(true) });
    // engine.listener.on("paused", () => { setIsPlaying(false) });
    engine.listener.on("afterSetTime", ({ time }) => { setTime(time) });
    engine.listener.on("setTimeByTick", ({ time }) => { setTime(time); });

    // return () => {
    //   if (!engine) return;
    //   engine.pause();
    //   engine.listener.offAll();
    // };
  }, []);


  const [selectProductVariant] = useLazySelectProductVariantQuery()

  // const handlePlayOrPause = () => {
  //   if (!timelineStateRef.current) { return };

  //   if (timelineStateRef.current.getTime() == videoDuration) {
  //     playerStateRef.current?.seekTo(0)
  //     timelineStateRef.current.setTime(0)
  //   }

  //   if (timelineStateRef.current.isPlaying) {
  //     setIsPlaying(false)
  //     timelineStateRef.current.pause();
  //   } else {
  //     setIsPlaying(true)
  //     timelineStateRef.current.play({ toTime: videoDuration });
  //   }
  // };

  // const timeRender = (time) => {
  //   const float = (parseInt((time % 1) * 100 + "") + "").padStart(2, "0");
  //   const min = (parseInt(time / 60 + "") + "").padStart(2, "0");
  //   const second = (parseInt((time % 60) + "") + "").padStart(2, "0");
  //   return <>{`${min}:${second}.${float.replace("0.", "")}`}</>;
  // };

  const [editorData, setEditorData] = useState<TimelineRow[]>([{ id: 'row', actions: [] }])
  const [editorChange, setEditorChange] = useState(false)

  useEffect(() => {
    timelineStateRef.current?.setTime(time)

    const totalVariantTime = editorData[0]
      .actions.reduce((a, b) => a + (b.end - b.start), 0)
    let combinedVariantTime = 0

    const newActions = editorData[0]
      .actions
      .sort((a, b) => a.start - b.start)
      .map((action) => {
        const minStart = combinedVariantTime
        combinedVariantTime += (action.end - action.start)
        const maxEnd = videoDuration - totalVariantTime + combinedVariantTime
        return {
          ...action,
          minStart,
          maxEnd,
        }
      })

    const newEditorData = [{
      ...editorData[0],
      actions: newActions
    }]

    setDisplayedVariants(
      // newEditorData
      editorData
        .flatMap(data => data.actions)
        .filter(action => action.start <= time && time <= action.end)
        .map((data: any) => data.variant as TProductVariant)
    )

    setEditorData(newEditorData)

  }, [time, editorChange])

  const [displayVariants, setDisplayedVariants] = useState<TProductVariant[]>([])
  const [variantToAdd, setVariantToAdd] = useState<TProductVariant | null>(null)
  const [clearSelect, setClearSelect] = useState(false)
  const handleAddClick = () => {
    if (!variantToAdd) { return }

    const newEditorData = editorData

    // const rowData = newEditorData.find((data) => data.id == variantToAdd.id)

    const begin = time - (scale / 2)
    const start = begin < 0 ? 0 : begin
    const final = time + (scale / 2)
    const end = final > videoDuration ? videoDuration : final

    const newAction = {
      id: `${Math.random()}`,
      start,
      end,
      effectId: '',
      maxEnd: videoDuration,
      variant: variantToAdd,
    }

    newEditorData[0].actions.push(newAction)

    // if (!rowData) {

    //   newEditorData.push({
    //     id: variantToAdd.id,
    //     actions: [newAction]
    //   })
    // } else {
    //   rowData.actions.push(newAction)
    //   newEditorData.map((data) => data.id != variantToAdd.id ? data : {
    //     ...data,
    //     actions: rowData.actions
    //   })
    // }

    setEditorData(newEditorData)

    setEditorChange(!editorChange)

    setVariantToAdd(null)

    setClearSelect(true)
  }

  // const [playbackRate, setPlaybackRate] = useState(1)

  // const handleDoubleClick = (event: any, param: { action: TimelineAction; row: TimelineRow; time: number; }) => {
  //   if (event) { event.preventDefault() }

  //   // const newRowActions = param.row.actions.filter((action) => action.id != param.action.id)
  //   // const newRow = {
  //   //   ...param.row,
  //   //   actions: newRowActions
  //   // }

  //   // setEditorData((prev) => newRowActions.length > 0
  //   //   ? prev.map((row) => row.id != param.row.id ? row : newRow)
  //   //   : prev.filter((row) => row.id != param.row.id)
  //   // )

  //   const newActions = editorData[0].actions.filter((action) => action.id != param.action.id)
  //   const newRow = {
  //     ...editorData[0],
  //     actions: newActions
  //   }

  //   setEditorData([newRow])
  //   setEditorChange(!editorChange)

  // }

  const handleRemoveClick = (action: TimelineAction) => {
    const newActions = editorData[0].actions.filter((act) => act.id != action.id)
    const newRow = {
      ...editorData[0],
      actions: newActions
    }

    setEditorData([newRow])
    setEditorChange(!editorChange)
  }

  const handleActionMoving = (params: { action: TimelineAction; row: TimelineRow; start: number; end: number; }) => {
    actionChanging(params)

    setEditorData([params.row])
    setEditorChange(!editorChange)
  }

  const handleActionResizing = (params: { action: TimelineAction; row: TimelineRow; start: number; end: number; dir: "right" | "left"; }) => {
    actionChanging(params)

    setEditorData([params.row])
    setEditorChange(!editorChange)
  }

  const actionChanging = (params: {
    action: TimelineAction; row: TimelineRow; start: number; end: number; dir?: "right" | "left";
  }) => {
    const direction = params.dir ? params.dir :
      params.start - params.action.start > 0 ? 'right' : 'left'

    switch (direction) {
      case 'right': {
        let idx
        params.row.actions = params.row.actions.map((action, index) => {
          if (action.id != params.action.id) {
            return action
          } else {
            idx = index
            return {
              ...action,
              start: params.start,
              end: params.end,
            }
          }
        })

        if (idx < params.row.actions.length - 1) {
          const nextAction = editorData[0].actions[idx + 1]
          if (params.end >= nextAction.start) {
            const displace = params.end - nextAction.start
            actionChanging({ action: nextAction, row: params.row, start: nextAction.start + displace, end: nextAction.end + displace })
          }
        }
        break;
      }
      case 'left': {
        let idx
        params.row.actions = params.row.actions.map((action, index) => {
          if (action.id != params.action.id) {
            return action
          } else {
            idx = index
            return {
              ...action,
              start: params.start,
              end: params.end,
            }
          }
        })

        if (idx > 0) {
          const nextAction = editorData[0].actions[idx - 1]
          if (params.start <= nextAction.end) {
            const displace = params.start - nextAction.end
            actionChanging({ action: nextAction, row: params.row, start: nextAction.start + displace, end: nextAction.end + displace })
          }
        }
      }
    }
  }

  const videoCardRef = useRef(null)
  // @ts-ignore
  const videoCardHeight = videoCardRef.current?.getBoundingClientRect()?.height || 400

  const [appearState, setAppearState] = useState(true)
  const [totalComments, setTotalComments] = useState(0)

  const commentsSize = appearState ? 3 : 0
  const calculateMainSize = () => {
    return isAdd
      ? 12
      : 12 - commentsSize
  }
  const colRef = useRef(null)

  const [downloading, setDownloading] = useState(false)
  const handleDownloadClick = (event) => {
    if (event) { event.preventDefault() }

    setDownloading(true)
    const headers = { 'Content-Type': 'blob' };
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: video?.url,
      responseType: 'arraybuffer',
      headers
    };
    axios(config)
      .then((response) => {
        const url = URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.download = ((post?.title + "-") || "") + 'video.mp4'
        link.click();
      })
      .catch((error) => console.log(error))
      .finally(() => setDownloading(false))
  }

  const [generating, setGenerating] = useState(false)
  const [generateThumbnail] = useGeneratePostThumbnailMutation()
  const handleGenerateClick = (event) => {
    if (event) { event.preventDefault() }

    setGenerating(true)
    generateThumbnail({ postId: id || "" })
      .unwrap()
      .then((res) => {
        setGenerating(false)
        setThumbnailUploaded(res)
        Swal.fire({
          icon: 'success',
          title: 'Thumbnail generated',
          html: `
            <p class="bg-dark-transparent">
              <img                
                src="${res?.url}"
                alt="img"
                style="display: block; margin: 0px auto; max-width: 100%; height: 100%; object-fit: cover;"
              />
            </p>
          `
        })
      })
      .catch((error) => Swal.fire('Error!', getAllErrorMessages(error).messages[0], 'error'))
      .finally(() => setGenerating(false))
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col ref={colRef} lg={calculateMainSize()}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Post Details"
                route={returnRoute}
              ></CardHeaderWithBack>
              {video &&
                <Card.Subtitle>
                  <Button
                    hidden={isAdd}
                    disabled={generating}
                    onClick={handleGenerateClick}
                    variant="primary-light"
                    className="me-2"
                  >
                    {post?.thumbnailId
                      ? "Regenerate"
                      : "Generate"
                    }
                    {!generating
                      ? <i className="bi bi-image ms-2" />
                      : <i className="spinner-border spinner-border-sm align-middle ms-2 me-0" />
                    }
                  </Button>
                  <Button
                    disabled={downloading}
                    variant="success-light"
                    onClick={handleDownloadClick}
                  >
                    Download
                    {!downloading
                      ? <i className="bi bi-file-earmark-play ms-2" />
                      : <i className="spinner-border spinner-border-sm align-middle ms-2" />
                    }
                  </Button>
                </Card.Subtitle>
              }
              {interactions && <Card.Subtitle>
                <Row>
                  <Col>
                    <div>
                      <span className="ri-eye-line mx-1" />
                      {interactions[TRACKING_ACTION.VIEW_POST]?.count || 0}
                    </div>
                    <div>
                      <span className="ri-share-line mx-1" />
                      {interactions[TRACKING_ACTION.SHARE]?.count || 0}
                    </div>
                  </Col>
                  <Col>
                    <div>
                      <span className="ri-heart-line mx-1" />
                      {interactions[TRACKING_ACTION.ADD_WISHLIST]?.count || 0}
                    </div>
                    <div
                      className={appearState ? "" : "text-info"}
                      onClick={() => setAppearState(!appearState)}
                      style={{ cursor: 'pointer' }}
                    >
                      <span className="ri-chat-1-line mx-1" />
                      {totalComments}
                    </div>
                  </Col>
                </Row>
              </Card.Subtitle>
              }
            </Card.Header>
            <div>
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}
            </div>
            <Card.Body>
              <Form className="mb-3">
                <Form.Control
                  type="text"
                  required
                  name="title"
                  placeholder="Title"
                  value={detailsFormData?.title || ""}
                  onChange={handleDetailsFormChange}
                  isInvalid={err?.validationErrors?.title}
                />
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.title}
                </Form.Control.Feedback>
              </Form>
            </Card.Body>
          </Card>
          <Card className="custom-card">
            <Card.Body ref={videoCardRef}>
              <VideoDropzone
                innerRef={playerStateRef}
                // playing={isPlaying}
                // playbackRate={playbackRate}
                uploadedLogo={video}
                // uploadFunction={handleThumbnailUpload}
                removeLogo={removeThumbnail}
                setVideoFile={setVideoFile}
                setTime={setTime}
                setDuration={setVideoDuration}
                // isUploading={isUploading || false}
                styles={{
                  dropzone: {
                    width: "auto",
                    height: "360px",
                  },
                  preview: {
                    width: "auto",
                    height: "auto",
                  },
                }}
                acceptType={['video/*']}
              />
              {missingVideo &&
                <span className="text-danger">
                  Please upload a video.
                </span>
              }
            </Card.Body>
            <Card.Footer>
              {displayVariants.map((variant, index) => (
                <Fragment key={index}>
                  <div className="d-flex align-items-center">
                    <div className="me-3 lh-1">
                      <span className="avatar avatar-xxl bd-gray-200">
                        <img src={variant?.image?.src || ""} alt="" />
                      </span>
                    </div>
                    <div>
                      <div
                        className="mb-1 fs-14 fw-semibold">
                        {variant?.product?.title}
                      </div>
                      <div
                        className="mb-1">
                        {variant?.title}
                      </div>
                      <div
                        className="mb-1">
                        {variant?.sku}
                      </div>
                    </div>
                  </div>
                </Fragment>
              ))}
            </Card.Footer>
          </Card>
          <Card className="custom-card">
            <Card.Body>
              <Form.Label>Variant</Form.Label>
              <InputGroup>
                <div style={{ width: '-webkit-fill-available' }}>
                  <LazySelect
                    selectionFunction={selectProductVariant}
                    label={value => value.title}
                    clearSelected={[clearSelect, setClearSelect]}
                    getSelectedOptions={setVariantToAdd}
                  />
                </div>
                <Button onClick={handleAddClick} disabled={!(video || videoFile)}>
                  Add
                </Button>
              </InputGroup>
            </Card.Body>
            <Card.Body id="timeline-card">
              <Timeline
                style={{
                  width: 'auto',
                  height: '100px',
                }}
                ref={timelineStateRef}
                editorData={editorData}
                effects={{}}
                scale={scale}
                scaleWidth={scaleWidth}
                scaleSplitCount={scaleSplitCount}
                startLeft={20}
                minScaleCount={scaleCount}
                maxScaleCount={scaleCount + 1}
                getScaleRender={(scale) => {
                  const min = parseInt(scale / 60 + '');
                  const second = (scale % 60 + '').padStart(2, '0');
                  return <>{`${min}:${second}`}</>
                }}
                getActionRender={(action) => {
                  // @ts-ignore
                  const variant = action.variant as TProductVariant
                  return (
                    <OverlayTrigger
                      overlay={<Tooltip>
                        <div className="d-flex align-items-center">
                          <div className="me-3 lh-1">
                            <span className="avatar avatar-md bd-gray-200">
                              <img src={variant?.image?.src || ""} alt="" />
                            </span>
                          </div>
                          <div
                            className="mb-1 fs-14 fw-semibold">
                            {variant?.title}
                          </div>
                        </div>
                      </Tooltip>}
                    >
                      <div className="d-flex">
                        <span className="text-info">
                          {variant?.sku}
                        </span>
                        <i
                          className="bi bi-x-lg text-danger ms-auto me-2"
                          onClick={() => handleRemoveClick(action)}
                        />
                      </div>
                    </OverlayTrigger>
                  );
                }}
                onChange={(data) => {
                  setEditorData(data)
                  setEditorChange(!editorChange)
                }}
                onCursorDragEnd={(time) => {
                  playerStateRef.current?.seekTo(time)
                  if (time >= videoDuration) {
                    timelineStateRef.current?.setTime(videoDuration)
                  }
                }}
                onClickTimeArea={(time) => {
                  playerStateRef.current?.seekTo(time)
                  timelineStateRef.current?.setTime(time)
                  if (time >= videoDuration) {
                    timelineStateRef.current?.setTime(videoDuration)
                    return false
                  }
                }}
                onActionMoving={handleActionMoving}
                onActionResizing={handleActionResizing}
              // onDoubleClickAction={handleDoubleClick}
              />
            </Card.Body>
            <Card.Footer>
              <Row>
                <Col lg={6}>
                  <Form.Label>Start Time</Form.Label>
                  <InputGroup>
                    <OverlayTrigger overlay={<Tooltip>Set</Tooltip>}>
                      <Button
                        onClick={() => {
                          let startTime = time
                          const endTime = Number.parseFloat(videoTrim.end)

                          if (videoTrim.end && startTime > endTime) {
                            startTime = endTime
                          }

                          setVideoTrim({ ...videoTrim, start: `${startTime}` })
                        }}
                      >
                        <i className="bi-stopwatch" />
                      </Button>
                    </OverlayTrigger>
                    <NumericFormat
                      className="form-control"
                      value={videoTrim?.start || '0.00'}
                      onChange={(e) => setVideoTrim({ ...videoTrim, start: e.target.value })}
                      onBlur={(e) => {
                        let startTime = Number.parseFloat(e.target.value)
                        const endTime = Number.parseFloat(videoTrim.end)

                        if (videoTrim.end && startTime > endTime) {
                          startTime = endTime
                        } else if (startTime > videoDuration) {
                          startTime = videoDuration
                        }

                        setVideoTrim({ ...videoTrim, start: `${startTime}` })
                      }}
                      decimalScale={2}
                      allowNegative={false}
                    />
                    <OverlayTrigger overlay={<Tooltip>Clear</Tooltip>}>
                      <Button
                        disabled={!videoTrim.start}
                        variant="danger-light"
                        onClick={() => setVideoTrim({ ...videoTrim, start: '' })}
                      >
                        <i className="bi-eraser" />
                      </Button>
                    </OverlayTrigger>
                  </InputGroup>
                </Col>
                <Col lg={6}>
                  <Form.Label>End Time</Form.Label>
                  <InputGroup>
                    <OverlayTrigger overlay={<Tooltip>Set</Tooltip>}>
                      <Button
                        onClick={() => {
                          let endTime = time
                          const startTime = Number.parseFloat(videoTrim.start)

                          if (videoTrim.start && endTime < startTime) {
                            endTime = startTime
                          }

                          setVideoTrim({ ...videoTrim, end: `${endTime}` })
                        }}
                      >
                        <i className="bi-stopwatch" />
                      </Button>
                    </OverlayTrigger>
                    <NumericFormat
                      className="form-control"
                      value={videoTrim?.end || videoDuration}
                      onChange={(e) => setVideoTrim({ ...videoTrim, end: e.target.value })}
                      onBlur={(e) => {
                        let endTime = Number.parseFloat(e.target.value)
                        const startTime = Number.parseFloat(videoTrim.start)

                        if (videoTrim.start && endTime < startTime) {
                          endTime = startTime
                        }

                        setVideoTrim({ ...videoTrim, end: `${endTime}` })
                      }}
                      decimalScale={2}
                      allowNegative={false}
                    />
                    <OverlayTrigger overlay={<Tooltip>Clear</Tooltip>}>
                      <Button
                        disabled={!videoTrim.end}
                        variant="danger-light"
                        onClick={() => setVideoTrim({ ...videoTrim, end: '' })}
                      >
                        <i className="bi-eraser" />
                      </Button>
                    </OverlayTrigger>
                  </InputGroup>
                </Col>
              </Row>
            </Card.Footer>
          </Card>
        </Col>
        {isEdit &&
          <Col lg={commentsSize}>
            <Col >
              <PostComments
                // @ts-ignore
                style={{ height: colRef.current?.clientHeight - 250 || 'calc(100vh * 1/2)' }}
                appearState={appearState}
                setExpandState={setAppearState}
                getTotalComments={setTotalComments}
              />
            </Col>
          </Col>
        }
      </Row>
      <Row>
        <Col>
          <Card className="custom-card">
            <Card.Header hidden={isAdd}>
              <InputGroup>
                <InputGroupText>Original Locale</InputGroupText>
                <Form.Select
                  value={detailsFormData.originLocale || ""}
                  name="originLocale"
                  onChange={handleDetailsFormChange}
                >
                  <option value=''></option>
                  {TRANSLATIONS.map((translation, index) => (
                    <option key={index} value={translation}>
                      {LANGUAGES[translation]}
                    </option>
                  ))}
                </Form.Select>
              </InputGroup>
            </Card.Header>
            <Card.Body>
              <Form.Label>Description</Form.Label>
              <ReactQuill
                theme="snow"
                value={detailsFormData?.description || ""}
                onChange={(value) => setDetailsFormData((prev) => ({
                  ...prev,
                  description: value
                }))}
              />
            </Card.Body>
            {Object.entries(translations).length > 0 &&
              // <Card.Body>
              <Accordion hidden={!hasPermission(ACTION.ACCESS_TRANSLATION, RESOURCE.POST)}>
                <Accordion.Item eventKey="0">
                  <Accordion.Header>Translations</Accordion.Header>
                  <Accordion.Body>
                    <InputGroup>
                      <InputGroupText>Locale</InputGroupText>
                      <Form.Select
                        onChange={(e) => setLocale(e.target.value)}
                        value={locale}
                      >
                        {Object.keys(translations).map((translation, index) => (
                          <option key={index} value={translation}>
                            {LANGUAGES[translation]}
                          </option>
                        ))}
                      </Form.Select>
                      {/* <Button onClick={handleTranslateClick}>Translate</Button> */}
                      <Button
                        variant="danger-light"
                        className="btn btn-sm"
                        onClick={handleTranslationDelete}
                      >Delete</Button>
                    </InputGroup>
                  </Accordion.Body>
                  <Accordion.Body>
                    <Form.Label>Title</Form.Label>
                    <Form.Control
                      className="mb-3"
                      type="text"
                      placeholder="Title"
                      value={translations?.[locale]?.title?.value || ""}
                      onChange={(e) => handleTranslationChange('title', e.target.value)
                      }
                    // isInvalid={err?.validationErrors?.title}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.title}
                    </Form.Control.Feedback>
                    <Form.Label>Description</Form.Label>
                    <ReactQuill
                      theme="snow"
                      value={translations?.[locale]?.description?.value || ""}
                      onChange={(value) => handleTranslationChange('description', value)}
                      onKeyDown={() => setQuillInput(true)}
                    />
                  </Accordion.Body>
                </Accordion.Item>
              </Accordion>
              // </Card.Body>
            }
          </Card>
        </Col>
        <Col>
          <Card className="custom-card">
            <Card.Body>
              <Col className="mb-3">
                <Form.Label>Categories</Form.Label>
                <LazySelect
                  isMulti
                  selectionFunction={selectPostCategories}
                  label={(value) => value.name}
                  initialSelectedOptions={detailsFormData?.categories}
                  getSelectedOptions={(value) => setDetailsFormData((prev) => ({
                    ...prev,
                    categories: value
                  }))}
                />
              </Col>
              {/*
          <Row className="mb-3">
                <Form.Label>
                  Resource
                </Form.Label>
                <Col>
                  <Form.Select
                    value={resourceType}
                    onChange={(event) => setResourceType(event.target.value)}
                  >
                    <option value="">None</option>
                    <option value="product">Product</option>
                    <option value="collection">Collection</option>
                  </Form.Select>
                </Col>
                <Col>
                  <LazySelect
                    hidden={resourceType != 'product'}
                    selectionFunction={selectProduct}
                    label={(value) => value.title}
                    initialSelectedOptions={product}
                    getSelectedOptions={(value) => setProduct(value)}
                  />
                  <LazySelect
                    hidden={resourceType != 'collection'}
                    selectionFunction={selectCollection}
                    label={(value) => value.title}
                    initialSelectedOptions={collection}
                    getSelectedOptions={(value) => setCollection(value)}
                  />
                </Col>
              </Row> */}
              <Col className="my-3">
                <Row>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="isDraft"
                      label="Draft"
                      checked={detailsFormData?.isDraft ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.isDraft}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.isDraft}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="isFavourite"
                      label="Favourite"
                      checked={detailsFormData?.isFavourite ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.isFavourite}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.isFavourite}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="isUnlist"
                      label="Unlist"
                      checked={detailsFormData?.isUnlist ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.isUnlist}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.isUnlist}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Check
                      type="checkbox"
                      name="expired"
                      label="Expired"
                      checked={detailsFormData?.expired ? true : false}
                      onChange={handCheckFormChange}
                      isInvalid={err?.validationErrors?.expired}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.expired}
                    </Form.Control.Feedback>
                  </Col>
                </Row>
              </Col>
              <Col className="mb-3" hidden={isAdd}>
                <Form.Label>URL</Form.Label>
                <InputGroup>
                  <InputGroupText
                    style={{
                      maxWidth: "700px",
                      whiteSpace: "nowrap",
                      overflow: "hidden",
                      textOverflow: "ellipsis",
                    }}
                  >
                    {detailsFormData?.url}
                  </InputGroupText>
                  <Link
                    to={detailsFormData?.url}
                    target="_blank"
                  >
                    <OverlayTrigger overlay={<Tooltip>Open Link</Tooltip>} >
                      <Button variant="primary-light">
                        <span className="ri-external-link-line fs-14"></span>
                      </Button>
                    </OverlayTrigger>
                  </Link>
                </InputGroup>
              </Col>
            </Card.Body>
          </Card>
          {addedToCart.length > 0 &&
            <Card className="custom-card">
              <Card.Header>
                <Card.Title>Products Added To Cart</Card.Title>
              </Card.Header>
              <Card.Body className="p-0">
                <Table>
                  <thead>
                    <tr>
                      <th>Image</th>
                      <th>Title</th>
                      <th>Carted</th>
                    </tr>
                  </thead>
                  <tbody>
                    {addedToCart.map((add, index) => (
                      <tr key={index}>
                        <td className="me-3 lh-1">
                          <span className="avatar avatar-xl bd-gray-200">
                            <img src={add.variant?.image?.src || ""} alt="" />
                          </span>
                        </td>
                        <td>
                          <Link to={`/managements-products/${add.variant?.productId}`}>
                            <div className="mb-1 fs-14 fw-semibold">
                              {add.variant?.product?.title}
                            </div>
                            <div className="mb-1">
                              {add.variant?.title}
                            </div>
                            <div>
                              <span className="">{add.variant?.sku}</span>
                            </div>
                          </Link>
                        </td>
                        <td className="ms-auto fw-semibold">
                          <span className="ri-shopping-cart-line mx-1" />
                          {add.count || 0}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </Card.Body>
            </Card>
          }
        </Col>
      </Row>
      <Card className="custom-card">
        <Card.Header>
          <Card.Title>
            <InputGroup>
              <InputGroup.Text className="bg-info">
                <i className="bx bx-calendar" />
              </InputGroup.Text>
              <DatePicker
                className="bg-info-transparent "
                dateFormat="MMMM d, yyyy h:mm aa"
                showTimeSelect
                minDate={new Date()}
                minTime={
                  moment(detailsFormData.scheduledAt).startOf('day').diff(new Date()) > 0
                    ? moment(new Date()).startOf('day').toDate() : moment(new Date()).toDate()
                }
                maxTime={moment(new Date()).endOf('day').toDate()}
                timeIntervals={15}
                shouldCloseOnSelect={false}
                selected={detailsFormData.scheduledAt}
                todayButton="Select Today"
                isClearable
                onChange={(date) => setDetailsFormData((prev) => ({
                  ...prev, scheduledAt: date
                }))}
              />
            </InputGroup>
          </Card.Title>
          {
            isAdd ? (
              <Card.Subtitle>
                {detailsFormData.scheduledAt
                  ?
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.POST)}
                    className="me-2"
                    variant="info-light"
                    onClick={(event) => handleAddFormSubmit(event, true)}
                  >
                    Schedule<i className="bi bi-calendar-event ms-2"></i>
                  </Button>
                  :
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.POST)}
                    variant="primary-light"
                    onClick={handleAddFormSubmit}
                  >
                    Add<i className="bi bi-plus-lg ms-2"></i>
                  </Button>
                }
              </Card.Subtitle>
            ) : isEdit ? (
              <Card.Subtitle>
                <Button
                  hidden={!hasPermission(ACTION.DELETE, RESOURCE.POST)}
                  className="mx-2"
                  variant="danger-light"
                  onClick={handleDeleteClick}
                >
                  Delete<i className="bi bi-trash ms-2"></i>
                </Button>
                {detailsFormData.scheduledAt
                  ?
                  <Button
                    hidden={!hasPermission(ACTION.CREATE, RESOURCE.POST)}
                    className="me-2"
                    variant="info-light"
                    onClick={(event) => handleUpdateFormSubmit(event, true)}
                  >
                    Schedule<i className="bi bi-calendar-event ms-2"></i>
                  </Button>
                  :
                  <Button
                    hidden={!hasPermission(ACTION.UPDATE, RESOURCE.POST)}
                    variant="success-light"
                    onClick={handleUpdateFormSubmit}
                  >
                    Save<i className="bi bi-download ms-2"></i>
                  </Button>
                }
              </Card.Subtitle>
            ) : null
          }
        </Card.Header>
      </Card>
    </Fragment>
  );
};

export default ClassifiedVideoDetails;

