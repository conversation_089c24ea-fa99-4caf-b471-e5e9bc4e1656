import { FC, useState } from "react";
import Swal from "sweetalert2";
import { format } from "date-fns";
import { <PERSON><PERSON>, Button, Form, InputGroup, Modal } from "react-bootstrap";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import { useRegisterAndCheckInMutation } from "../../../../services/event";
import { ErrorType } from "../../../../utils/error_type";

interface EventRegisterModalProps {
  eventId: string;
  show: boolean;
  onHide: () => void;
  setIsLoading: (boolean) => void;
}

const EventRegisterModal: FC<EventRegisterModalProps> = ({ eventId, show, onHide, setIsLoading }) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    languages: ["vi"],
    occupation: "owner",
    otherOccupation: ""
  });
  const [errors, setErrors] = useState<any>({});
  const [err, setErr] = useState<ErrorType>();
  const [registerAndCheckIn] = useRegisterAndCheckInMutation();

  const handleCheckboxChange = (e) => {
    let languages = formData.languages;

    if (e.target.checked)
      languages.push(e.target.name);
    else
      languages = languages.filter(lg => lg !== e.target.name);

    setFormData({
      ...formData,
      languages
    });
  };

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      phone: "",
      languages: ["vi"],
      occupation: "owner",
      otherOccupation: ""
    });
    setErrors({});
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const data = {
      ...formData,
      occupation: formData.occupation !== 'other' ? formData.occupation : formData.otherOccupation
    }

    setErr({});
    setIsLoading(true);
    await registerAndCheckIn({
      eventId,
      ...data,
      source: "admin",
    }).unwrap()
      .then(() => {
        resetForm();
        Swal.fire("Register Successfully!", "", "success");
      })
      .catch(onError)
      .finally(() => { setIsLoading(false) });
  };

  const onError = (error: any) => {
    if (error.data?.success === false) {
      if (error.data?.message === 'Validation failure') {
        setErr({ messages: ['Some fields are required'] })
      } else if (error.data?.message === 'Validation failed') {
        setErr({ messages: [error.data?.errors[0].message] })
      } else {
        Swal.fire({
          title: "Registration Exists",
          html: `
              <div>
                <p>${error.data.message}</p>
                <p><strong>Name:</strong> ${error.data.data.name}</p>
                <p><strong>Email:</strong> ${error.data.data.email}</p>
                <p><strong>Phone:</strong> ${error.data.data.phone}</p>
                ${error.data.data.checkedIn
              ? `
                <p><strong>Checked in at:</strong> ${format(
                new Date(error.data.data.checkedInAt),
                "PPp"
              )}</p>
                `
              : ""
            }
              </div>
            `,
          icon: "warning",
        });
      }
    } else {
      console.error("Failed to register and check in:", error);
    }
  }

  return (
    <Modal show={show} onHide={onHide}>
      <Modal.Header closeButton>
        <Modal.Title>New Register</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {err?.messages?.map((message: string, index: number) => (
          <Alert key={index} variant="danger">
            {message}
          </Alert>
        ))}

        <Form>
          <Form.Group className="mb-3">
            <Form.Label>Name</Form.Label>

            <Form.Control
              type="text"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              isInvalid={!!errors.name}
              required
            />
            {errors.name && (
              <Form.Control.Feedback type="invalid">
                {errors.name}
              </Form.Control.Feedback>
            )}
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Email</Form.Label>
            <Form.Control
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              isInvalid={!!errors.email}
              required
            />
            {errors.email && (
              <Form.Control.Feedback type="invalid">
                {errors.email}
              </Form.Control.Feedback>
            )}
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Phone</Form.Label>
            <InputGroup hasValidation>
              <InputGroup.Text>
                <i className="bx bx-phone"></i>
              </InputGroup.Text>
              <Form.Control
                type="tel"
                value={formData.phone}
                onChange={(e) => {
                  const value = e.target.value.replace(/[^0-9+]/g, "");
                  if (value.length <= 15) {
                    setFormData({ ...formData, phone: value });
                  }
                }}
                isInvalid={!!errors.phone}
                required
                placeholder="Phone number"
              />
              {errors.phone && (
                <Form.Control.Feedback type="invalid">
                  {errors.phone}
                </Form.Control.Feedback>
              )}
            </InputGroup>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Languages</Form.Label>

            <InputGroup className="mb-3">
              <Form.Check
                type="checkbox"
                label="English"
                name="en"
                className="ms-4"
                checked={formData.languages.includes('en')}
                onChange={handleCheckboxChange}
              />
              <Form.Check
                type="checkbox"
                label="Vietnamese"
                name="vi"
                className="ms-4"
                checked={formData.languages.includes('vi')}
                onChange={handleCheckboxChange}
              />
              <Form.Check
                type="checkbox"
                label="Spanish"
                name="es"
                className="ms-4"
                checked={formData.languages.includes('es')}
                onChange={handleCheckboxChange}
              />
            </InputGroup>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Occupation</Form.Label>

            <Form.Select
              name="occupation"
              value={formData.occupation}
              onChange={(e) => setFormData({ ...formData, occupation: e.target.value })}
              className="mb-3"
            >
              <option value='owner'>Salon Owner</option>
              <option value='technician'>Technician</option>
              <option value='other'>Other</option>
            </Form.Select>

            <Form.Control
              hidden={formData.occupation !== 'other'}
              type="text"
              value={formData.otherOccupation}
              onChange={(e) => setFormData({ ...formData, otherOccupation: e.target.value })}
            />
          </Form.Group>

          <div className="d-flex justify-content-end gap-2">
            <Button
              hidden={
                !hasPermission(ACTION.ADD_REGISTRATION, RESOURCE.EVENT) ||
                !hasPermission(ACTION.CHECK_IN, RESOURCE.EVENT)
              }
              variant="primary"
              onClick={handleSubmit}
            >
              Register & Check in
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default EventRegisterModal;