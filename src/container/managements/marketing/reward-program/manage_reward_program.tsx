import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON>ton, Card, OverlayTrigger, Table, Tooltip } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import Card<PERSON>eaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useDeleteRewardProgramMutation, useLazyListRewardProgramsQuery } from "../../../../services/reward-program";
import { EProgramRewardType, ERewardProgramStatusName } from "../../../../utils/constant/reward-program";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";

interface ManagementRewardProgramProps { }

const ManagementRewardProgram: FC<ManagementRewardProgramProps> = () => {

  const navigate = useNavigate()

  const [listPrograms] = useLazyListRewardProgramsQuery()
  const [deleteRewardProgram] = useDeleteRewardProgramMutation()

  const [earnPrograms, setEarnPrograms] = useState<TRewardProgram[]>([])
  const [earnPage, setEarnPage] = useState(1)
  const [earnLastPage, setEarnLastPage] = useState(1)

  const [redeemPrograms, setRedeemPrograms] = useState<TRewardProgram[]>([])
  const [redeemPage, setRedeemPage] = useState(1)
  const [redeemLastPage, setRedeemLastPage] = useState(1)

  useEffect(() => {
    listPrograms({
      page: earnPage,
      rewardType: EProgramRewardType.EARN
    })
      .unwrap()
      .then((res) => {
        setEarnPrograms(res.data || [])
        setEarnLastPage(res.meta.lastPage)
      })
  }, [earnPage])

  useEffect(() => {
    listPrograms({
      page: redeemPage,
      rewardType: EProgramRewardType.REDEEM
    })
      .unwrap()
      .then((res) => {
        setRedeemPrograms(res.data || [])
        setRedeemLastPage(res.meta.lastPage)
      })
  }, [redeemPage])

  const handleDeleteClick = (program: TRewardProgram) => {
    deleteSweetAlert({
      id: program.id,
      deleteAction: deleteRewardProgram,
    })
  }

  return (
    <Fragment>
      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title="Manage Reward Programs"
            route=""
          ></CardHeaderWithBack>
        </Card.Header>
      </Card>

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Ways to Earn</Card.Title>
          <Card.Subtitle>
            <Button
              variant="primary-light"
              onClick={() => navigate(`new-earn`)}
            >
              Add<i className="bi bi-plus-lg ms-2" />
            </Button>
          </Card.Subtitle>
        </Card.Header>
        <Card.Body>
          <Table className="table table-bordered text-nowrap">
            <thead>
              <tr>
                <th>Name</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {earnPrograms.map((program, index) => (
                <Fragment key={index}>
                  <tr>
                    <td>
                      <Link to={program.id}>
                        {program.rewardName}
                      </Link>
                    </td>
                    <td>{ERewardProgramStatusName[program.status]}</td>
                    <td>
                      <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
                        <Link to={program.id}>
                          <Button
                            variant="warning-light"
                            className="btn btn-sm ms-2"
                          >
                            <span className="ri-edit-line fs-14"></span>
                          </Button>
                        </Link>
                      </OverlayTrigger>

                      <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
                        <Button
                          variant="danger-light"
                          className="btn btn-sm ms-2"
                          onClick={() => handleDeleteClick(program)}
                        >
                          <span className="ri-delete-bin-7-line fs-14"></span>
                        </Button>
                      </OverlayTrigger>
                    </td>
                  </tr>
                </Fragment>
              ))}
            </tbody>
          </Table>
          <PaginationBar
            page={earnPage}
            setPage={setEarnPage}
            lastPage={earnLastPage}
          />
        </Card.Body>
      </Card>

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Ways to Redeem</Card.Title>
          <Card.Subtitle>
            <Button
              variant="primary-light"
              onClick={() => navigate(`new-redeem`)}
            >
              Add<i className="bi bi-plus-lg ms-2" />
            </Button>
          </Card.Subtitle>
        </Card.Header>
        <Card.Body>
          <Table className="table table-bordered text-nowrap">
            <thead>
              <tr>
                <th>Name</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {redeemPrograms.map((program, index) => (
                <Fragment key={index}>
                  <tr>
                    <td>
                      <Link to={program.id}>
                        {program.rewardName}
                      </Link>
                    </td>
                    <td>{ERewardProgramStatusName[program.status]}</td>
                    <td>
                      <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
                        <Link to={program.id}>
                          <Button
                            variant="warning-light"
                            className="btn btn-sm ms-2"
                          >
                            <span className="ri-edit-line fs-14"></span>
                          </Button>
                        </Link>
                      </OverlayTrigger>

                      <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
                        <Button
                          variant="danger-light"
                          className="btn btn-sm ms-2"
                          onClick={() => handleDeleteClick(program)}
                        >
                          <span className="ri-delete-bin-7-line fs-14"></span>
                        </Button>
                      </OverlayTrigger>
                    </td>
                  </tr>
                </Fragment>
              ))}
            </tbody>
          </Table>
          <PaginationBar
            page={redeemPage}
            setPage={setRedeemPage}
            lastPage={redeemLastPage}
          />
        </Card.Body>
      </Card>
    </Fragment>
  );
};

export default ManagementRewardProgram;
