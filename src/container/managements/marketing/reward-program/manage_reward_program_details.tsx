import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, Card, Col, Form, Row } from "react-bootstrap";
import { useNavigate, useParams } from "react-router-dom";
import Swal from "sweetalert2";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import {
  useCreateRewardProgramMutation,
  useDeleteRewardProgramMutation,
  useLazyGetRewardProgramByIdQuery,
  useUpdateRewardProgramMutation
} from "../../../../services/reward-program";
import {
  EProgramRewardType,
  EProgramRewardTypeName,
  ERewardProgramExchangeType,
  ERewardProgramStatus,
  ERewardProgramTypeEarn,
  ERewardProgramTypeName,
  ERewardProgramTypeRedeem
} from "../../../../utils/constant/reward-program";
import { getAllErrorMessages } from "../../../../utils/errors";

interface ManagementRewardProgramDetailsProps { }

const ManagementRewardProgramDetails: FC<ManagementRewardProgramDetailsProps> = () => {
  const { id } = useParams()
  const [err, setErr] = useState<any>({})

  const [isAdd, setIsAdd] = useState(false)
  const [isEdit, setIsEdit] = useState(false)

  const [detailsFormData, setDetailsFormData] = useState<Partial<TRewardProgram>>({
    status: ERewardProgramStatus.ACTIVE,
    rewardType: EProgramRewardType.EARN,
    exchangeType: ERewardProgramExchangeType.FIXED,

    rewardValue: 1,
  })

  const [getRewardProgram] = useLazyGetRewardProgramByIdQuery()
  const [createRewardProgram] = useCreateRewardProgramMutation()
  const [updateRewardProgram] = useUpdateRewardProgramMutation()
  const [deleteRewardProgram] = useDeleteRewardProgramMutation()

  useEffect(() => {
    setIsAdd(false)
    setIsEdit(false)

    switch (id) {
      case ('new-earn'): {
        setIsAdd(true)
        setDetailsFormData({
          ...detailsFormData,
          rewardType: EProgramRewardType.EARN
        })
        break
      }
      case ('new-redeem'): {
        setIsAdd(true)
        setDetailsFormData({
          ...detailsFormData,
          rewardType: EProgramRewardType.REDEEM
        })
        break
      }
      default: {
        setIsEdit(true)
        break
      }
    }

  }, [id])

  useEffect(() => {
    if (isEdit) {
      getRewardProgram({ id })
        .unwrap()
        .then((res) => {
          setDetailsFormData(res)
        })
    }
  }, [isAdd, isEdit])

  const handleDetailsFormChange = (event) => {
    if (event) { event.preventDefault(); }

    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.value;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData)
  }

  const prepareSubmitForm = () => {
    const preparedForm: any = { ...detailsFormData }

    if (detailsFormData.exchangeType == ERewardProgramExchangeType.FIXED) {
      preparedForm.variablePointsStep = null
      preparedForm.variablePointsStepRewardValue = null
      preparedForm.variablePointsMin = null
      preparedForm.variablePointsMax = null
    } else if (detailsFormData.exchangeType == ERewardProgramExchangeType.VARIABLE) {
      preparedForm.pointsPrice = null
      preparedForm.rewardValue = null
    }

    return preparedForm
  }

  const navigate = useNavigate()
  const handleAddFormSubmit = () => {

    const rewardProgram = prepareSubmitForm()

    createRewardProgram({
      ...rewardProgram,
    })
      .unwrap()
      .then(() => {
        navigate("/managements-reward-programs");
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error))
        Swal.fire('Error!', getAllErrorMessages(error).messages[0], 'error')
      })
  }

  const handleUpdateFormSubmit = () => {
    const rewardProgram = prepareSubmitForm()

    updateRewardProgram({
      ...rewardProgram,
      id,
    })
      .unwrap()
      .then(() => {
        navigate("/managements-reward-programs");
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error))
        Swal.fire('Error!', getAllErrorMessages(error).messages[0], 'error')
      })
  }

  const handleDeleteClick = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: deleteRewardProgram,
      finishAction() {
        navigate("/managements-reward-programs");
      },
    })
  }

  return (
    <Fragment>
      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title="Manage Reward Programs"
            route=""
          ></CardHeaderWithBack>
        </Card.Header>
      </Card>

      <Row>
        <Col lg={8}>
          <Card className="custom-card">
            <Card.Body>
              <div className="mb-3">
                <Form.Label>
                  Reward Name*
                </Form.Label>
                <Form.Control
                  name='rewardName'
                  value={detailsFormData.rewardName || ""}
                  onChange={handleDetailsFormChange}
                  isInvalid={err?.validationErrors?.rewardName}
                />
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.rewardName}
                </Form.Control.Feedback>
              </div>

              <div className="mb-3">
                <Form.Label>
                  Reward Description
                </Form.Label>
                <Form.Control
                  name='rewardDescription'
                  value={detailsFormData.rewardDescription || ""}
                  onChange={handleDetailsFormChange}
                  isInvalid={err?.validationErrors?.rewardDescription}
                />
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.rewardDescription}
                </Form.Control.Feedback>
              </div>

              <div className="">
                <Form.Label>
                  Exchange Description*
                </Form.Label>
                <Form.Control
                  name='exchangeDescription'
                  value={detailsFormData.exchangeDescription || ""}
                  onChange={handleDetailsFormChange}
                  isInvalid={err?.validationErrors?.exchangeDescription}
                />
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.exchangeDescription}
                </Form.Control.Feedback>
              </div>
            </Card.Body>
          </Card>
        </Col>
        <Col lg={4}>
          <Card className="custom-card">
            <Card.Body>
              <div className="mb-3">
                <Form.Label>
                  Status
                </Form.Label>
                <Row>
                  <Col>
                    <Form.Check
                      type='radio'
                      label="Active"
                      name="status"
                      checked={detailsFormData.status == ERewardProgramStatus.ACTIVE}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setDetailsFormData({
                            ...detailsFormData,
                            status: ERewardProgramStatus.ACTIVE
                          })
                        }
                      }}
                    />
                  </Col>
                  <Col>
                    <Form.Check
                      type='radio'
                      label="Disabled"
                      name="status"
                      checked={detailsFormData.status == ERewardProgramStatus.DISABLED}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setDetailsFormData({
                            ...detailsFormData,
                            status: ERewardProgramStatus.DISABLED
                          })
                        }
                      }}
                    />
                  </Col>
                </Row>
              </div>

              <div className="mb-3">
                <Form.Label>
                  Reward Type
                </Form.Label>
                <Form.Control
                  disabled
                  value={EProgramRewardTypeName[detailsFormData.rewardType || 0]}
                />
              </div>

              <div className="">
                <Form.Label>
                  Reward Program Type*
                </Form.Label>
                <Form.Select
                  disabled={isEdit}
                  name='programType'
                  value={detailsFormData.programType || ""}
                  onChange={handleDetailsFormChange}
                  isInvalid={err?.validationErrors?.programType}
                >
                  {detailsFormData.rewardType == EProgramRewardType.EARN ?
                    <Fragment>
                      <option value="" hidden></option>
                      {
                        Object.values(ERewardProgramTypeEarn)
                          .filter((e: any) => !isNaN(parseInt(e)))
                          .map((earn, index) => (
                            <option key={index} value={earn}>
                              {ERewardProgramTypeName[earn]}
                            </option>
                          ))
                      }
                    </Fragment>
                    : detailsFormData.rewardType == EProgramRewardType.REDEEM ?
                      <Fragment>
                        <option value="" hidden></option>
                        {
                          Object.values(ERewardProgramTypeRedeem)
                            .filter((e: any) => !isNaN(parseInt(e)))
                            .map((redeem, index) => (
                              <option key={index} value={redeem}>
                                {ERewardProgramTypeName[redeem]}
                              </option>
                            ))
                        }
                      </Fragment>
                      : null
                  }
                </Form.Select>
                <Form.Control.Feedback className="invalid-feedback">
                  {err?.validationErrors?.programType}
                </Form.Control.Feedback>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>
            {
              detailsFormData.rewardType == EProgramRewardType.EARN ?
                "Earning Value"
                : detailsFormData.rewardType == EProgramRewardType.REDEEM ?
                  "Redeem Value"
                  : null
            }
          </Card.Title>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col>
              <Form.Label>
                Points Exchange Type
              </Form.Label>
            </Col>
            <Col>
              <Form.Check
                type='radio'
                label="Fixed amount of points"
                name="exchange-type"
                checked={detailsFormData.exchangeType == ERewardProgramExchangeType.FIXED}
                onChange={(e) => {
                  if (e.target.checked) {
                    setDetailsFormData({
                      ...detailsFormData,
                      exchangeType: ERewardProgramExchangeType.FIXED
                    })
                  }
                }}
              />
            </Col>
            <Col>
              <Form.Check
                type='radio'
                label="Increments of points"
                name="exchange-type"
                checked={detailsFormData.exchangeType == ERewardProgramExchangeType.VARIABLE}
                onChange={(e) => {
                  if (e.target.checked) {
                    setDetailsFormData({
                      ...detailsFormData,
                      exchangeType: ERewardProgramExchangeType.VARIABLE
                    })
                  }
                }}
              />
            </Col>
          </Row>
        </Card.Body>
        <div className="border-bottom" />
        <Card.Body>
          {detailsFormData.exchangeType == ERewardProgramExchangeType.FIXED ?
            <Fragment>
              <Row>
                <Col>
                  <Form.Label>
                    Points*
                  </Form.Label>
                  <Form.Control
                    type='number'
                    name='pointsPrice'
                    min={1}
                    value={detailsFormData.pointsPrice || ""}
                    onChange={handleDetailsFormChange}
                    isInvalid={err?.validationErrors?.pointsPrice}
                  />
                  <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.pointsPrice}
                  </Form.Control.Feedback>
                </Col>
                <Col>
                  <Form.Label>
                    Reward Value*
                  </Form.Label>
                  <Form.Control
                    disabled={detailsFormData.rewardType == EProgramRewardType.EARN}
                    type='number'
                    name='rewardValue'
                    min={1}
                    value={detailsFormData.rewardValue || 1}
                    onChange={handleDetailsFormChange}
                    isInvalid={err?.validationErrors?.rewardValue}
                  />
                  <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.rewardValue}
                  </Form.Control.Feedback>
                </Col>
              </Row>
            </Fragment>
            : detailsFormData.exchangeType == ERewardProgramExchangeType.VARIABLE ?
              <Fragment>
                <Row className="mb-3">
                  <Col>
                    <Form.Label>
                      Points Step*
                    </Form.Label>
                    <Form.Control
                      type='number'
                      name='variablePointsStep'
                      min={1}
                      value={detailsFormData.variablePointsStep || ""}
                      onChange={handleDetailsFormChange}
                      isInvalid={err?.validationErrors?.variablePointsStep}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.variablePointsStep}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Label>
                      Reward Value*
                    </Form.Label>
                    <Form.Control
                      type='number'
                      name='variablePointsStepRewardValue'
                      min={1}
                      value={detailsFormData.variablePointsStepRewardValue || ""}
                      onChange={handleDetailsFormChange}
                      isInvalid={err?.validationErrors?.variablePointsStepRewardValue}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.variablePointsStepRewardValue}
                    </Form.Control.Feedback>
                  </Col>
                </Row>
                {/* <Row>
                  <Col>
                    <Form.Label>
                      Variable Points Min
                    </Form.Label>
                    <Form.Control
                      type='number'
                      name='variablePointsMin'
                      min={1}
                      value={detailsFormData.variablePointsMin || ""}
                      onChange={handleDetailsFormChange}
                      isInvalid={err?.validationErrors?.variablePointsMin}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.variablePointsMin}
                    </Form.Control.Feedback>
                  </Col>
                  <Col>
                    <Form.Label>
                      Variable Points Max
                    </Form.Label>
                    <Form.Control
                      type='number'
                      name='variablePointsMax'
                      min={1}
                      value={detailsFormData.variablePointsMax || ""}
                      onChange={handleDetailsFormChange}
                      isInvalid={err?.validationErrors?.variablePointsMax}
                    />
                    <Form.Control.Feedback className="invalid-feedback">
                      {err?.validationErrors?.variablePointsMax}
                    </Form.Control.Feedback>
                  </Col>
                </Row> */}
              </Fragment>
              : null
          }
        </Card.Body>

        {/* <Card.Footer>
          <Form.Label>
            Minimum Point Price
          </Form.Label>
          <Form.Control
            type='number'
            name='minimumPointsPrice'
            min={1}
            value={detailsFormData.minimumPointsPrice || ""}
            onChange={handleDetailsFormChange}
            isInvalid={err?.validationErrors?.minimumPointsPrice}
          />
          <Form.Control.Feedback className="invalid-feedback">
            {err?.validationErrors?.minimumPointsPrice}
          </Form.Control.Feedback>
        </Card.Footer> */}
      </Card>

      <Card className="custom-card">
        <Card.Header>
          <Card.Title></Card.Title>
          <Card.Subtitle>
            {isAdd ?
              <Button
                variant="primary-light"
                onClick={handleAddFormSubmit}
              >
                Add<i className="bi bi-plus-lg ms-2" />
              </Button>
              : isEdit ?
                <Fragment>
                  <Button
                    variant="danger-light"
                    className="me-2"
                    onClick={handleDeleteClick}
                  >
                    Delete<i className="bi bi-trash ms-2" />
                  </Button>
                  <Button
                    variant="success-light"
                    onClick={handleUpdateFormSubmit}
                  >
                    Save<i className="bi bi-download ms-2" />
                  </Button>
                </Fragment>
                : null
            }
          </Card.Subtitle>
        </Card.Header>
      </Card>
    </Fragment >
  );
};

export default ManagementRewardProgramDetails;
