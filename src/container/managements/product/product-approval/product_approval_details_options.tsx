import { FC, Fragment } from "react";
import { Card, Form, InputGroup } from "react-bootstrap";

interface ManagementProductApprovalDetailsOptionsProps {
  productOptions: TProductOption[],
}

export const ManagementProducApprovaltDetailsOptionsList: FC<ManagementProductApprovalDetailsOptionsProps> = ({
  productOptions,
}) => {

  return (
    <Card className="custom-card">
      <Card.Header>
        <Card.Title>
          Options
        </Card.Title>
      </Card.Header>
      <Card.Body>
        {productOptions?.map((option, index) =>
          <Fragment key={index}>
            <SingleProductOption
              option={option}
            />
          </Fragment>
        )}
      </Card.Body>
    </Card>
  );
};

const SingleProductOption = ({
  option,
}) => {

  return <Fragment>
    <Card className="custom-card border">
      <Card.Header>
        <Form.Label>Option Name</Form.Label>
        <InputGroup>
          <Form.Control
            disabled
            value={option.name}
          />
        </InputGroup>
      </Card.Header>
      <Card.Body>
        <Form.Label>Option Values</Form.Label>
        {
          option.productOptionValues?.map((optionValue, index) => (
            <Fragment key={index}>
              <SingleProductOptionValue
                optionValue={optionValue}
              />
            </Fragment>
          ))
        }
      </Card.Body>
    </Card>
  </Fragment>
}

const SingleProductOptionValue = ({
  optionValue,
}) => {

  return <Fragment>
    <InputGroup>
      <Form.Control
        disabled
        value={optionValue.value}
      />
    </InputGroup>
  </Fragment>
}