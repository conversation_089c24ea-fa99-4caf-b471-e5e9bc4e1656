import { FC, Fragment, useState } from "react";
import { Card, Table } from "react-bootstrap";
import { ManagementProductApprovalDetailsVariantModal } from "./product_approval_details_variant_modal";

interface ManagementProductApprovalDetailsVariantListProps {
  productVariants: TProductVariant[]
}

export const ManagementProductApprovalDetailsVariantList: FC<ManagementProductApprovalDetailsVariantListProps> = ({
  productVariants,
}) => {

  return (
    <Card className="custom-card">
      <Card.Header>
        <Card.Title>
          Variants
        </Card.Title>
      </Card.Header>
      <Card.Body className="overflow-auto">
        <Table className="table table-bordered text-nowrap border-bottom mb-3 position-relative">
          <thead>
            <tr>
              <th className="text-center">Image</th>
              <th className="text-center">Title</th>
              <th className="text-center">SKU</th>
              <th className="text-center">Quantity</th>
              <th className="text-center">Price</th>
              <th className="text-center">Compare at Price</th>
            </tr>
          </thead>
          <tbody>
            {productVariants.map((productVariant: TProductVariant) => (
              <Fragment key={productVariant.id}>
                <ReadOnlyRow
                  productVariant={productVariant}
                />
              </Fragment>
            ))}
          </tbody>
        </Table>
      </Card.Body>
    </Card>
  );
};

const ReadOnlyRow = ({
  productVariant,
}: any) => {
  const [modalShown, setModalShown] = useState(false)

  const variantTitle = [...productVariant.optionValues]
    .sort((a, b) =>
      (a.option?.position || 0) - (b.option?.position || 0)
    )
    .map(opVal => opVal.value)
    .join(' / ')

  const [isHovered, setIsHovered] = useState(false)
  const [isSeleted, setIsSelected] = useState(false)

  return (
    <Fragment>
      <tr
        className={(isHovered || isSeleted) ? 'table-dark' : ''}
        style={{ cursor: 'pointer' }}
        onClick={() => {
          setIsSelected(true)
          setModalShown(true)
        }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <td
          className="text-center"
          style={{
            textAlign: "center",
            padding: "10px",
            width: "10px",
            height: "10px",
          }}>
          <p className="avatar avatar-lg bg-dark-transparent my-auto">
            {
              productVariant.image?.src ?
                <img
                  src={productVariant.image?.src}
                  alt="img"
                  style={{
                    display: "block",
                    margin: "0 auto",
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                />
                : "img"
            }
          </p>
        </td>
        <td className="text-center">
          {variantTitle}
        </td>
        <td className="text-center">{productVariant.sku}</td>
        <td className="text-center">{productVariant.inventoryQuantity}</td>
        <td className="text-center">{productVariant.price}</td>
        <td className="text-center">{productVariant.compareAtPrice}</td>
      </tr>

      <ManagementProductApprovalDetailsVariantModal
        show={modalShown}
        onHide={() => {
          setIsSelected(false)
          setModalShown(false)
        }}
        productVariant={productVariant}
        variantTitle={variantTitle}
      />
    </Fragment>
  );
};
