import { FC, Fragment, useState } from "react";
import { Col, Form, Modal, Row } from "react-bootstrap";
import { NumericFormat } from "react-number-format";

interface ManagementProductApprovalDetailsVariantModalProps {
  show?: boolean
  onHide?: () => void

  productVariant: TProductVariant
  variantTitle,
}

export const ManagementProductApprovalDetailsVariantModal: FC<ManagementProductApprovalDetailsVariantModalProps> = ({
  show, onHide, productVariant, variantTitle,
}) => {

  const [variant] = useState(productVariant)

  const [isPhysical] = useState(!!(productVariant.weight || productVariant.weightUnit))

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => onHide?.()}
        centered
        size='xl'
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Variant
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Col>
            <Row>
              <Col lg={4} className="text-center mb-3">
                <Col>
                  <Form.Label className="text-primary">Image</Form.Label>
                </Col>

                <Col className="avatar avatar-xxxl bg-dark-transparent mb-2">
                  {
                    productVariant.image?.src ?
                      <img
                        src={productVariant.image?.src}
                        alt="img"
                        style={{
                          display: "block",
                          margin: "0 auto",
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                      />
                      : 'img'
                  }
                </Col>
              </Col>

              <Col lg={8} className="mb-3">
                <Form.Label className="text-primary">Options</Form.Label>

                <Row>
                  <Col lg={6} className="mb-3">
                    {
                      [...variant.optionValues]
                        .sort((a, b) =>
                          (a.option?.position || 0) - (b.option?.position || 0)
                        )
                        .map((optionValue, index) => (
                          <Col key={index} className="mb-3">
                            <Form.Label>
                              {optionValue.option?.name}
                            </Form.Label>
                            <Form.Control
                              disabled
                              value={optionValue.value}
                            />
                          </Col>
                        ))
                    }
                  </Col>
                </Row>
              </Col>
            </Row>
          </Col>

          <div className="border-bottom border-primary mb-3" />

          <Col>
            <div className="mb-3">
              <Form.Label className="text-primary">Title</Form.Label>
              <Form.Control
                disabled
                placeholder="Title"
                value={variantTitle}
              />
            </div>

          </Col>

          <div className="border-bottom border-primary mb-3" />

          <Row>
            <Col md={6} className="border-end border-primary">

              <Col className="border-bottom border-primary mb-3">
                <Form.Label className="text-primary">Inventory</Form.Label>

                <Row>
                  <Col lg={6} className="mb-3">
                    <Form.Label>SKU</Form.Label>
                    <Form.Control
                      disabled
                      value={variant?.sku || ""}
                    />
                  </Col>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Barcode</Form.Label>
                    <Form.Control
                      disabled
                      value={variant?.barcode || ""}
                    />
                  </Col>
                </Row>

                <Row>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Quantity</Form.Label>
                    <NumericFormat
                      disabled
                      className="form-control"
                      value={variant?.inventoryQuantity}
                    />
                  </Col>
                </Row>

                <Col>
                  <div className="form-check form-switch mb-2">
                    <input
                      disabled
                      className="form-check-input form-checked-primary"
                      type="checkbox"
                      checked={variant.inventoryPolicy == 'continue'}
                    />
                    <label>
                      Continue selling when out of stock
                    </label>
                  </div>
                </Col>
              </Col>
            </Col>

            <Col md={6}>
              <Col className="border-bottom border-primary mb-3">
                <Form.Label className="text-primary">Pricing</Form.Label>

                <Row>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Price</Form.Label>
                    <NumericFormat
                      disabled
                      className="form-control"
                      value={variant?.price}
                      decimalScale={2}
                    />
                  </Col>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Compare At Price</Form.Label>
                    <NumericFormat
                      disabled
                      className="form-control"
                      value={variant?.compareAtPrice}
                    />
                  </Col>
                </Row>
              </Col>

              <Col>
                <Row>
                  <Col>
                    <Form.Label className="text-primary">Package</Form.Label>
                  </Col>

                  <Col>
                    <div className="form-check form-switch mb-2">
                      <input
                        disabled
                        className="form-check-input form-checked-primary"
                        type="checkbox"
                        checked={isPhysical}
                      />
                      <label>
                        Is Physical Product
                      </label>
                    </div>
                  </Col>
                </Row>
                {
                  isPhysical &&
                  <Row>
                    <Col lg={6} className="mb-3">
                      <Form.Label>Weight</Form.Label>
                      <NumericFormat
                        disabled
                        className="form-control"
                        value={variant?.weight}
                      />
                    </Col>
                    <Col lg={6} className="mb-3">
                      <Form.Label>Weight Unit</Form.Label>
                      <Form.Select
                        disabled
                        name='weightUnit'
                        value={variant?.weightUnit || ''}
                      >
                        <option value='g'>g</option>
                        <option value='kg'>kg</option>
                        <option value='lb'>lb</option>
                        <option value='oz'>oz</option>
                      </Form.Select>
                    </Col>
                  </Row>
                }
              </Col>
            </Col>
          </Row>
        </Modal.Body>
      </Modal >
    </Fragment>
  );
};
