import { FC, Fragment, useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card, Col,
  FormControl, InputGroup,
  Row, Table
} from "react-bootstrap";

import axios, { AxiosRequestConfig } from "axios";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import Card<PERSON>eaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazyExportInventoryImportReportQuery, useLazyGetInventoryImportReportQuery } from "../../../../services/report/inventory-import";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";

interface ManagementInventoryImportReportProps { }

const ManagementInventoryImportReport: FC<ManagementInventoryImportReportProps> = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(20);
  const [total, setTotal] = useState(20);
  const [lastPage, setLastPage] = useState(20);

  const [trigger] = useLazyGetInventoryImportReportQuery()
  const [exportOrderReport] = useLazyExportInventoryImportReportQuery()

  const [inventoryReports, setInventoryReports] = useState<TInventorySurplusReport[]>([])

  const [startDate, setStartDate] = useState<string>('')
  const [endDate, setEndDate] = useState<string>('')
  const [sku, setSku] = useState('')

  const [isLoading, setIsLoading] = useState(false);
  const [isInitial, setIsInitial] = useState(true);

  const loadData = (input: {
    page: number,
    limit?: number,
    startDate: string,
    endDate: string,
    sku: string,
  }) => {
    setIsLoading(true);
    trigger(input).then((res) => {
      setTotal(res.data?.meta?.total)
      setLastPage(res.data?.meta?.lastPage)
      setInventoryReports(res.data?.data || [])
    }).finally(() => {
      setIsInitial(false);
      setIsLoading(false);
    });
  };

  useEffect(() => {
    if (isInitial) { return }
    loadData({
      page,
      limit,
      startDate,
      endDate,
      sku,
    })
  }, [page])

  const handleApplyClick = (event) => {
    if (event) { event.preventDefault() }
    setPage(1)
    loadData({
      page: 1,
      limit,
      startDate,
      endDate,
      sku,
    })
  }

  const handleExportClick = (event) => {
    if (event) { event.preventDefault() }

    setIsLoading(true)
    exportOrderReport({
      startDate,
      endDate,
      sku,
    }).then((res) => {
      if (res.data) {
        const headers = { 'Content-Type': 'blob' };
        const config: AxiosRequestConfig = {
          method: 'GET',
          url: res.data.url,
          responseType: 'arraybuffer',
          headers
        };
        axios(config)
          .then((response) => {
            const url = URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.download = res.data?.filename || ""
            link.click();
          })
          .catch((error) => console.log(error))
      }
    })
      .catch((error) => console.log(error))
      .finally(() => setIsLoading(false))
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Manage Inventory Import Report"
                route=""
              />
              <Button
                hidden={isInitial ||
                  !hasPermission(ACTION.EXPORT, RESOURCE.INVENTORY_REPORT)}
                className="mx-2"
                variant="success-light"
                onClick={handleExportClick}
              >
                Export<i className="bi bi-download ms-2" />
              </Button>

            </Card.Header>
            <Card.Body>
              <div className="d-flex mb-3">
                {/* <Col lg={6}>
                  <InputGroup>
                    <InputGroup.Text>
                      Warehouse
                    </InputGroup.Text>
                    <Form.Select

                    />
                  </InputGroup>
                </Col> */}

                <div className="me-2">
                  <InputGroup>
                    <InputGroup.Text>
                      From
                    </InputGroup.Text>
                    <FormControl
                      type='date'
                      value={startDate}
                      onChange={(event) =>
                        setStartDate(event.target.value)
                      }
                    />
                  </InputGroup>
                </div>

                <div className="me-2">
                  <InputGroup>
                    <InputGroup.Text>
                      To
                    </InputGroup.Text>
                    <FormControl
                      type='date'
                      value={endDate}
                      onChange={(event) =>
                        setEndDate(event.target.value)
                      }
                    />
                  </InputGroup>
                </div>

                <div className="me-2">
                  <InputGroup>
                    <InputGroup.Text>
                      SKU
                    </InputGroup.Text>
                    <FormControl
                      type='search'
                      value={sku}
                      onChange={(event) =>
                        setSku(event.target.value)
                      }
                    />
                  </InputGroup>
                </div>

                <div className="ms-auto">
                  <Button
                    hidden={!hasPermission(ACTION.READ, RESOURCE.INVENTORY_REPORT)}
                    variant="primary-light"
                    onClick={handleApplyClick}
                  >
                    Apply<i className="bi bi-search ms-2" />
                  </Button>
                </div>
              </div>

              <div className="overflow-auto">
                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Import Date</th>
                      <th>SKU</th>
                      <th>Name</th>
                      <th>Cost Price</th>
                      <th>Unit Price</th>
                      <th>Quantity</th>
                      <th>Measurement</th>
                      <th>Supplier</th>
                      <th>Warehouse</th>
                    </tr>
                  </thead>
                  <tbody>
                    {inventoryReports.map((inventoryReport: any) => (
                      <Fragment key={Math.random()}>
                        <ReadOnlyRow
                          inventoryReport={inventoryReport}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
              </div>
              {
                !isInitial &&
                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={lastPage}
                  limit={limit}
                  total={total}
                />
              }
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

const ReadOnlyRow = ({
  inventoryReport,
}: { inventoryReport: TInventoryImportReport }) => {

  return (
    <tr>
      <td>{inventoryReport.effective_date?.value}</td>
      <td>{inventoryReport.product_code}</td>
      <td>{inventoryReport.product_name}</td>
      <td>{inventoryReport.cost_price}</td>
      <td>{inventoryReport.unit_price}</td>
      <td>{inventoryReport.quantity}</td>
      <td>{inventoryReport.uom_name}</td>
      <td>{inventoryReport.supplier_name}</td>
      <td>{inventoryReport.to_warehouse_name}</td>
    </tr>

  );
};

export default ManagementInventoryImportReport;
