import React, { FC, Fragment, useEffect, useState } from "react";
import {
  <PERSON>ge,
  <PERSON>,
  Card,
  Col,
  OverlayTrigger,
  Row,
  Table,
  Tooltip,
} from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import {
  useLazyListTransactionsQuery,
  useLazyGetTransactionStatsQuery,
} from "../../../services/transaction";
import moment from "moment";
import { currencySymbol } from "../../../utils/constant/currency";
import { hasPermission } from "../../../utils/authorization";
import { ACTION } from "../../../utils/constant/authorization";
import { RESOURCE } from "../../../utils/constant/authorization";

interface ManagementTransactionProps {}

const ManagementTransaction: FC<ManagementTransactionProps> = () => {
  const navigate = useNavigate();
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20);

  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<TTransactionStats | null>(null);

  const [trigger] = useLazyListTransactionsQuery();
  const [getStats] = useLazyGetTransactionStatsQuery();

  const [transactions, setTransactions] = useState<TTransaction[]>([]);

  useEffect(() => {
    setIsLoading(true);
    trigger({
      page,
      limit,
    })
      .unwrap()
      .then((res) => {
        setTransactions(res.data || []);
        setLastPage(res?.meta?.lastPage);
        setTotal(res?.meta?.total);
      })
      .finally(() => {
        setIsLoading(false);
      });
  }, [page, limit]);

  useEffect(() => {
    getStats()
      .unwrap()
      .then((res) => {
        setStats(res);
      });
  }, []);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      COMPLETED: { variant: "success", text: "Completed" },
      REFUNDED: { variant: "warning", text: "Refunded" },
      CAPTURED: { variant: "info", text: "Captured" },
      VOIDED: { variant: "danger", text: "Voided" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "secondary",
      text: status,
    };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const getSourceBadge = (source: string) => {
    const sourceConfig = {
      PAYPAL: { variant: "primary", text: "PayPal" },
      AUTHORIZE_NET: { variant: "info", text: "Authorize.net" },
      CASH: { variant: "success", text: "Cash" },
      OTHER: { variant: "secondary", text: "Other" },
    };

    const config = sourceConfig[source as keyof typeof sourceConfig] || {
      variant: "secondary",
      text: source,
    };
    return <Badge bg={config.variant}>{config.text}</Badge>;
  };

  const formatCurrency = (
    amount: number | undefined | null,
    currency: string
  ) => {
    if (amount === null || amount === undefined || isNaN(amount)) {
      return `${currency}0.00`;
    }
    const symbol =
      currencySymbol[currency.toLowerCase() as keyof typeof currencySymbol] ||
      currency;
    return `${symbol}${Number(amount).toFixed(2)}`;
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Transaction Management"
                route=""
              ></CardHeaderWithBack>
            </Card.Header>
            <Card.Body className="overflow-auto">
              {/* Stats Cards */}
              {stats && (
                <Row className="mb-4">
                  <Col md={2}>
                    <Card className="text-center h-100">
                      <Card.Body
                        className="d-flex flex-column justify-content-center"
                        style={{ minHeight: "100px" }}
                      >
                        <h4 className="text-primary mb-2">{stats.total}</h4>
                        <small className="text-muted">Total Transactions</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center h-100">
                      <Card.Body
                        className="d-flex flex-column justify-content-center"
                        style={{ minHeight: "100px" }}
                      >
                        <h4 className="text-success mb-2">{stats.completed}</h4>
                        <small className="text-muted">Completed</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center h-100">
                      <Card.Body
                        className="d-flex flex-column justify-content-center"
                        style={{ minHeight: "100px" }}
                      >
                        <h4 className="text-warning mb-2">{stats.refunded}</h4>
                        <small className="text-muted">Refunded</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center h-100">
                      <Card.Body
                        className="d-flex flex-column justify-content-center"
                        style={{ minHeight: "100px" }}
                      >
                        <h4 className="text-info mb-2">{stats.captured}</h4>
                        <small className="text-muted">Captured</small>
                      </Card.Body>
                    </Card>
                  </Col>
                  <Col md={2}>
                    <Card className="text-center h-100">
                      <Card.Body
                        className="d-flex flex-column justify-content-center"
                        style={{ minHeight: "100px" }}
                      >
                        <h4 className="text-danger mb-2">{stats.voided}</h4>
                        <small className="text-muted">Voided</small>
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>
              )}

              <div className="app-container">
                <Table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th className="text-center">Transaction ID</th>
                      <th className="text-center">Order</th>
                      <th className="text-center">Amount</th>
                      <th className="text-center">Source</th>
                      <th className="text-center">Status</th>
                      <th className="text-center">Payer</th>
                      <th className="text-center">Date</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.map((transaction: TTransaction) => (
                      <tr key={transaction.id}>
                        <td className="text-center">
                          <Link
                            to={`/managements-transactions/${transaction.id}`}
                          >
                            {transaction.id}
                          </Link>
                        </td>
                        <td className="text-center">
                          <Link
                            to={`/managements-order/${transaction.orderId}`}
                            className="text-primary"
                          >
                            {transaction.order?.name || transaction.orderId}
                          </Link>
                        </td>
                        <td className="text-center">
                          <strong>
                            {formatCurrency(
                              transaction.amount,
                              transaction.currency
                            )}
                          </strong>
                          {transaction.refundAmount &&
                            transaction.refundAmount > 0 && (
                              <div>
                                <small className="text-danger">
                                  Refunded:{" "}
                                  {formatCurrency(
                                    transaction.refundAmount,
                                    transaction.currency
                                  )}
                                </small>
                              </div>
                            )}
                        </td>
                        <td className="text-center">
                          {getSourceBadge(transaction.source)}
                        </td>
                        <td className="text-center">
                          {getStatusBadge(transaction.status)}
                        </td>
                        <td className="text-center">
                          <div>
                            <div>{transaction.payerName}</div>
                            <small className="text-muted">
                              {transaction.payerEmail}
                            </small>
                          </div>
                        </td>
                        <td className="text-center">
                          <small>
                            {moment(transaction.processedAt).format(
                              "MMM DD, YYYY HH:mm"
                            )}
                          </small>
                        </td>
                        <td className="text-center">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip>View</Tooltip>}
                          >
                            <Button
                              hidden={
                                !hasPermission(
                                  ACTION.READ,
                                  RESOURCE.SALON_CONSTRUCTION_SERVICE
                                )
                              }
                              variant="info-light"
                              className="btn btn-info-light btn-sm ms-2"
                              onClick={() =>
                                navigate(
                                  `/managements-transactions/${transaction.id}`
                                )
                              }
                            >
                              <span className="ri-eye-line fs-14"></span>
                            </Button>
                          </OverlayTrigger>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>

                <PaginationBar
                  page={page}
                  lastPage={lastPage}
                  total={total}
                  setPage={setPage}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment>
  );
};

export default ManagementTransaction;
