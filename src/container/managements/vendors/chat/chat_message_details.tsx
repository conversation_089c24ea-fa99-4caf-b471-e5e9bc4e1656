import { ChatMessage } from "amazon-ivs-chat-messaging"
import moment from "moment"
import { Fragment } from "react"

export const transformDbChatToIvsChat = (chat: TChatMessage) => {
    let userId = 'guest'
    let senderAttributes: any = {
        type: 'guest',
        guestName: chat.attributes?.guestName
    }

    if (chat.user) {
        userId = chat.userId
        senderAttributes = {
            type: 'user',
            email: chat.user.email,
            firstName: chat.user.firstName,
            lastName: chat.user.lastName,
            fullname: chat.user.fullname,
            avatarUrl: chat.user.avatarUrl,
            avatarMediaUrl: chat.user.avatarMedia?.url,
            vendorId: chat.user.vendorId,
        }
    } else if (chat.admin) {
        userId = chat.adminId
        senderAttributes = {
            type: "admin",
            username: chat.admin.username,
            name: chat.admin.name,
            avatarUrl: chat.admin.avatar?.url,
        }
    } else if (chat.assistantId) {
        userId = chat.assistantId
        senderAttributes = {
            type: "assistant",
        }
    }

    const message: ChatMessage = {
        id: chat.ivsChatMessageId,
        content: chat.content,
        sendTime: new Date(chat.createdAt),
        sender: {
            userId: userId,
            attributes: senderAttributes,
        },
    }

    return message
}

export const transformChatMessage = (message: ChatMessage, currentAdmin: TAdmin) => {

    const senderStatus = getSenderStatus({ message, currentAdmin })

    return (
        <li className={`d-flex ${senderStatus.isAdmin ? 'flex-row-reverse' : ''}`}>
            {transformMessageAvatar({ message, senderStatus })}
            <div>
                {transformMessageContent({ message, senderStatus })}
            </div>
        </li>
    )
}

export const transformMessageAvatar = ({ message, senderStatus }: any) => {
    const getAvatarUrl = () => {
        let url = ''

        if (senderStatus.isAdmin) {
            url = message?.sender.attributes?.avatarUrl
        } else if (senderStatus.isUser) {
            url = message.sender.attributes?.avatarUrl || message.sender.attributes?.avatarMediaUrl
        }

        return url
    }


    return (
        <Fragment>
            <div className="p-2">
                <span className={`avatar avatar-md ${getBoxStatus(senderStatus)}`}>
                    {
                        getAvatarUrl()
                            ?
                            <img
                                src={getAvatarUrl()}
                                alt="img"
                            />
                            : `${getInitals(getDisplayedName({ message, senderStatus }))}`
                    }
                </span>
            </div>
        </Fragment>
    )
}

export const transformMessageContent = ({ message, senderStatus }: any) => {

    return (
        <Fragment>
            <div className={`d-flex ${senderStatus.isAdmin ? 'flex-row-reverse' : ''}`}>
                <span className="fw-bold">
                    {getDisplayedName({ message, senderStatus })}
                </span>
                <span className="text-muted mx-1">
                    {moment(message.sendTime).fromNow()}
                </span>
            </div>

            <div className={`p-2 rounded ${getBoxStatus(senderStatus)}`}>{message.content}</div>
        </Fragment>
    )
}

const getBoxStatus = (senderStatus) => {
    let avatarStatus = ''

    if (senderStatus.isAdmin) {
        avatarStatus = 'bg-outline-primary'
        if (senderStatus.isCurrent) {
            avatarStatus += ' bg-primary-transparent'
        }
    } else if (senderStatus.isUser) {
        if (senderStatus.isVendor) {
            avatarStatus = 'bg-outline-success'
        } else if (senderStatus.isCustomer) {
            avatarStatus = 'bg-outline-dark'
        }
    } else if (senderStatus.isAssistant) {
        avatarStatus = 'bg-outline-info'
    } else if (senderStatus.isGuest) {
        avatarStatus = 'bg-outline-dark'
    }

    return avatarStatus
}

export const getDateLabel = (date: string) => {
    const isToday = moment().format('YYYY-MM-DD') == date
    const isYesterday = moment().subtract(1, 'day').format('YYYY-MM-DD') == date
    const isThisYear = moment().format('YYYY') == moment(date).format('YYYY')

    if (isToday) {
        return 'Today'
    } else if (isYesterday) {
        return 'Yesterday'
    } else if (isThisYear) {
        return moment(date).format('MMM DD')
    } else {
        return moment(date).format('MMM DD, YYYY')
    }
}

const getInitals = (name: string) => {
    if (!name) { return '' }

    let initals = ''
    const names = name.split(" ")
    if (names.length > 1) {
        initals = names[0].charAt(0) + names[1].charAt(0)
    } else {
        initals = name.slice(0, 2)
    }
    return initals.toUpperCase()
}

const getDisplayedName = (payload: { message: ChatMessage, senderStatus: any }) => {
    const { message, senderStatus } = payload

    let name = message?.sender.attributes?.guestName || message.attributes?.guestName || 'Guest'

    if (senderStatus.isAdmin) {
        name = message?.sender.attributes?.name || message?.sender.attributes?.username || ''
    } else if (senderStatus.isUser) {
        name = message?.sender.attributes?.fullname || message?.sender.attributes?.email || ''
    } else if (senderStatus.isAssistant) {
        name = 'Zurno Bot'
    }

    return name
}

const getSenderStatus = ({
    message,
    currentAdmin,
}: {
    message: ChatMessage,
    currentAdmin?: TAdmin,
}) => {
    let isAdmin, isUser, isAssistant, isGuest
    let isVendor, isCustomer, isCurrent

    switch (message.sender.attributes?.type) {
        case 'admin': {
            isAdmin = true

            if (currentAdmin?.id == message.sender.userId) {
                isCurrent = true
            }

            break
        }
        case 'user': {
            isUser = true

            if (message.sender.attributes?.vendorId) {
                isVendor = true
            } else {
                isCustomer = true
            }

            break
        }
        case 'assistant': {
            isAssistant = true
            break
        }
        default: {
            isGuest = true
        }
    }

    return {
        isAdmin, isUser, isAssistant, isGuest,
        isVendor, isCustomer, isCurrent,
    }
}
