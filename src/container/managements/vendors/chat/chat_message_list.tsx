import {
    Chat<PERSON><PERSON><PERSON>,
    ChatRoom,
    ChatToken,
    ConnectionState,
    SendMessageRequest,
} from 'amazon-ivs-chat-messaging';
import { FC, Fragment, useEffect, useRef, useState } from "react";
import { Button, Card, Form, InputGroup, ListGroup, OverlayTrigger, Tooltip } from "react-bootstrap";
import SimpleBar from "simplebar-react";
import { useCreateChatMessageMutation, useCreateChatTokenMutation, useLazyListChatMessagesQuery } from "../../../../services/chat/chat";
import { getDateLabel, transformChatMessage, transformDbChatToIvsChat } from "./chat_message_details";
import moment from 'moment';
import { store } from '../../../../services/rtk/store';

interface ChatMessageListProps {
    roomId: string,
}

const ChatMessageList: FC<ChatMessageListProps> = ({
    roomId,
}) => {

    const [getChatToken] = useCreateChatTokenMutation()

    const tokenProvider = async (): Promise<ChatToken> => {
        const token = await getChatToken(roomId).unwrap()
        return token
    }

    const [room, setRoom] = useState<ChatRoom>(new ChatRoom({
        regionOrUrl: import.meta.env.VITE_AWS_SNS_REGION,
        tokenProvider: () => tokenProvider(),
    }))

    const [connectionState, setConnectionState] = useState<ConnectionState>('disconnected');

    // const [messages, setMessages] = useState<ChatMessage[]>([]);
    const [messageToSend, setMessageToSend] = useState('')
    const [isSending, setIsSending] = useState(false);
    const isSendDisabled = connectionState !== 'connected' || isSending || !messageToSend;

    useEffect(() => {
        if (!room) { return }

        const unsubscribeOnConnecting = room.addListener('connecting', () => {
            setConnectionState('connecting');
        });

        const unsubscribeOnConnected = room.addListener('connect', () => {
            setConnectionState('connected');
        });

        const unsubscribeOnDisconnected = room.addListener('disconnect', () => {
            setConnectionState('disconnected');
        });

        room.connect()

        const unsubscribeOnEvent = room.addListener('event', (event) => {
            console.log(event);
        });

        const unsubscribeOnMessageReceived = room.addListener('message', (message) => {
            // setMessages((msgs) => {
            //     msgs = [...msgs, message];
            //     return msgs
            // });
            setChatMessagesByDate((prev) => {
                const messageDate = moment(message.sendTime).format('YYYY-MM-DD')

                let messagesByDate = [...prev]
                const messageByDate = messagesByDate.find(mbd => mbd.date == messageDate)
                if (messageByDate) {
                    messageByDate.messages.push(message)
                } else {
                    messagesByDate.push({
                        date: messageDate,
                        messages: [message]
                    })
                }
                return messagesByDate
            })
        });

        const unsubscribeOnMessageDeleted = room.addListener('messageDelete', (messageDeleted) => {
            console.log(messageDeleted);
        });

        const unsubscribeOnUserDisconnected = room.addListener('userDisconnect', (userDisconnected) => {
            console.log(userDisconnected);
        });


        return () => {
            // Clean up subscriptions.
            unsubscribeOnConnecting();
            unsubscribeOnConnected();
            unsubscribeOnDisconnected();
            unsubscribeOnMessageReceived();
            unsubscribeOnMessageDeleted();
            unsubscribeOnUserDisconnected();
            unsubscribeOnEvent();
        };
    }, [room]);

    const [chatMessagesByDate, setChatMessagesByDate] = useState<{
        date: string,
        messages: ChatMessage[],
    }[]>([])
    const prepareReceivedData = (addingMessages: TChatMessage[], isAdding?: boolean) => {
        // get all chat messages from chat messages by date 
        // [ { messages: [ <ChatMessage> ] } ] =>  [ <ChatMessage> ]

        let chatMessages: ChatMessage[] = []
        if (isAdding) {
            chatMessages = chatMessagesByDate.flatMap(dateMessage => dateMessage.messages)
        }

        // add/replace exsiting messages with new messages
        for (const addingMessage of addingMessages) {
            const existingMessage = chatMessages.find(msg => msg.id == addingMessage.id)
            if (existingMessage) {
                const isAddingNewerMessage = moment(addingMessage.createdAt).valueOf() -
                    moment(existingMessage.sendTime).valueOf() > 0

                if (isAddingNewerMessage) {
                    chatMessages = chatMessages.filter(msg => msg.id != existingMessage.id)
                }
            }

            chatMessages.push(transformDbChatToIvsChat(addingMessage))
        }

        // sort by oldest
        chatMessages.sort((a, b) => moment(a.sendTime).valueOf() - moment(b.sendTime).valueOf())

        // group by date
        let messagesByDate: any = []
        for (const message of chatMessages) {
            const messageDate = moment(message.sendTime).format('YYYY-MM-DD')

            const messageByDate = messagesByDate.find(mbd => mbd.date == messageDate)
            if (messageByDate) {
                messageByDate.messages.push(message)
            } else {
                messagesByDate.push({
                    date: messageDate,
                    messages: [message]
                })
            }
        }

        setChatMessagesByDate(messagesByDate.sort((a, b) => moment(a.date).valueOf() - moment(b.date).valueOf()))
    }

    const [listMessages, { isFetching: isFetchingMessages }] = useLazyListChatMessagesQuery()
    const [page, setPage] = useState(1)
    const [hasMoreMessages, setHasMoreMessages] = useState(true)
    useEffect(() => {
        if (!roomId) { return }

        setRoom(new ChatRoom({
            regionOrUrl: import.meta.env.VITE_AWS_SNS_REGION,
            tokenProvider: () => tokenProvider(),
        }))

        setPage(1)
        // setMessages([])
        setChatMessagesByDate([])
        listMessages({ roomId, page: 1 })
            .unwrap()
            .then((res) => {
                prepareReceivedData(res.data)
                // setMessages(
                //     (res.data || []).map((chat) =>
                //         transformDbChatToIvsChat(chat))
                //         .reverse(),
                // )
                setCurrentRoom(res.room)
                setHasMoreMessages(!!res.meta.nextPageUrl)

            })
            .catch((error) => console.log(error))

    }, [roomId])

    useEffect(() => {
        if (!roomId) { return }

        listMessages({ roomId, page })
            .unwrap()
            .then((res) => {
                setIsMessageFromLoadMore(true)
                prepareReceivedData(res.data, true)
                // setMessages((prev) => [
                //     ...(res.data || []).map((chat) =>
                //         transformDbChatToIvsChat(chat))
                //         .reverse(),
                //     ...prev,
                // ])
                setCurrentRoom(res.room)
                setHasMoreMessages(!!res.meta.nextPageUrl)
            })
            .catch((error) => console.log(error))

    }, [page])

    const [sendComment] = useCreateChatMessageMutation()
    const handleMessageSend = (event) => {
        if (event) { event.preventDefault() }

        let attributes: any = {}

        // console.log(messageToSend);
        // socket.emit('message', { asdf: messageToSend })

        const request = new SendMessageRequest(messageToSend, attributes);
        setIsSending(true);
        setMessageToSend('');

        room.sendMessage(request)
            .then((response) => {
                sendComment({ roomId, ...response })
            })
            .catch((error) => {
                console.log(error);
            })
            .finally(() => {
                setIsSending(false);
            })
    };

    const handleMessageAreaKeyDown = (event) => {
        if (event.keyCode == 13 && event.shiftKey == false) {
            event.preventDefault()
            handleMessageSend(event)
        }
    }

    const simpleBarRef = useRef(null)
    const checkPostition = () => {
        let isAtTop, isAtBottom, simpleBarElement
        let scrollHeight = 0

        if (simpleBarRef.current) {
            // @ts-ignore
            simpleBarElement = simpleBarRef.current.getScrollElement();
            const scrollTop = simpleBarElement.scrollTop;
            scrollHeight = simpleBarElement.scrollHeight;
            const clientHeight = simpleBarElement.clientHeight;

            isAtTop = !scrollTop
            isAtBottom = scrollTop + clientHeight + 200 > scrollHeight
        }

        return { isAtTop, isAtBottom, scrollHeight, simpleBarElement }
    }
    const handleOnWheel = () => {
        const { isAtTop, scrollHeight } = checkPostition()

        if (isAtTop && hasMoreMessages && !isFetchingMessages) {
            setPage(page + 1)
            setCurrentScrollHeight(scrollHeight)
        }
    }

    const [currentScrollHeight, setCurrentScrollHeight] = useState(0)
    const [isMessagesFromLoadMore, setIsMessageFromLoadMore] = useState(false)
    useEffect(() => {
        const { isAtBottom, scrollHeight, simpleBarElement } = checkPostition()

        if (isAtBottom || page == 1) {
            // scroll to/stay at bottom if already at bottom
            simpleBarElement.scrollTop = simpleBarElement.scrollHeight
        } else if (isMessagesFromLoadMore) {
            // stay at top of old page (not new page) when load more messages
            simpleBarElement.scrollTop = scrollHeight - currentScrollHeight - 50
            setIsMessageFromLoadMore(false)
        }

    }, [chatMessagesByDate])

    const [currentRoom, setCurrentRoom] = useState<TChatRoom | null>(null)
    const [copyClicked, setCopyClicked] = useState(false)

    useEffect(() => {
        if (!copyClicked) { return }

        setTimeout(() => {
            setCopyClicked(false)
        }, 2000)

    }, [copyClicked])

    return (
        <Fragment>
            <Card className="custom-card">
                <Card.Header>
                    <Card.Title>
                        {currentRoom?.name || "Room Name"}

                        <OverlayTrigger overlay={<Tooltip>Copy Public Link</Tooltip>}>
                            <Button
                                className='btn-sm ms-2'
                                variant='light'
                                disabled={copyClicked}
                                onClick={() => {
                                    navigator.clipboard.writeText(currentRoom?.vendorPublicRoomUrl || '')
                                    setCopyClicked(true)
                                }}
                            >
                                {
                                    copyClicked
                                        ? <span className='ri-check-line fs-14' />
                                        : <span className='ri-clipboard-line fs-14' />
                                }
                            </Button>
                        </OverlayTrigger>
                    </Card.Title>
                </Card.Header>
                <Card.Body>
                    <SimpleBar
                        onWheel={handleOnWheel}
                        ref={simpleBarRef}
                        style={{ height: "calc(100vh - 400px)" }}
                    >
                        {/* {
                            !hasMoreMessages &&
                            <div className="p-3 text-center fw-bold bg-dark-transparent">
                                End of List
                            </div>
                        } */}
                        <ul className="list-unstyled">
                            {
                                isFetchingMessages &&
                                <li className="p-3 text-center align-items-center">
                                    <span className="spinner-border spinner-border-sm" />
                                </li>
                            }
                            {
                                chatMessagesByDate
                                    .map((message, index) => (
                                        <Fragment key={index}>
                                            <ReadOnlyRow
                                                messagesByDate={message}
                                            />
                                        </Fragment>
                                    ))
                            }
                        </ul>
                    </SimpleBar>
                </Card.Body>

                <Card.Footer>
                    <InputGroup>
                        <Form.Control
                            as='textarea'
                            rows={3}
                            maxLength={400}
                            value={messageToSend}
                            onChange={(event) => setMessageToSend(event.target.value)}
                            onKeyDown={handleMessageAreaKeyDown}
                        />
                        <Button
                            type="submit"
                            disabled={isSendDisabled}
                            onClick={handleMessageSend}
                        >
                            <span className="ri-send-plane-line" />
                        </Button>
                    </InputGroup>
                    <div className="form-text">
                        Message should not exceed 400 characters
                        {
                            messageToSend.length >= 300 &&
                            <span>
                                {" "}(
                                <span className={
                                    messageToSend.length == 400 ? "text-danger" : "text-warning"}>
                                    {messageToSend.length}
                                </span>
                                )
                            </span>
                        }
                    </div>
                </Card.Footer>
            </Card>
        </Fragment>
    )
}

const ReadOnlyRow = ({
    messagesByDate,
}: any) => {

    const currentAdmin = store.getState().auth.user as TAdmin

    return (
        <Fragment>
            <li>
                <ListGroup>
                    <ListGroup.Item className="text-center bg-transparent border-0 my-2">
                        <small
                            className="text-muted text-uppercase d-inline-block px-2 py-1 rounded-2e"
                            style={{
                                backgroundColor: 'rgba(114, 105, 239, 0.15)',
                                color: '#7269ef',
                                fontSize: '0.75rem'
                            }}>
                            {getDateLabel(messagesByDate.date)}
                        </small>
                    </ListGroup.Item>
                </ListGroup>
            </li>

            {
                messagesByDate.messages.map((message, index) => (
                    <div key={index} className="my-2">
                        {transformChatMessage(message, currentAdmin)}
                    </div>
                ))
            }
        </Fragment>
    )
}

export default ChatMessageList