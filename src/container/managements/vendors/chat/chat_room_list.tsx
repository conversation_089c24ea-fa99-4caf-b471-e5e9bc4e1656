import moment from "moment"
import { FC, Fragment, useEffect, useRef, useState } from "react"
import { Card, ListGroup } from "react-bootstrap"
import { useNavigate } from "react-router-dom"
import SimpleBar from "simplebar-react"
import LazySelect from "../../../../components/lazy-select/lazy-select"
import { useLazyListChatRoomsQuery } from "../../../../services/chat/chat"
import { useLazyGetVendorsListsQuery } from "../../../../services/vendors"
import { getDateLabel } from "./chat_message_details"

interface ChatRoomListProps {
    currentRoomId?: string,

    newUpdatedRoom: TChatRoom | null
}

const ChatRoomList: FC<ChatRoomListProps> = ({
    currentRoomId,

    newUpdatedRoom
}) => {
    const [page, setPage] = useState(1)
    const [hasMorePages, setHasMorePages] = useState(true)

    const [getRooms, { isFetching: isFetchingRooms }] = useLazyListChatRoomsQuery()
    const [chatRoomsByDate, setChatRoomsByDate] = useState<{
        date: string,
        rooms: TChatRoom[],
    }[]>([])

    const prepareReceivedData = (addingRooms: TChatRoom[], isAdding?: boolean) => {
        const getValueOfLatestAt = (dateChatRoom: TChatRoom) => {
            return moment(dateChatRoom.latestMessageAt || dateChatRoom.updatedAt).valueOf()
        }

        // get all chat rooms from chat rooms by date 
        // [ { rooms: [ <ChatRoom> ] } ] =>  [ <ChatRoom> ]
        let chatRooms: TChatRoom[] = []
        if (isAdding) {
            chatRooms = chatRoomsByDate.flatMap(dateRoom => dateRoom.rooms)
        }

        // add/replace exsiting rooms with new rooms
        for (const addingRoom of addingRooms) {
            const existingRoom = chatRooms.find(rm => rm.id == addingRoom.id)
            if (existingRoom) {
                const isAddingNewerRoom = getValueOfLatestAt(addingRoom) - getValueOfLatestAt(existingRoom) > 0

                if (isAddingNewerRoom) {
                    chatRooms = chatRooms.filter(room => room.id != existingRoom.id)
                }
            }

            chatRooms.push(addingRoom)
        }

        // sort by newest
        chatRooms.sort((a, b) => getValueOfLatestAt(b) - getValueOfLatestAt(a))

        // group by date
        let roomsByDate: any = []
        for (const room of chatRooms) {
            const roomDate = moment(room.latestMessageAt || room.updatedAt).format('YYYY-MM-DD')

            const roomByDate = roomsByDate.find(rbd => rbd.date == roomDate)
            if (roomByDate) {
                roomByDate.rooms.push(room)
            } else {
                roomsByDate.push({
                    date: roomDate,
                    rooms: [room]
                })
            }
        }

        setChatRoomsByDate(roomsByDate.sort((a, b) => moment(b.date).valueOf() - moment(a.date).valueOf()))
    }

    const [vendor, setVendor] = useState<TVendor | null>(null)
    useEffect(() => {
        console.log('vendor');

        setPage(1)
        setChatRoomsByDate([])
        getRooms({ page: 1, limit: 10, vendorId: vendor?.id || true })
            .unwrap()
            .then((res) => {
                prepareReceivedData(res.data)
                setHasMorePages(!!res.meta.nextPageUrl)
            })
    }, [vendor])

    useEffect(() => {
        if (page == 1) { return }

        getRooms({ page, limit: 10 })
            .unwrap()
            .then((res) => {
                prepareReceivedData(res.data, true)
                setHasMorePages(!!res.meta.nextPageUrl)
            })
    }, [page])

    useEffect(() => {
        if (!newUpdatedRoom) { return }

        prepareReceivedData([newUpdatedRoom], true)
    }, [newUpdatedRoom])

    const simpleBarRef = useRef(null)
    const handleOnWheel = () => {
        if (!simpleBarRef.current) { return }

        // @ts-ignore
        const simpleBarElement = simpleBarRef.current.getScrollElement();
        const scrollTop = simpleBarElement.scrollTop;
        const scrollHeight = simpleBarElement.scrollHeight;
        const clientHeight = simpleBarElement.clientHeight;

        const isAtBottom = scrollTop + clientHeight + 200 > scrollHeight

        if (isAtBottom && hasMorePages && !isFetchingRooms) {
            setPage(page + 1)
        }
    }

    const [vendorSelect] = useLazyGetVendorsListsQuery()

    return (
        <Fragment>
            <Card className="custom-card">
                <Card.Header>
                    <div style={{ width: '-webkit-fill-available' }}>
                        <LazySelect
                            isClearable
                            selectionFunction={vendorSelect}
                            label={value => value.companyName}
                            getSelectedOptions={value => setVendor(value)}
                        />
                    </div>
                </Card.Header>
                <Card.Body className="p-0">
                    <SimpleBar
                        onWheel={handleOnWheel}
                        ref={simpleBarRef}
                        style={{ height: "calc(100vh - 250px)" }}
                    >
                        <ul className="list-unstyled">
                            {
                                chatRoomsByDate
                                    .map((room, index) => (
                                        <Fragment key={index}>
                                            <ReadOnlyRow
                                                roomsByDate={room}
                                                currentRoomId={currentRoomId}
                                            />
                                        </Fragment>
                                    ))
                            }
                            {
                                isFetchingRooms &&
                                <li className="p-3 text-center align-items-center">
                                    <span className="spinner-border spinner-border-sm" />
                                </li>
                            }
                        </ul>
                        {
                            !hasMorePages &&
                            <div className="p-3 text-center fw-bold bg-dark-transparent">
                                End of List
                            </div>
                        }
                    </SimpleBar>
                </Card.Body>
            </Card>
        </Fragment>
    )
}

const ReadOnlyRow = ({
    roomsByDate,

    currentRoomId,
}: any) => {

    const navigate = useNavigate()

    const displayedName = (message: TChatMessage) => {
        let name = ''

        if (message?.assistantId) {
            name = 'Zurno Bot'
        } else if (message?.adminId) {
            name = message?.admin?.name || message?.admin?.username
        } else if (message?.userId) {
            name = message?.user?.fullname || message?.user?.email
        } else if (message) {
            name = message.attributes?.guestName || "Guest"
        }

        return name
    }

    return (
        <Fragment>
            <li>
                <ListGroup>
                    <ListGroup.Item className="bg-dark-transparent">
                        <div className="fw-semibold">
                            {getDateLabel(roomsByDate.date)}
                        </div>
                    </ListGroup.Item>
                    {
                        roomsByDate.rooms.map((room, index) => (
                            <Fragment key={index}>
                                <ListGroup.Item
                                    action
                                    active={room.id == currentRoomId}
                                    onClick={() => {
                                        navigate(`/managements-vendors-chats/${room.id}`, { replace: false });
                                    }}
                                >
                                    <div className="d-flex">
                                        <span className="fw-bold">
                                            {room.name}
                                        </span>
                                        <span className="ms-auto fw-bold">
                                            {room.vendor?.companyName}
                                        </span>
                                    </div>
                                    <div className="d-flex">
                                        <span className="fw-semibold">
                                            {displayedName(room.latestMessage)}
                                        </span>
                                        <span className="ms-auto text-muted">
                                            {moment(room.latestMessageAt || room.updatedAt).format('HH:mm')}
                                        </span>
                                    </div>
                                    <div>
                                        <dt className="text-truncate text-muted">
                                            {room.latestMessage?.content}
                                        </dt>
                                    </div>
                                </ListGroup.Item>
                            </Fragment>
                        ))
                    }
                </ListGroup>
            </li>
        </Fragment>
    )
}

export default ChatRoomList