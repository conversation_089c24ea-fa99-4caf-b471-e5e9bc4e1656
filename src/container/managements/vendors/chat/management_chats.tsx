import { FC, Fragment, useEffect, useState } from "react"
import { <PERSON><PERSON>, Card, Col, Form, Modal, Row } from "react-bootstrap"
import { useNavigate, useParams } from "react-router-dom"
import LazySelect from "../../../../components/lazy-select/lazy-select"
import Card<PERSON>eader<PERSON>ithBack from "../../../../components/table-title/card-header-with-back"
import { useCreateChatRoomMutation } from "../../../../services/chat/chat"
import { useLazyGetVendorsListsQuery } from "../../../../services/vendors"
import { Ws } from "../../../../utils/socket"
import ChatMessageList from "./chat_message_list"
import ChatRoomList from "./chat_room_list"

interface ManagementChatsProps { }

const ManagementChats: FC<ManagementChatsProps> = ({
}) => {
    const { id: roomId } = useParams()

    const [newUpdatedRoom, setNewUpdatedRoom] = useState<TChatRoom | null>(null)
    const [receivedUpdatedRoom, setReceivedUpdatedRoom] = useState<TChatRoom | null>(null)

    useEffect(() => {
        if (!receivedUpdatedRoom) { return }

        if (!!receivedUpdatedRoom.vendorId) {
            setNewUpdatedRoom(receivedUpdatedRoom)
        }

    }, [receivedUpdatedRoom])

    useEffect(() => {
        const ws = new Ws()
        const socket = ws.socket('chat-rooms')

        socket.connect()

        const onConnect = () => {
            console.log('onConnect');
        }

        const onDisconnect = () => {
            console.log('onDisconnect');
        }

        const onEvent = (value) => {
            console.log('onEvent', value);
            setReceivedUpdatedRoom(value.data)
        }

        socket.on('connect', onConnect);
        socket.on('disconnect', onDisconnect);
        socket.on('event', onEvent);

        return () => {
            socket.disconnect()
            socket.off('connect', onConnect);
            socket.off('disconnect', onDisconnect);
            socket.off('event', onEvent);
        };
    }, [])

    const [createRoomModalShown, setCreateRoomModalShown] = useState(false)

    const [vendorSelect] = useLazyGetVendorsListsQuery()
    const [detailsFormData, setDetailsFormData] = useState({
        name: '',
        vendorId: '',
    })

    const navigate = useNavigate()
    const [createRoom, { isLoading: isCreatingRoom }] = useCreateChatRoomMutation()
    const handleCreateClick = () => {
        createRoom(detailsFormData)
            .unwrap()
            .then((res) => {
                navigate(`/managements-vendors-chats/${res.id}`, { replace: false });
                setNewUpdatedRoom(res)
                setCreateRoomModalShown(false)
            })
            .catch((error) => console.log(error))
    }


    return (
        <Fragment>
            <Card className="custom-card">
                <Card.Header>
                    <CardHeaderWithBack
                        title="Vendor Chats"
                        route=''
                    />
                    <Card.Subtitle>
                        <Button
                            variant="primary-light"
                            className=""
                            onClick={() => setCreateRoomModalShown(true)}
                        >
                            Create
                            <i className="bi bi-plus-lg ms-2" />
                        </Button>
                    </Card.Subtitle>
                </Card.Header>
            </Card>

            <Modal
                show={createRoomModalShown}
                onHide={() => setCreateRoomModalShown(false)}
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        Room
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <div className="mb-3">
                        <Form.Label>
                            Room Name
                        </Form.Label>
                        <Form.Control
                            onChange={(e) => setDetailsFormData({ ...detailsFormData, name: e.target.value })}
                        />
                    </div>

                    <div>
                        <Form.Label>
                            Vendor
                        </Form.Label>

                        <div style={{ width: '-webkit-fill-available' }}>
                            <LazySelect
                                isClearable
                                selectionFunction={vendorSelect}
                                label={value => value.companyName}
                                getSelectedOptions={value => setDetailsFormData({ ...detailsFormData, vendorId: value?.id })}
                            />
                        </div>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <Button
                        disabled={!detailsFormData.name || !detailsFormData.vendorId}
                        onClick={handleCreateClick}
                        className="w-25"
                    >
                        {
                            isCreatingRoom
                                ? <span className="spinner-border spinner-border-sm mx-0" />
                                : 'Create'
                        }

                    </Button>
                </Modal.Footer>
            </Modal>

            <Row>
                <Col lg={3}>
                    <ChatRoomList
                        currentRoomId={roomId}
                        newUpdatedRoom={newUpdatedRoom}
                    />
                </Col>
                <Col lg={9}>
                    {
                        roomId
                            ? <ChatMessageList
                                roomId={roomId}
                            />
                            : <Fragment>
                                <Card className="custom-card">
                                    <Card.Header>
                                        <Card.Title>
                                            Select a chat to start messaging
                                        </Card.Title>
                                    </Card.Header>
                                    <Card.Body
                                        style={{ height: "calc(100vh - 300px)" }}
                                    >

                                    </Card.Body>
                                </Card>
                            </Fragment>
                    }
                </Col>
            </Row>
        </Fragment>
    )
}

export default ManagementChats