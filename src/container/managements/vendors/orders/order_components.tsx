import { Dropdown } from "react-bootstrap"
import { EVendorOrderStatus } from "../../../../utils/constant/vendor-order"

export const statusColor = {
    [EVendorOrderStatus.PENDING]: 'primary',
    [EVendorOrderStatus.CONFIRMED]: 'success',
    [EVendorOrderStatus.READY_TO_SHIP]: 'warning',
    [EVendorOrderStatus.IN_TRANSIT]: 'info',
    [EVendorOrderStatus.HELD]: 'dark',
    [EVendorOrderStatus.CANCELLED]: 'danger',
}

export const mainStatusButton = (status, handleStatusChangeClick) => {
    switch (status) {
        case (EVendorOrderStatus.IN_TRANSIT): {
            return {
                action: () => handleStatusChangeClick?.(EVendorOrderStatus.DELIVERED),
                text: <span>Delivered</span>
            }
        }
        case (EVendorOrderStatus.DELIVERED):
        case (EVendorOrderStatus.RTO): {
            return {
                action: () => handleStatusChangeClick?.(EVendorOrderStatus.IN_TRANSIT),
                text: <span>Move to Transit</span>
            }
        }
        case (EVendorOrderStatus.CONFIRMED): {
            return {
                action: () => handleStatusChangeClick?.(EVendorOrderStatus.READY_TO_SHIP),
                text: <span>Ready To Ship</span>
            }
        }
        case (EVendorOrderStatus.READY_TO_SHIP): {
            return {
                action: () => handleStatusChangeClick?.(EVendorOrderStatus.IN_TRANSIT),
                text: <span>Move To Transit</span>
            }
        }
        case (EVendorOrderStatus.HELD):
        case (EVendorOrderStatus.CANCELLED): {
            return {
                action: () => handleStatusChangeClick?.(EVendorOrderStatus.PENDING),
                text: <span>Move to Pending</span>
            }
        }
        case (EVendorOrderStatus.PENDING):
        default: {
            return {
                action: () => handleStatusChangeClick?.(EVendorOrderStatus.CONFIRMED),
                text: <span>Confirm</span>
            }
        }
    }
}

export const subStatusButtons = (status, handleStatusChangeClick) => {
    const rtoButton = <Dropdown.Item
        className="btn-sm"
        onClick={() => handleStatusChangeClick?.(EVendorOrderStatus.RTO)}
    >
        RTO
    </Dropdown.Item>

    const holdButton = <Dropdown.Item
        className="btn-sm"
        onClick={() => handleStatusChangeClick?.(EVendorOrderStatus.HELD)}
    >
        Hold
    </Dropdown.Item>

    const cancelButton = <Dropdown.Item
        className="btn-sm"
        onClick={() => handleStatusChangeClick?.(EVendorOrderStatus.CANCELLED)}
    >
        Cancel
    </Dropdown.Item>

    switch (status) {
        case (EVendorOrderStatus.IN_TRANSIT): {
            return <Dropdown.Menu>
                {rtoButton}
                {holdButton}
            </Dropdown.Menu>
        }
        case (EVendorOrderStatus.DELIVERED):
        case (EVendorOrderStatus.RTO): {
            return null
        }
        case (EVendorOrderStatus.HELD): {
            return <Dropdown.Menu>
                {cancelButton}
            </Dropdown.Menu>
        }
        case (EVendorOrderStatus.CANCELLED): {
            return null
        }
        case (EVendorOrderStatus.CONFIRMED):
        case (EVendorOrderStatus.READY_TO_SHIP):
        case (EVendorOrderStatus.PENDING):
        default: {
            return <Dropdown.Menu>
                {holdButton}
                {cancelButton}
            </Dropdown.Menu>
        }
    }
}

export const shippingCarrierOptions = [
    {
        label: 'Frequently Used Carriers',
        options: [
            { label: 'UPS', value: 'UPS' },
            { label: 'FedEx', value: 'FedEx' },
            { label: 'DHL Express', value: 'DHL Express' },
            { label: 'USPS', value: 'USPS' },
        ]
    }
]