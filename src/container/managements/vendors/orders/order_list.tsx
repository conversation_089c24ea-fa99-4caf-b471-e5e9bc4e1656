import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>, Card, Carousel, Col, Dropdown, Form, InputGroup, Modal, Row, Table } from "react-bootstrap";

import moment from "moment";
import { <PERSON> } from "react-router-dom";
import Swal from "sweetalert2";
import LazySelect from "../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import { useLazySelectProductVendorQuery } from "../../../../services/product/product_vendor";
import { useLazyListVendorOrdersQuery, useUpdateVendorOrderMutation } from "../../../../services/vendor-order";
import { currencySymbol } from "../../../../utils/constant/currency";
import { getAllErrorMessages } from "../../../../utils/errors";
import { mainStatusButton, statusColor, subStatusButtons } from "./order_components";

interface ManagementVendorOrdersListProps {
    active?: boolean

    status?: string
    setStatusCounts: (value: any) => void

    search: any,
    setSearch: (value: any) => void

    filters: any,
    setFilters: (value: any) => void
}

const ManagementVendorOrdersList: FC<ManagementVendorOrdersListProps> = ({
    active,
    status, setStatusCounts,
    search, setSearch,
    filters, setFilters,
}) => {
    const [page, setPage] = useState(1);
    const [limit] = useState(10);
    const [lastPage, setLastPage] = useState(20);
    const [total, setTotal] = useState(20);

    // const [err, setErr] = useState<any>({});
    const [isLoading, setIsLoading] = useState(false);

    const [trigger] = useLazyListVendorOrdersQuery();

    const [refresh, setRefresh] = useState(false)

    const [orders, setOrders] = useState<TVendorOrder[]>([]);

    useEffect(() => {
        if (!active) { return }

        setIsLoading(true);
        // @ts-ignore
        trigger({ page, limit, search, status, vendorId: filters.vendor?.id })
            .unwrap()
            .then((res) => {
                setOrders(res?.data || []);
                setLastPage(res?.meta?.lastPage);
                setTotal(res?.meta?.total)
                setStatusCounts({
                    ...res?.meta?.statusCounts,
                })
            })
            .finally(() => {
                setIsLoading(false);
            });
    }, [page, limit, search, refresh, active, filters]);

    const [selectVendor] = useLazySelectProductVendorQuery()

    return (
        <Fragment>
            {isLoading && <LoadingOverlay />}
            <Card.Body className="overflow-auto">
                <div className="app-container">
                    <Row>
                        <Col lg={9} className="mb-3">
                            <Form.Control
                                type="search"
                                placeholder="Search..."
                                onChange={(e) => setSearch(e.target.value)}
                            />
                        </Col>

                        <Col lg={3} className="mb-3">
                            <LazySelect
                                isClearable
                                selectionFunction={selectVendor}
                                label={value => value.companyName}
                                initialSelectedOptions={filters.vendor}
                                getSelectedOptions={value => setFilters({
                                    ...filters,
                                    vendor: value
                                })}
                            />
                        </Col>
                    </Row>

                    <Table className="table table-bordered text-nowrap border-bottom mb-3">
                        <thead>
                            <tr>
                                <th className="text-center">Order</th>
                                <th className="text-center">Products</th>
                                <th className="text-center">Customer</th>
                                <th className="text-center">Payment</th>
                                <th className="text-center">Address</th>
                                <th className="text-center">Fulfillment</th>
                                <th className="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {orders.map((order) => (
                                <Fragment key={order.id}>
                                    <ReadOnlyRow
                                        order={order}
                                        refresh={() => setRefresh(!refresh)}
                                    />
                                </Fragment>
                            ))}
                        </tbody>
                    </Table>
                    <PaginationBar
                        page={page}
                        setPage={setPage}
                        limit={limit}
                        lastPage={lastPage}
                        total={total}
                    />
                </div>
            </Card.Body>

        </Fragment>
    );
};



const ReadOnlyRow = ({
    order,
    refresh,
}: any) => {

    const [orderNameHovered, setOrderNameHovered] = useState(false)
    const [vendorNameHovered, setVendorNameHovered] = useState(false)
    const [customerNameHovered, setCustomerNameHovered] = useState(false)

    const [orderDetailsModalShown, setOrderDetailsModalShown] = useState(false)

    const [paymentDetailsModalShown, setPaymentDetailsModalShown] = useState(false)

    const [shipmentAddressModalShown, setShipmentAddressModalShown] = useState(false)

    // const [fulfillmentDetailsModalShown, setFulfillmentDetailsModalShown] = useState(false)

    const [updateOrder] = useUpdateVendorOrderMutation()
    const [updatingStatus, setUpdatingStatus] = useState(false)
    const handleStatusChangeClick = (status: string) => {
        setUpdatingStatus(true)
        updateOrder({ id: order.id, status })
            .then(() => {
                refresh?.()
            })
            .catch((error) => Swal.fire(getAllErrorMessages(error).messages[0]))
            .finally(() => setUpdatingStatus(false))
    }

    const [trackingInfo, setTrackingInfo] = useState<{
        trackingCompany: string,
        trackings: {
            id: number,
            number: string,
            url: string
        }[]
    } | null>(null)

    useEffect(() => {
        if (order) {
            const numbers = order.fulfillment?.trackingNumbers.split(',')
            const urls = order.fulfillment?.trackingUrls.split(',')

            const longestArray = (numbers?.length || 0) > (urls?.length || 0) ? numbers : urls

            const trackings: { id: number, number: string, url: string }[] = []
            for (let i = 0; i < (longestArray?.length || 0); i++) {
                trackings.push({ id: Date.now() + i, number: numbers[i] || '', url: urls[i] || '' })
            }

            setTrackingInfo({
                trackingCompany: order.fulfillment?.trackingCompany || '',
                trackings,
            })
        }
    }, [order])

    return (
        <Fragment>
            <tr>
                <td className="text-center">
                    <div>
                        <Link
                            to={`/managements-vendors-orders/${order.id}`}
                            className={`text-${orderNameHovered ? 'info text-decoration-underline' : 'primary'}`}
                            onMouseEnter={() => setOrderNameHovered(true)}
                            onMouseLeave={() => setOrderNameHovered(false)}
                        >
                            {order.order?.name}
                        </Link>
                    </div>
                    <div>
                        {moment(order.createdAt).format('YYYY-MM-DD')}
                    </div>
                    <div>
                        {moment(order.createdAt).format('HH:mm:ss')}
                    </div>
                    <div className="text-uppercase">
                        <Link
                            to={`/managements-vendors/${order.vendor?.id}`}
                            className={`text-${vendorNameHovered ? 'info text-decoration-underline' : 'primary'}`}
                            onMouseEnter={() => setVendorNameHovered(true)}
                            onMouseLeave={() => setVendorNameHovered(false)}
                        >
                            {order.vendor?.companyName}
                        </Link>
                    </div>
                </td>
                <td className="text-center" style={{ width: 300 }}>
                    <div className="text-primary mb-1">
                        <span
                            style={{ cursor: 'pointer' }}
                            onClick={() => setOrderDetailsModalShown(true)}
                        >
                            {order.orderDetailsCount} Product{order.orderDetailsCount > 1 ? 's' : ''}
                        </span>
                    </div>
                    <div>
                        <Carousel indicators={false}>
                            {order.orderDetails.map((details, index) => (
                                <Carousel.Item key={index}>
                                    <div className="avatar avatar-xl bg-dark-transparent">
                                        {
                                            details.variant?.image?.src
                                                ?
                                                <img
                                                    src={details.variant?.image?.src || ''}
                                                    className=""
                                                />
                                                : "IMG"
                                        }
                                    </div>
                                    <div>
                                        <div>
                                            {details.variant.title}
                                        </div>
                                        <div>
                                            {details.variant.sku} - Quantities: {details.quantity}
                                        </div>
                                    </div>
                                </Carousel.Item>
                            ))}
                        </Carousel>
                    </div>
                </td>
                <td className="text-center">
                    <div>
                        <Link
                            to={`/managements-users/details/${order.order?.user?.id}`}
                            className={`text-${customerNameHovered ? 'info text-decoration-underline' : 'primary'}`}
                            onMouseEnter={() => setCustomerNameHovered(true)}
                            onMouseLeave={() => setCustomerNameHovered(false)}
                        >
                            {order.order?.user?.fullname}
                        </Link>
                    </div>
                    <div>
                        {order.order?.user?.email}
                    </div>
                    <div>
                        {order.order?.user?.phone}
                    </div>
                </td>
                <td className="text-center">
                    <div className="text-primary">
                        <span
                            style={{ cursor: 'pointer' }}
                            onClick={() => setPaymentDetailsModalShown(true)}
                        >
                            Order Value
                        </span>
                    </div>
                    <div>
                        {currencySymbol[order.order?.currency]} {order.totalPrice}
                    </div>
                    <div className="text-uppercase">
                        {order.order?.financialStatus}
                    </div>
                </td>
                <td className="text-center">
                    {
                        order.order?.shipping
                            ?
                            <div>
                                <div className="text-primary">
                                    <span
                                        style={{ cursor: 'pointer' }}
                                        onClick={() => setShipmentAddressModalShown(true)}
                                    >
                                        Ship To
                                    </span>
                                </div>
                                <div>
                                    {order.order?.shipping?.city}, {order.order?.shipping?.province}
                                </div>
                                <div>
                                    {order.order?.shipping?.country}
                                </div>
                            </div>
                            :
                            <div>
                                No Shipping Address
                            </div>
                    }
                </td>
                <td className="text-center">
                    {/* <div className="text-primary">
                        <span
                            style={{ cursor: 'pointer' }}
                            onClick={() => setFulfillmentDetailsModalShown(true)}
                        >
                            Fulfillment
                        </span>
                    </div> */}
                    <div>{trackingInfo?.trackingCompany}</div>
                    {
                        trackingInfo?.trackings?.map((tracking, index) => (
                            <div key={index}>
                                <Link
                                    to={tracking.url}
                                    className="text-decoration-underline text-info text-muted mb-3"
                                >
                                    {tracking.number}
                                </Link>
                            </div>
                        ))
                    }
                </td>
                <td className="text-center">
                    <Badge
                        className={`text-capitalize fs-5 rounded-pill mb-1 bg-${statusColor[order.status]}-transparent`}
                    >
                        {order.status}
                    </Badge>
                    <Row>
                        <Col></Col>
                        <Col>
                            <InputGroup>
                                <Button
                                    className="btn-sm"
                                    onClick={() => mainStatusButton(order.status, handleStatusChangeClick).action?.()}
                                >
                                    {
                                        updatingStatus
                                            ? <i className="spinner-border spinner-border-sm mx-3" />
                                            : mainStatusButton(order.status, handleStatusChangeClick).text
                                    }
                                </Button>
                                {
                                    subStatusButtons(order.status, handleStatusChangeClick) &&
                                    <Dropdown>
                                        <Dropdown.Toggle className="btn-sm dropdown-toggle-split" />
                                        {subStatusButtons(order.status, handleStatusChangeClick)}
                                    </Dropdown>
                                }
                            </InputGroup>
                        </Col>
                        <Col></Col>
                    </Row>
                </td>
            </tr>

            <OrderDetailsModal
                show={orderDetailsModalShown}
                onHide={() => setOrderDetailsModalShown(false)}
                order={order}
            />

            <PaymentDetailsModal
                show={paymentDetailsModalShown}
                onHide={() => setPaymentDetailsModalShown(false)}
                order={order}
            />

            <ShipmentAddressModal
                show={shipmentAddressModalShown}
                onHide={() => setShipmentAddressModalShown(false)}
                order={order}
            />

            {/* <FulfillmentDetailsModal
                show={fulfillmentDetailsModalShown}
                onHide={() => setFulfillmentDetailsModalShown(false)}
                order={order}
            /> */}

        </Fragment>
    );
};

const OrderDetailsModal = ({
    show,
    onHide,

    order,
}: any) => {

    const [productNameHovered, setProductNameHovered] = useState(false)

    return (
        <Fragment>
            <Modal
                show={show}
                onHide={() => { onHide?.() }}

                centered
                size='xl'
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        Order {order.order?.name}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body className="overflow-auto">
                    <Table className="table table-bordered text-nowrap border-bottom mb-3">
                        <thead>
                            <tr>
                                <th className="text-center">Image</th>
                                <th className="text-center">Product</th>
                                <th className="text-center">Quantity</th>
                                <th className="text-center">Price</th>
                                <th className="text-center">Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            {order.orderDetails.map((details, index) => (
                                <tr key={index}>
                                    <td className="text-center">
                                        <div className="avatar avatar-xxl bg-dark-transparent">
                                            {
                                                details.variant?.image?.src
                                                    ?
                                                    <img
                                                        src={details.variant?.image?.src || ''}
                                                        className=""
                                                    />
                                                    : "IMG"
                                            }
                                        </div>
                                    </td>
                                    <td className="text-center">
                                        <div>{details.variant?.sku}</div>
                                        <div>{details.variant?.barcode}</div>
                                        <div>{details.variant?.title}</div>
                                        <div>
                                            <Link
                                                to={`/managements-products/${details.variant?.productId}`}
                                                className={`text-${productNameHovered ? 'info text-decoration-underline' : "primary"}`}
                                                onMouseEnter={() => setProductNameHovered(true)}
                                                onMouseLeave={() => setProductNameHovered(false)}
                                            >
                                                {details.variant?.product?.title}
                                            </Link>
                                        </div>
                                    </td>
                                    <td className="text-center">{details.quantity}</td>
                                    <td className="text-center">{currencySymbol[order.order?.currency]} {details.price}</td>
                                    <td className="text-center">{currencySymbol[order.order?.currency]} {details.quantity * details.price}</td>
                                </tr>
                            ))}
                        </tbody>
                    </Table>
                </Modal.Body>
            </Modal>
        </Fragment>
    )
}

const PaymentDetailsModal = ({
    show,
    onHide,

    order,
}: any) => {

    return (
        <Fragment>
            <Modal
                show={show}
                onHide={() => { onHide?.() }}

                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        Order {order.order?.name}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Row>
                        <Col>
                            <div className="fw-bold">Financial Status</div>
                            <div className="fw-bold">Total Discounts</div>
                            <div className="fw-bold">Total Tax</div>
                            <div className="fw-bold">Total Shipping</div>
                            <div className="fw-bold">Subtotal Price</div>
                            <div className="fw-bold">Total Price</div>
                            <div className="fw-bold">Current Total Price</div>
                        </Col>
                        <Col>
                            <div className="text-capitalize">{order.order?.financialStatus}</div>
                            <div>{order.totalDiscounts}</div>
                            <div>{order.totalTax}</div>
                            <div>{order.totalShipping}</div>
                            <div>{order.subtotalPrice}</div>
                            <div>{order.totalPrice}</div>
                            <div>{order.currentTotalPrice}</div>
                        </Col>
                    </Row>
                </Modal.Body>
            </Modal>
        </Fragment>
    )
}

const ShipmentAddressModal = ({
    show,
    onHide,

    order,
}: any) => {

    return (
        <Fragment>
            <Modal
                show={show}
                onHide={() => { onHide?.() }}

                centered
            >
                <Modal.Header closeButton>
                    <Modal.Title>
                        Order {order.order?.name}
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <Row>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                First Name
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.firstName}
                            </div>
                        </Col>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Last Name

                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.lastName}
                            </div>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Name
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.name}
                            </div>
                        </Col>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Company
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.company}
                            </div>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Address 1
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.address1}
                            </div>
                        </Col>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Address 2
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.address2}
                            </div>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                City
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.city}
                            </div>
                        </Col>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Zip
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.zip}
                            </div>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Province
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.province}
                            </div>
                        </Col>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Country
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.country}
                            </div>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Province Code
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.provinceCode}
                            </div>
                        </Col>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Country Code
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.countryCode}
                            </div>
                        </Col>
                    </Row>
                    <Row>
                        <Col className="mb-3">
                            <Form.Label className="fw-bold">
                                Latitude
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.latitude}
                            </div>
                        </Col>
                        <Col className="mb-3">
                            <Form.Label>
                                Longitude
                            </Form.Label>
                            <div className="text-muted">
                                {order.order?.shipping?.longitude}
                            </div>
                        </Col>
                    </Row>
                </Modal.Body>
            </Modal>
        </Fragment>
    )
}

export default ManagementVendorOrdersList;
