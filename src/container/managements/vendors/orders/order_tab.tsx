import { FC, Fragment, useState } from "react";
import { Card, Nav, Tab } from "react-bootstrap";
import ManagementVendorOrdersList from "./order_list";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { EVendorOrderStatus } from "../../../../utils/constant/vendor-order";
import { debounce } from "lodash";


interface ManagementVendorOrdersTabProps { }

const ManagementVendorOrdersTab: FC<ManagementVendorOrdersTabProps> = () => {

  const [orderListTabs] = useState([
    EVendorOrderStatus.PENDING,
    EVendorOrderStatus.CONFIRMED,
    EVendorOrderStatus.READY_TO_SHIP,
    EVendorOrderStatus.IN_TRANSIT,
    EVendorOrderStatus.DELIVERED,
    EVendorOrderStatus.RTO,
    EVendorOrderStatus.HELD,
    EVendorOrderStatus.CANCELLED,
    '',
  ])

  const [statusCounts, setStatusCounts] = useState({
    [EVendorOrderStatus.PENDING]: 0,
    [EVendorOrderStatus.CONFIRMED]: 0,
    [EVendorOrderStatus.READY_TO_SHIP]: 0,
    [EVendorOrderStatus.IN_TRANSIT]: 0,
    [EVendorOrderStatus.DELIVERED]: 0,
    [EVendorOrderStatus.RTO]: 0,
    [EVendorOrderStatus.HELD]: 0,
    [EVendorOrderStatus.CANCELLED]: 0,
    '': 0,
  })

  const [activeEventKey, setActiveEventkey] = useState<EVendorOrderStatus | string>(EVendorOrderStatus.PENDING)


  const [search, setSearch] = useState("");
  const setDebouncedSearch = debounce((value) => setSearch(value), 1000)
  
  const [filters, setFilters] = useState({
    vendor:null
  })

  return (
    <Fragment>
      <Tab.Container
        activeKey={activeEventKey}
        onSelect={(eventKey) => setActiveEventkey(eventKey!)}>
        <Card>
          <Card.Header>
            <CardHeaderWithBack
              title="Order Management"
              route=""
            ></CardHeaderWithBack>
          </Card.Header>
        </Card>

        <Card>
          <Card.Header>
            <Nav
              className="nav nav-style-1 nav-pills"
              defaultActiveKey={EVendorOrderStatus.PENDING}
            >
              {
                orderListTabs.map((status, index) => (
                  <Fragment key={index}>
                    <Nav.Link eventKey={status}>
                      <span className="text-capitalize">
                        {status || 'all'}
                      </span>
                      {
                        statusCounts[status] != 0 &&
                        <span className="badge bg-secondary ms-1 rounded-pill">
                          {statusCounts[status]}
                        </span>
                      }
                    </Nav.Link>
                  </Fragment>
                ))
              }
            </Nav>
          </Card.Header>
          <Tab.Content>
            {
              orderListTabs.map((status, index) => (
                <Fragment key={index}>
                  <Tab.Pane eventKey={status}>
                    <ManagementVendorOrdersList
                      active={activeEventKey == status}
                      status={status}
                      setStatusCounts={setStatusCounts}
                      search={search}
                      setSearch={setDebouncedSearch}
                      filters={filters}
                      setFilters={setFilters}
                    />
                  </Tab.Pane>
                </Fragment>
              ))
            }
          </Tab.Content>
        </Card>
      </Tab.Container>
    </Fragment>
  );
};


export default ManagementVendorOrdersTab;
