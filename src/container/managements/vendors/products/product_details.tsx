import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Form, Row } from "react-bootstrap";
import ReactQuill from "react-quill";
import { useNavigate, useParams } from "react-router-dom";
import MediaDropzone from "../../../../components/dropzone/media_dropzone";
import LazyCreatableSelect from "../../../../components/lazy-select/lazy-creatable-select";
// import LazySelect from "../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useUploadMultipleMutation } from "../../../../services/media";
import {
  useCreateProductMutation,
  useDeleteProductByIdMutation,
  useLazyGetProductByIdQuery,
  // useLazyListProductVendorsQuery,
  useUpdateProductMutation,
} from "../../../../services/product";
import { getAllErrorMessages } from "../../../../utils/errors";
import ManagementVendorProductDetailsOptionsVariants from "./product_details_options_variants";
import Swal from "sweetalert2";
import LazySelect from "../../../../components/lazy-select/lazy-select";
import { useLazySelectProductCategoryQuery } from "../../../../services/product-category";
import { useLazySelectProductTypeQuery } from "../../../../services/product/product_type";
import { useLazySelectProductTagQuery } from "../../../../services/product/product_tag";

interface ManagementVendorProductDetailsProps { }

const ManagementVendorProductDetails: FC<ManagementVendorProductDetailsProps> = () => {
  const { id } = useParams();
  const [isAdd, setIsAdd] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const [getProduct] = useLazyGetProductByIdQuery();
  const [createProduct] = useCreateProductMutation();
  const [updateProduct] = useUpdateProductMutation();
  const [deleteProduct] = useDeleteProductByIdMutation();

  // const [selectProductVendor] = useLazySelectProductVendorQuery()
  // const [selectProductType] = useLazySelectProductTypeQuery()
  // const [selectProductTag] = useLazySelectProductTagQuery()

  // const [listVendors] = useLazyListProductVendorsQuery()
  const [listCategories] = useLazySelectProductCategoryQuery()
  const [listTypes] = useLazySelectProductTypeQuery()
  const [listTags] = useLazySelectProductTagQuery()

  const [upload] = useUploadMultipleMutation();
  const [mediasUploaded, setMediasUploaded] = useState<TMedia[]>([]);
  const [mediasUploading, setMediasUploading] = useState<any>([]);

  const [detailsFormData, setDetailsFormData] = useState<Partial<TProduct>>({});
  // const [variants, setVariants] = useState<Partial<TProductVariant>[]>([]);
  const [err, setErr] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [productOptions, setProductOptions] = useState<TProductOption[]>([])
  const [productVariants, setProductVariants] = useState<TProductVariant[]>([]);

  const navigate = useNavigate();
  const returnToListPage = () => {
    navigate("/products");
  };

  useEffect(() => {
    setIsAdd(false);
    setIsEdit(false);

    if (id == 'new') {
      setIsAdd(true);

      const defaultOption: any = {
        id: `${Date.now()}`,
        name: 'Title',
        position: 1,
      }

      const defaultOptionValue = {
        id: `${Date.now() + 1}`,
        optionId: defaultOption.id,
        value: 'Default Title',
        position: 1,
      }

      setProductOptions([{
        ...defaultOption,
        productOptionValues: [defaultOptionValue]
      }])

      setProductVariants([{
        id: `${Date.now() + 2}`,
        // title: 'Default Title',
        position: 1,
        inventoryQuantity: 0,
        price: 0,
        weight: 0,
        weightUnit: 'g',
        // @ts-ignore
        optionValues: [{ ...defaultOptionValue, option: defaultOption }]
      }])
    } else {
      setIsEdit(true);
    }
  }, [id]);

  const prepareReceivedData = (data) => {
    const details: any = {
      ...data
    };


    if (data.images?.length > 0) {
      setMediasUploaded(data.images.map((image) => {
        return { url: image.src || image.url }
      }))
    }

    if (data.options) {
      setProductOptions(data.options)
    }

    if (data.variants) {
      setProductVariants(data.variants)
    }

    setDetailsFormData(details);
  }

  // const [pendingData, setPendingData] = useState<Partial<TProduct>>({})
  // const [originalData, setOriginalData] = useState<Partial<TProduct>>({})
  useEffect(() => {
    switch (true) {
      case isAdd: {
        break;
      }
      case isEdit: {
        setIsLoading(true);
        getProduct(id || "")
          .unwrap()
          .then((res) => {
            // setOriginalData(res)
            if (res.pendingChanges) {
              // setPendingData(res.pendingChanges)
              prepareReceivedData(res.pendingChanges)
            } else {
              prepareReceivedData(res)
            }
          })
          .catch((error) => {
            setErr(getAllErrorMessages(error));
          })
          .finally(() => {
            setIsLoading(false);
          });
        break;
      }
      default:
        break;
    }
  }, [isAdd, isEdit]);

  const handleDetailsFormChange = (event: any) => {
    if (event) { event.preventDefault(); }

    const fieldName = event.target.getAttribute("name");
    const fieldValue = event.target.value;

    const newFormData: any = { ...detailsFormData };
    newFormData[fieldName] = fieldValue;

    setDetailsFormData(newFormData);

    setErr({})
  };

  const handleFilesMediaChange = async (fileList: File[]) => {
    const validFiles = fileList.filter((file) => file);
    if (validFiles.length > 0) {
      upload({ file: validFiles })
        .unwrap()
        .then((uploadedFiles) => {
          setMediasUploaded((prev) => [...prev, ...uploadedFiles]);
          setMediasUploading([]);
        })
        .catch((error) => {
          const formattedErrors = error?.data?.errors?.reduce(
            (acc: any, curr: any) => {
              acc[curr.field] = curr.message;
              return acc;
            },
            {}
          );
          setErr(formattedErrors);
        });
    }
  };

  const removeFile = (index: number) => {
    if (index < mediasUploading.length) {
      setMediasUploading((prev) => prev.filter((_, i) => i !== index));
    } else {
      const uploadedIndex = index - mediasUploading.length;
      setMediasUploaded((prev) => prev.filter((_, i) => i !== uploadedIndex));
    }
  };

  useEffect(() => {
    if (mediasUploading.length > 0) {
      handleFilesMediaChange(mediasUploading);
    }
  }, [mediasUploading]);

  const prepareSubmitForm = () => {
    const preparedForm: any = {
      title: detailsFormData.title,
      description: detailsFormData.description,
      status: detailsFormData.status || 'draft',

      productType: typeof detailsFormData.productType != 'string'
        ? detailsFormData.productType
        : ({ name: detailsFormData.productType }),
      tags: (detailsFormData.tags || []).map(tag => typeof tag != 'string' ? tag : ({ name: tag })),
      category: detailsFormData.category,

      shopifyProductId: detailsFormData.shopifyProductId,

      images: mediasUploaded.map((media, index) => ({
        src: media.url,
        position: index + 1
      })),

      options: productOptions,
      variants: productVariants,
    }

    return preparedForm;
  };

  const handleAddFormSubmit = async (event: any, status?: 'draft' | 'active') => {
    if (event) { event.preventDefault(); }

    const newProduct = prepareSubmitForm()

    let confirm = true
    if (!status || status == 'draft') {
      newProduct.status = 'draft'
    } else if (status == 'active') {
      confirm = await new Promise((resolve) => {
        Swal.fire({
          icon: 'warning',
          title: 'Submit your product for approval?',
          showConfirmButton: true,
        }).then((result) => {
          if (result.isConfirmed) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }

    if (!confirm) { return }

    setIsLoading(true);
    createProduct({ ...newProduct })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        returnToListPage();
      })
      .catch((error) => {
        console.log(error);

        setErr(getAllErrorMessages(error));

        Swal.fire('Errors', getAllErrorMessages(error).messages[0], 'error')
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleUpdateFormSubmit = async (event: any, approval?: 'requiresApproval') => {
    if (event) { event.preventDefault(); }

    const updatedProduct = prepareSubmitForm()

    let confirm = true
    if (approval == 'requiresApproval') {
      confirm = await new Promise((resolve) => {
        Swal.fire({
          icon: 'warning',
          title: 'Submit your product for approval?',
          showConfirmButton: true,
        }).then((result) => {
          if (result.isConfirmed) {
            updatedProduct.requiresApproval = true
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    }

    if (!confirm) { return }

    setIsLoading(true);
    updateProduct({ id: id || "", ...updatedProduct })
      .unwrap()
      .then(() => {
        setDetailsFormData({});
        returnToListPage();
      })
      .catch((error) => {
        console.log(error);

        setErr(getAllErrorMessages(error));

        Swal.fire('Errors', getAllErrorMessages(error).messages[0], 'error')
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const handleDeleteClick = () => {
    deleteSweetAlert({
      id: id || "",
      deleteAction: deleteProduct,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => returnToListPage()),
      finalAction: (() => setIsLoading(false)),
    })
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Product Details"
                route="/products/"
              />
            </Card.Header>
            <div>
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}
            </div>
            <Card.Body>
              <Row>
                <Col lg={6}>
                  <Row>
                    <Col lg={9} className="mb-3">
                      <Form.Label>Product Name*</Form.Label>
                      <Form.Control
                        type="text"
                        required
                        name="title"
                        placeholder="Title"
                        value={detailsFormData?.title || ""}
                        onChange={handleDetailsFormChange}
                        isInvalid={err?.validationErrors?.title}
                      />
                      <Form.Control.Feedback className="invalid-feedback">
                        {err?.validationErrors?.title}
                      </Form.Control.Feedback>
                    </Col>

                    <Col lg={3} className="mb-3">
                      <Form.Label>Status</Form.Label>
                      <Form.Select
                        name="status"
                        value={detailsFormData?.status || ""}
                        onChange={handleDetailsFormChange}
                      >
                        <option value='draft'>Draft</option>
                        <option value='active'>Active</option>
                      </Form.Select>
                    </Col>
                  </Row>
                  <Row>
                    <Col className="mb-3">
                      <Form.Label>Product Category</Form.Label>
                      <LazySelect
                        selectionFunction={listCategories}
                        label={(value) => value.name}
                        initialSelectedOptions={detailsFormData?.category}
                        getSelectedOptions={(value) =>
                          setDetailsFormData((prev) => ({ ...prev, category: value }))
                        }
                      />
                      <div
                        hidden={!err?.validationErrors?.categoryId}
                        className="text-danger small"
                        role="alert">
                        {err?.validationErrors?.categoryId}
                      </div>
                    </Col>
                    <Col className="mb-3">
                      <Form.Label>Product Type</Form.Label>
                      <LazyCreatableSelect
                        selectionFunction={listTypes}
                        label={(value) => value.name}
                        initialSelectedOptions={detailsFormData?.productType}
                        getSelectedOptions={(value) =>
                          setDetailsFormData((prev) => ({ ...prev, productType: value }))
                        }
                      />
                      <div
                        hidden={!err?.validationErrors?.productTypeId}
                        className="text-danger small"
                        role="alert">
                        {err?.validationErrors?.productTypeId}
                      </div>
                    </Col>
                  </Row>
                  <Col className="mb-3">
                    <Form.Label>Tags</Form.Label>
                    <LazyCreatableSelect
                      isMulti
                      selectionFunction={listTags}
                      label={(value) => value.name}
                      initialSelectedOptions={detailsFormData?.tags}
                      getSelectedOptions={(value) =>
                        setDetailsFormData((prev) => {
                          return { ...prev, tags: value }
                        })
                      }
                    />
                  </Col>
                </Col>
                <Col lg={6}>
                  <MediaDropzone
                    uploadedFiles={mediasUploaded}
                    uploadFunction={setMediasUploading}
                    removeFile={removeFile}
                    // acceptType={["image/*"]}
                  />
                </Col>
              </Row>
              <Col>
                <Form.Label>Description</Form.Label>
                <ReactQuill
                  theme="snow"
                  value={detailsFormData?.description || ""}
                  onChange={(value) => setDetailsFormData((prev) => ({
                    ...prev,
                    description: value
                  }))}
                />
              </Col>
            </Card.Body>
          </Card>

          <ManagementVendorProductDetailsOptionsVariants
            productOptions={productOptions}
            setProductOptions={setProductOptions}
            productVariants={productVariants}
            setProductVariants={setProductVariants}
            mediasUploaded={mediasUploaded}
            setMediasUploaded={setMediasUploaded}
          />
        </Col>
      </Row>
      <Card className="custom-card">
        <Card.Header>
          <Card.Title></Card.Title>
          <Card.Subtitle>
            {isAdd ? (
              <Fragment>
                <Button
                  variant="warning-light"
                  className="ms-2"
                  onClick={handleAddFormSubmit}
                >
                  Save as Draft<i className="bi bi-file-earmark ms-2"></i>
                </Button>

                {
                  detailsFormData.status == 'active' &&
                  <Button
                    variant="primary-light"
                    className="ms-2"
                    onClick={(e) => {
                      handleAddFormSubmit(e, 'active')
                    }}
                  >
                    Submit for Approval<i className="bi bi-person-check ms-2"></i>
                  </Button>
                }
              </Fragment>
            ) : (
              <Fragment>
                <Button
                  className="ms-2"
                  variant="danger-light"
                  onClick={handleDeleteClick}
                >
                  Delete<i className="bi bi-trash ms-2"></i>
                </Button>

                <Button
                  className="ms-2"
                  variant="info-light"
                  onClick={handleUpdateFormSubmit}
                >
                  Save Changes<i className="bi bi-download ms-2"></i>
                </Button>

                {
                  detailsFormData.status == 'active' &&
                  <Button
                    variant="primary-light"
                    className="ms-2"
                    onClick={(e) => {
                      handleUpdateFormSubmit(e, 'requiresApproval')
                    }}
                  >
                    Submit for Approval<i className="bi bi-person-check ms-2"></i>
                  </Button>
                }
              </Fragment>
            )}
            <Button
              variant=""
              className="btn-light border-dark ms-2"
              onClick={() => returnToListPage()}
            >
              Cancel<i className="bi bi-x-lg ms-2"></i>
            </Button>
          </Card.Subtitle>
        </Card.Header>
      </Card>
    </Fragment>
  );
};

export default ManagementVendorProductDetails;
