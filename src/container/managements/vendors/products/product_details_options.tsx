import { FC, Fragment, useState } from "react";
import { Button, Card, Form, InputGroup, OverlayTrigger, Tooltip } from "react-bootstrap";

interface ManagementVendorProductDetailsOptionsProps {
  productOptions: TProductOption[],
  setProductOptions: (value: TProductOption[]) => void,

  productOptionCreated?: (value: TProductOption) => void,
  productOptionChanged?: (value: TProductOption) => void,
  productOptionDeleted?: (value: TProductOption) => void,

  optionValueCreated?: (value: TProductOptionValue, option: TProductOption) => void,
  optionValueChanged?: (value: TProductOptionValue, option: TProductOption) => void,
  optionValueDeleted?: (value: TProductOptionValue) => void,
}

export const ManagementVendorProductDetailsOptionsList: FC<ManagementVendorProductDetailsOptionsProps> = ({
  productOptions, setProductOptions,
  productOptionCreated, productOptionChanged, productOptionDeleted,
  optionValueCreated, optionValueChanged, optionValueDeleted,
}) => {

  const setSingleProductOption = (inProdOp: TProductOption) => {
    setProductOptions(productOptions?.map((prodOp => prodOp.id != inProdOp.id ? prodOp : inProdOp)) || [])
    productOptionChanged?.(inProdOp)
  }

  const deleteSingleProductOption = (inProdOp: TProductOption) => {
    setProductOptions(
      productOptions
        .filter(prodOp => prodOp.id != inProdOp.id)
        .map((prodOp, index) => ({
          ...prodOp, position: index + 1
        }))
    )
    productOptionDeleted?.(inProdOp)
  }

  const [productOptionLength, setProductOptionLength] = useState(0)
  const handleAddProductOptionClick = () => {
    let currentProductOptionLength = 0
    if (!productOptionLength) {
      currentProductOptionLength = productOptions.length + 1
    } else {
      currentProductOptionLength = productOptionLength + 1
    }

    setProductOptionLength(currentProductOptionLength)

    const newProductOption: any = {
      id: `${Date.now()}`,
      name: `Option ${currentProductOptionLength}`,
      position: productOptions.length + 1,
    }

    const newProductOptionValue = {
      id: `${Date.now() + 1}`,
      optionId: newProductOption.id,
      value: `Option ${currentProductOptionLength} - Value 1`,
      position: 1,
    }

    newProductOption.productOptionValues = [newProductOptionValue]

    // @ts-ignore
    setProductOptions([...productOptions, newProductOption,])

    // @ts-ignore
    productOptionCreated?.(newProductOption)
  }

  return (
    <Card className="custom-card">
      <Card.Header>
        <Card.Title>
          Options
        </Card.Title>
        <Card.Subtitle>
          {
            productOptions.length >= 3
              ?
              <OverlayTrigger overlay={<Tooltip>Maximum 3 options</Tooltip>}>
                <div>
                  <Button
                    disabled
                    variant="primary-light"
                  >
                    <i className="bi bi-plus-lg" /> Option
                  </Button>
                </div>
              </OverlayTrigger>
              :
              <Button
                variant="primary-light"
                onClick={handleAddProductOptionClick}
              >
                <i className="bi bi-plus-lg" /> Option
              </Button>
          }
        </Card.Subtitle>
      </Card.Header>
      <Card.Body>
        {productOptions?.map((option, index) =>
          <Fragment key={index}>
            <SingleProductOption
              option={option}
              setOption={setSingleProductOption}
              deleteOption={deleteSingleProductOption}

              optionValueCreated={optionValueCreated}
              optionValueChanged={optionValueChanged}
              optionValueDeleted={optionValueDeleted}

              disabledDeleteButton={(productOptions?.length || 0) < 2}
            />
          </Fragment>
        )}
      </Card.Body>
    </Card>
  );
};

const SingleProductOption = ({
  option,
  setOption,
  deleteOption,
  optionValueCreated,
  optionValueChanged,
  optionValueDeleted,
  disabledDeleteButton = false,
}) => {

  const setSingleOptionValue = (inOpVal: TProductOptionValue) => {
    setOption({
      ...option,
      productOptionValues: option?.productOptionValues.map(prodOpVal => prodOpVal.id != inOpVal.id ? prodOpVal : inOpVal)
    })

    optionValueChanged?.(inOpVal, option)
  }

  const deleteSingleOptionValue = (inOpVal: TProductOptionValue) => {
    setOption({
      ...option,
      productOptionValues: option?.productOptionValues
        .filter(prodOpVal => prodOpVal.id != inOpVal.id)
        .map((prodOpVal, index) => ({ ...prodOpVal, position: index + 1 }))
    })

    optionValueDeleted?.(inOpVal)
  }

  const [optionValueLength, setOptionValueLength] = useState(0)
  const handleAddOptionValueClick = () => {
    let currentOptionValueLength = 0
    if (!optionValueLength) {
      currentOptionValueLength = (option?.productOptionValues?.length || 0) + 1
    } else {
      currentOptionValueLength = optionValueLength + 1
    }

    setOptionValueLength(currentOptionValueLength)

    const { productOptionValues, ...optionWithoutValues } = option

    const newOptionValue = {
      id: `${Date.now()}`,
      value: `${option.name} - Value ${currentOptionValueLength}`,
      position: (option?.productOptionValues?.length || 0) + 1,
      optionId: option.id,
      option: optionWithoutValues
    }

    setOption({
      ...option,
      productOptionValues: [
        ...(option?.productOptionValues || []),
        newOptionValue
      ]
    })

    optionValueCreated?.(newOptionValue, option)
  }

  return <Fragment>
    <Card className="custom-card border">
      <Card.Header>
        <Form.Label>Option Name</Form.Label>
        <InputGroup>
          <Form.Control
            value={option.name}
            onChange={(e) => setOption({ ...option, name: e.target.value })}
          />
          <OverlayTrigger placement="top" overlay={<Tooltip>Delete Option</Tooltip>}>
            <Button
              disabled={disabledDeleteButton}
              variant="danger"
              className="btn-sm"
              onClick={() => deleteOption(option)}
            >
              <span className="ri-delete-bin-7-line fs-14"></span>
            </Button>
          </OverlayTrigger>
        </InputGroup>
      </Card.Header>
      <Card.Body>
        <Form.Label>Option Values</Form.Label>
        {
          option.productOptionValues?.map((optionValue, index) => (
            <Fragment key={index}>
              <SingleProductOptionValue
                optionValue={optionValue}
                setOptionValue={setSingleOptionValue}
                deleteOptionValue={deleteSingleOptionValue}
                disabledDeleteButton={(option.productOptionValues?.length || 0) < 2}
              />
            </Fragment>
          ))
        }
        <div className="d-grid">
          <Button
            variant="light"
            className="btn btn-primary-light"
            onClick={handleAddOptionValueClick}
          >
            <i className="bi bi-plus-lg" /> Value
          </Button>
        </div>
      </Card.Body>
    </Card>
  </Fragment>
}

const SingleProductOptionValue = ({
  optionValue,
  setOptionValue,
  deleteOptionValue,
  disabledDeleteButton = false,
}) => {

  return <Fragment>
    <InputGroup>
      <Form.Control
        value={optionValue.value}
        onChange={(e) => {
          setOptionValue({ ...optionValue, value: e.target.value })
        }}
      />
      <OverlayTrigger placement="top" overlay={<Tooltip>Delete Value</Tooltip>}>
        <Button
          disabled={disabledDeleteButton}
          variant="danger-light"
          className="btn-sm"
          onClick={() => deleteOptionValue(optionValue)}
        >
          <span className="ri-delete-bin-7-line fs-14"></span>
        </Button>
      </OverlayTrigger>
    </InputGroup>
  </Fragment>
}