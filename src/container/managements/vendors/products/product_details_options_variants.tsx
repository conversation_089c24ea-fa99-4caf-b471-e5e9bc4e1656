import { FC, Fragment } from "react";
import { Col, Row } from "react-bootstrap";
import { ManagementVendorProductDetailsOptionsList } from "./product_details_options";
import { ManagementVendorProductDetailsVariantList } from "./product_details_variant_list";

interface ManagementVendorProductDetailsOptionsVariantsProps {
    productOptions: TProductOption[],
    setProductOptions: (values: TProductOption[]) => void,
    productVariants: TProductVariant[],
    setProductVariants: (values: TProductVariant[]) => void,

    mediasUploaded: TMedia[],
    setMediasUploaded: (values: TMedia[]) => void
}

const ManagementVendorProductDetailsOptionsVariants: FC<ManagementVendorProductDetailsOptionsVariantsProps> = ({
    productOptions, setProductOptions,
    productVariants, setProductVariants,
    mediasUploaded, setMediasUploaded
}) => {

    const handleProductOptionCreated = (option: TProductOption) => {

        setProductVariants(
            productVariants.map(
                vari => ({
                    ...vari,
                    optionValues: [
                        ...vari.optionValues,
                        {
                            ...option.productOptionValues[0],
                            optionId: option.id,
                            option: option
                        }
                    ]
                })
            )
        )
    }

    const handleProductOptionChanged = (option: TProductOption) => {
        setProductVariants(
            productVariants.map(
                vari => ({
                    ...vari,
                    optionValues: vari.optionValues.map(
                        opVal => opVal.optionId != option.id ? opVal : {
                            ...opVal,
                            option: option
                        }
                    )
                })
            )
        )
    }

    const handleProductOptionDeleted = (option: TProductOption) => {
        setProductVariants(
            productVariants
                .filter(
                    vari => {
                        const firstOptionValue = option.productOptionValues[0]
                        const variantWithFirstOptionValue = vari.optionValues.find(opVal => opVal.id == firstOptionValue.id)
                        return !!variantWithFirstOptionValue
                    }
                )
                .map(
                    vari => ({
                        ...vari,
                        optionValues: vari.optionValues.filter(
                            opVal => opVal.optionId != option.id
                        )
                    })
                )
        )
    }

    const handleOptionValueCreated = (optionValue: TProductOptionValue, option: TProductOption) => {
        const { productOptionValues, ...optionWithoutValues } = option
        // @ts-ignore
        setProductVariants([
            ...productVariants,
            ...productVariants
                .filter(
                    vari => {
                        const firstOptionValue = productOptionValues[0]
                        const variantWithFirstOptionValue = vari.optionValues.find(opVal => opVal.id == firstOptionValue.id)
                        return !!variantWithFirstOptionValue
                    }
                )
                .map(
                    (vari, index) => ({
                        ...vari,
                        id: `${Date.now() + index}`,
                        optionValues: vari.optionValues
                            .map(
                                opVal => opVal.optionId != optionValue.optionId ? opVal : {
                                    ...optionValue,
                                    option: optionWithoutValues
                                }
                            )
                    })
                )
        ])
    }

    const handleOptionValueChanged = (optionValue: TProductOptionValue, option: TProductOption) => {
        const { productOptionValues, ...optionWithoutValues } = option

        setProductVariants(
            // @ts-ignore
            productVariants
                .map(vari => ({
                    ...vari,
                    optionValues: vari.optionValues
                        .map(
                            opVal => opVal.id != optionValue.id ? opVal : {
                                ...optionValue,
                                option: optionWithoutValues
                            }
                        )
                }))
        )
    }

    const handleOptionValueDeleted = (optionValue: TProductOptionValue) => {
        setProductVariants(
            productVariants
                .filter(vari =>
                    !vari.optionValues.find(opVal => opVal.id == optionValue.id)
                )
        )
    }

    return (
        <Fragment>
            <Row>
                <Col lg={4}>
                    <ManagementVendorProductDetailsOptionsList
                        productOptions={productOptions}
                        setProductOptions={setProductOptions}

                        productOptionCreated={handleProductOptionCreated}
                        productOptionChanged={handleProductOptionChanged}
                        productOptionDeleted={handleProductOptionDeleted}

                        optionValueCreated={handleOptionValueCreated}
                        optionValueChanged={handleOptionValueChanged}
                        optionValueDeleted={handleOptionValueDeleted}
                    />
                </Col>

                <Col lg={8}>
                    <ManagementVendorProductDetailsVariantList
                        productVariants={productVariants}
                        setProductVariants={setProductVariants}
                        mediasUploaded={mediasUploaded}
                        setMediasUploaded={setMediasUploaded}
                    />
                </Col>
            </Row>
        </Fragment >
    );
};

export default ManagementVendorProductDetailsOptionsVariants;
