import { FC, Fragment, useState } from "react";
import { Button, Card, OverlayTrigger, Table, Tooltip } from "react-bootstrap";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import { useDeleteProductVariantByIdMutation } from "../../../../services/product-variant";
import { ManagementVendorProductDetailsVariantModal } from "./product_details_variant_modal";

interface ManagementVendorProductDetailsVariantListProps {
  productVariants: TProductVariant[]
  setProductVariants: (value: TProductVariant[]) => void

  mediasUploaded: TMedia[]
  setMediasUploaded: (values: TMedia[]) => void
}

export const ManagementVendorProductDetailsVariantList: FC<ManagementVendorProductDetailsVariantListProps> = ({
  productVariants, setProductVariants, mediasUploaded, setMediasUploaded
}) => {

  // const [isInitial, setIsInitial] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});

  const [deleteById] = useDeleteProductVariantByIdMutation();

  const setProductVariant = (variant: TProductVariant) => {
    setProductVariants(
      productVariants.map(prodVar => prodVar.id != variant.id ? prodVar : variant)
    )
  }

  const handleDeleteClick = (productVariant: TProductVariant) => {
    deleteSweetAlert({
      id: productVariant.id,
      deleteAction: deleteById,
      confirmText: `Do you want to delete ${productVariant.title} ?`,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => setProductVariants(productVariants.filter((prodVar => prodVar.id != productVariant.id)))),
      finalAction: (() => setIsLoading(false)),
    })
  };

  return (
    <Card className="custom-card">
      <Card.Header>
        <Card.Title>
          Variants
        </Card.Title>
        {/* <Card.Subtitle>
          <Button
            variant="primary-light"
          >
            <i className="bi bi-plus-lg" /> Variant
          </Button>
        </Card.Subtitle> */}
      </Card.Header>
      <Card.Body className="overflow-auto">
        <Table className="table table-bordered text-nowrap border-bottom mb-3 position-relative">
          {isLoading && <LoadingOverlay />}
          <thead>
            <tr>
              <th className="text-center">Image</th>
              <th className="text-center">Title</th>
              <th className="text-center">SKU</th>
              <th className="text-center">Quantity</th>
              <th className="text-center">Price</th>
              <th className="text-center">Compare at Price</th>
              <th className="text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            {productVariants.map((productVariant: TProductVariant) => (
              <Fragment key={productVariant.id}>
                <ReadOnlyRow
                  productVariant={productVariant}
                  setProductVariant={setProductVariant}
                  handleDeleteClick={handleDeleteClick}
                  mediasUploaded={mediasUploaded}
                  setMediasUploaded={setMediasUploaded}
                  err={err}
                  setErr={setErr}
                />
              </Fragment>
            ))}
          </tbody>
        </Table>
      </Card.Body>
    </Card>
  );
};

const ReadOnlyRow = ({
  productVariant,
  setProductVariant,
  mediasUploaded,
  setMediasUploaded,
  // handleDeleteClick,
}: // err,
  // setErr,
  any) => {
  // const navigate = useNavigate();
  const [modalShown, setModalShown] = useState(false)

  const variantTitle = [...productVariant.optionValues]
    .sort((a, b) =>
      (a.option?.position || 0) - (b.option?.position || 0)
    )
    .map(opVal => opVal.value)
    .join(' / ')

  return (
    <Fragment>
      <tr>
        <td
          className="text-center"
          style={{
            textAlign: "center",
            padding: "10px",
            width: "10px",
            height: "10px",
          }}>
          <p className="avatar avatar-lg bg-dark-transparent my-auto">
            {
              productVariant.image?.src ?
                <img
                  src={productVariant.image?.src}
                  alt="img"
                  style={{
                    display: "block",
                    margin: "0 auto",
                    width: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                />
                : "img"
            }
          </p>
        </td>
        <td
          className="text-center"
          style={{ cursor: 'pointer' }}
          onClick={() => setModalShown(true)}
        >
          {variantTitle}
        </td>
        <td className="text-center">{productVariant.sku}</td>
        <td className="text-center">{productVariant.inventoryQuantity}</td>
        <td className="text-center">{productVariant.price}</td>
        <td className="text-center">{productVariant.compareAtPrice}</td>
        <td className="text-center">
          <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
            <Button
              variant="primary-light"
              className="btn btn-warning-light btn-sm ms-2"
              onClick={() => setModalShown(true)}
            >
              <span className="ri-edit-line fs-14"></span>
            </Button>
          </OverlayTrigger>

          {/* <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
            <Button
              variant="primary-light"
              className="btn btn-danger-light btn-sm ms-2"
            // onClick={() => handleDeleteClick(productVariant)}
            >
              <span className="ri-delete-bin-7-line fs-14"></span>
            </Button>
          </OverlayTrigger> */}
        </td>
      </tr>

      <ManagementVendorProductDetailsVariantModal
        show={modalShown}
        onHide={() => {
          setModalShown(false)
        }}
        productVariant={productVariant}
        setProductVariant={setProductVariant}
        variantTitle={variantTitle}
        mediasUploaded={mediasUploaded}
        setMediasUploaded={setMediasUploaded}
      />
    </Fragment>
  );
};
