import { FC, Fragment, useEffect, useState } from "react";
import { But<PERSON>, Col, Form, Modal, Row } from "react-bootstrap";
import { NumericFormat } from "react-number-format";
import Swal from "sweetalert2";
import LogoDropzone from "../../../../components/dropzone/logo_dropzone";
import { useUploadMultipleMutation } from "../../../../services/media";
import { getAllErrorMessages } from "../../../../utils/errors";

interface ManagementVendorProductDetailsVariantModalProps {
  show?: boolean
  onHide?: () => void

  productVariant: TProductVariant
  setProductVariant: (value: TProductVariant) => void
  variantTitle,

  mediasUploaded: TMedia[]
  setMediasUploaded: (values: TMedia[]) => void
}

export const ManagementVendorProductDetailsVariantModal: FC<ManagementVendorProductDetailsVariantModalProps> = ({
  show, onHide, productVariant, setProductVariant, variantTitle, mediasUploaded, setMediasUploaded
}) => {

  const [variant, setVariant] = useState(productVariant)
  const [refreshImage, setRefreshImage] = useState(false)

  useEffect(() => {
    if (productVariant.image?.src) {
      const variantMedia = mediasUploaded.find(med => med.url == productVariant.image?.src)
      if (variantMedia) {
        setChangeImageSelectedMedia(variantMedia)
        setVariantDetailsSelectedMedia(variantMedia)
      } else {
        setChangeImageSelectedMedia(null)
        setVariantDetailsSelectedMedia(null)
      }
    }
  }, [refreshImage])

  useEffect(() => {
    setVariant(productVariant)
  }, [productVariant])

  const handleDetailsFormChange = (e) => {
    if (e) { e.preventDefault() }

    const name = e.target.name
    let value = e.target.value

    setVariant({
      ...variant,
      [name]: value
    })

  }

  const [thumbnailUploaded] = useState<TMedia | null>(null);
  const [upload] = useUploadMultipleMutation();
  const handleThumbnailUpload = async (file: File) => {
    try {
      const [uploadedLogo] = await upload({ file: [file] }).unwrap();
      // setThumbnailUploaded(uploadedLogo);
      setMediasUploaded([...mediasUploaded, uploadedLogo])
      setChangeImageSelectedMedia(uploadedLogo)
    } catch (error) {
      Swal.fire('Errors!', getAllErrorMessages(error).messages[0], 'error')
    }
  };

  const removeThumbnail = () => {
    // setThumbnailUploaded(null);
  };

  const [isPhysical, setIsPhysical] = useState(!!(productVariant.weight || productVariant.weightUnit))


  const [imageModalShown, setImageModalShown] = useState(false)
  // shown on image select modal
  const [changeImageSelectedMedia, setChangeImageSelectedMedia] = useState<TMedia | null>(null)
  const handleSelectMedia = (media: TMedia) => {
    if (media.url == changeImageSelectedMedia?.url) {
      setChangeImageSelectedMedia(null)
    } else {
      setChangeImageSelectedMedia(media)
    }
  }

  // shown on variant detail modal
  const [variantDetailsSelectedMedia, setVariantDetailsSelectedMedia] = useState<TMedia | null>(null)

  return (
    <Fragment>
      <Modal
        show={show}
        onHide={() => onHide?.()}
        centered
        size='xl'
      >
        <Modal.Header closeButton>
          <Modal.Title>
            Edit Variant
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Col>
            <Row>
              <Col lg={4} className="text-center mb-3">
                <Col>
                  <Form.Label className="text-primary">Image</Form.Label>
                </Col>

                <Col className="avatar avatar-xxxl bg-dark-transparent mb-2">
                  {
                    variantDetailsSelectedMedia
                      ?
                      <img
                        src={variantDetailsSelectedMedia.url}
                        alt="img"
                        style={{
                          display: "block",
                          margin: "0 auto",
                          width: "100%",
                          height: "100%",
                          objectFit: "cover",
                        }}
                      />
                      :
                      //  productVariant.image?.src ?
                      //   <img
                      //     src={productVariant.image?.src}
                      //     alt="img"
                      //     style={{
                      //       display: "block",
                      //       margin: "0 auto",
                      //       width: "100%",
                      //       height: "100%",
                      //       objectFit: "cover",
                      //     }}
                      //   />
                      //   :
                      'img'
                  }
                </Col>

                <Col>
                  <span
                    style={{ cursor: 'pointer' }}
                    onClick={() => setImageModalShown(true)}
                  >
                    Change
                  </span>
                </Col>
              </Col>

              <Col lg={8} className="mb-3">
                <Form.Label className="text-primary">Options</Form.Label>

                <Row>
                  <Col lg={6} className="mb-3">
                    {
                      [...variant.optionValues]
                        .sort((a, b) =>
                          (a.option?.position || 0) - (b.option?.position || 0)
                        )
                        .map((optionValue, index) => (
                          <Col key={index} className="mb-3">
                            <Form.Label>
                              {optionValue.option?.name}
                            </Form.Label>
                            <Form.Control
                              disabled
                              value={optionValue.value}
                            />
                          </Col>
                        ))
                    }
                  </Col>
                </Row>
              </Col>
            </Row>
          </Col>

          <div className="border-bottom border-primary mb-3" />

          <Col>
            <div className="mb-3">
              <Form.Label className="text-primary">Title</Form.Label>
              <Form.Control
                disabled
                placeholder="Title"
                value={variantTitle}
              />
            </div>

          </Col>

          <div className="border-bottom border-primary mb-3" />

          <Row>
            <Col md={6} className="border-end border-primary">

              <Col className="border-bottom border-primary mb-3">
                <Form.Label className="text-primary">Inventory</Form.Label>

                <Row>
                  <Col lg={6} className="mb-3">
                    <Form.Label>SKU</Form.Label>
                    <Form.Control
                      type="text"
                      required
                      name="sku"
                      placeholder="SKU"
                      value={variant?.sku || ""}
                      onChange={handleDetailsFormChange}
                    // isInvalid={err?.validationErrors?.title}
                    />
                    {/* <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.title}
                  </Form.Control.Feedback> */}
                  </Col>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Barcode</Form.Label>
                    <Form.Control
                      type="text"
                      required
                      name="barcode"
                      placeholder="Barcode"
                      value={variant?.barcode || ""}
                      onChange={handleDetailsFormChange}
                    // isInvalid={err?.validationErrors?.title}
                    />
                    {/* <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.title}
                  </Form.Control.Feedback> */}
                  </Col>
                </Row>

                <Row>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Quantity</Form.Label>
                    <NumericFormat
                      className="form-control"
                      required
                      name="inventoryQuantity"
                      placeholder="Quantity"
                      value={variant?.inventoryQuantity}
                      decimalScale={0}
                      allowNegative={false}
                      allowLeadingZeros={false}
                      onChange={handleDetailsFormChange}
                    // isInvalid={err?.validationErrors?.title}
                    />
                    {/* <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.title}
                  </Form.Control.Feedback> */}
                  </Col>
                </Row>

                <Col>
                  <div className="form-check form-switch mb-2">
                    <input
                      className="form-check-input form-checked-primary"
                      type="checkbox"
                      checked={variant.inventoryPolicy == 'continue'}
                      onChange={e => {
                        const policy = e.target.checked ? 'continue' : 'deny'
                        setVariant({ ...variant, inventoryPolicy: policy })
                      }}
                    />
                    <label>
                      Continue selling when out of stock
                    </label>
                  </div>
                </Col>
              </Col>
            </Col>

            <Col md={6}>
              <Col className="border-bottom border-primary mb-3">
                <Form.Label className="text-primary">Pricing</Form.Label>

                <Row>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Price</Form.Label>
                    <NumericFormat
                      className="form-control"
                      required
                      name="price"
                      placeholder="Price"
                      value={variant?.price}
                      decimalScale={2}
                      allowNegative={false}
                      valueIsNumericString={false}
                      onChange={handleDetailsFormChange}
                    // isInvalid={err?.validationErrors?.title}
                    />
                    {/* <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.title}
                  </Form.Control.Feedback> */}
                  </Col>
                  <Col lg={6} className="mb-3">
                    <Form.Label>Compare At Price</Form.Label>
                    <NumericFormat
                      className="form-control"
                      required
                      name="compareAtPrice"
                      placeholder="Compare At Price"
                      value={variant?.compareAtPrice}
                      decimalScale={2}
                      allowNegative={false}
                      onChange={handleDetailsFormChange}
                    // isInvalid={err?.validationErrors?.title}
                    />
                    {/* <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.title}
                  </Form.Control.Feedback> */}
                  </Col>
                </Row>
              </Col>

              <Col>
                <Row>
                  <Col>
                    <Form.Label className="text-primary">Package</Form.Label>
                  </Col>

                  <Col>
                    <div className="form-check form-switch mb-2">
                      <input
                        className="form-check-input form-checked-primary"
                        type="checkbox"
                        checked={isPhysical}
                        onChange={e => setIsPhysical(e.target.checked)}
                      />
                      <label>
                        Is Physical Product
                      </label>
                    </div>
                  </Col>
                </Row>
                {
                  isPhysical &&
                  <Row>
                    <Col lg={6} className="mb-3">
                      <Form.Label>Weight</Form.Label>
                      <NumericFormat
                        className="form-control"
                        required
                        name="weight"
                        placeholder="Weight"
                        value={variant?.weight}
                        decimalScale={0}
                        allowNegative={false}
                        onChange={handleDetailsFormChange}
                      // isInvalid={err?.validationErrors?.title}
                      />
                      {/* <Form.Control.Feedback className="invalid-feedback">
                    {err?.validationErrors?.title}
                    </Form.Control.Feedback> */}
                    </Col>
                    <Col lg={6} className="mb-3">
                      <Form.Label>Weight Unit</Form.Label>
                      <Form.Select
                        name='weightUnit'
                        onChange={handleDetailsFormChange}
                        value={variant?.weightUnit || ''}
                      >
                        <option value='g'>g</option>
                        <option value='kg'>kg</option>
                        <option value='lb'>lb</option>
                        <option value='oz'>oz</option>
                      </Form.Select>
                    </Col>
                  </Row>
                }
              </Col>
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer>
          <Button
            variant="warning-light"
            onClick={() => {
              const updatedVariant = { ...variant }

              if (!isPhysical) {
                updatedVariant.weight = null
                updatedVariant.weightUnit = null
              }

              if (variantDetailsSelectedMedia) {
                // @ts-ignore
                updatedVariant.image = {
                  src: variantDetailsSelectedMedia.url
                }
              } else {
                // @ts-ignore
                updatedVariant.image = null              
              }

              setProductVariant(updatedVariant)
              onHide?.()
            }}
          >
            Update
          </Button>
          {/* <Button
          variant="danger-light"
        >
          Delete
        </Button> */}
          <Button
            variant=""
            className="btn-light border-dark"
            onClick={() => {
              setVariant(productVariant)
              setRefreshImage(!refreshImage)
              onHide?.()
            }}
          >
            Cancel
          </Button>
        </Modal.Footer>
      </Modal >

      <Modal
        show={imageModalShown}
        onHide={() => setImageModalShown(false)}
        size="lg"
      >
        <Modal.Header closeButton className="bg-dark-transparent">
          <Modal.Title>Edit Image</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Row>
            {
              mediasUploaded.map((media, index) => (
                <Fragment key={index}>
                  <ReadOnlyRow
                    media={media}
                    onMouseClick={handleSelectMedia}
                    changeImageSelectedMedia={changeImageSelectedMedia}
                  />
                </Fragment>
              ))
            }
            <Col className="p-2">
              <LogoDropzone
                uploadedLogo={thumbnailUploaded}
                uploadFunction={handleThumbnailUpload}
                removeLogo={removeThumbnail}
                // text="Drag & Drop image here or Click to browse"
                styles={{
                  dropzone: {
                    width: "125px",
                    height: "125px",
                  },
                }}
                // preview={false}
              />
            </Col>
          </Row>
        </Modal.Body>
        <Modal.Footer className="bg-dark-transparent">
          <Button
            hidden={mediasUploaded.length == 0}
            variant=""
            className="btn-success-light"
            onClick={() => {
              setVariantDetailsSelectedMedia(changeImageSelectedMedia)
              setImageModalShown(false)
            }}
          >
            Select
          </Button>
          <Button
            variant=""
            className="btn-light ms-2"
            onClick={() => {
              setImageModalShown(false)
              setChangeImageSelectedMedia(null)
            }}
          >
            Cancel
          </Button>
        </Modal.Footer>

      </Modal>
    </Fragment>
  );
};

const ReadOnlyRow = ({
  media, onMouseClick, changeImageSelectedMedia
}) => {
  const [isHovered, setIsHovered] = useState(false)
  const [isSelected, setIsSelected] = useState(false)

  useEffect(() => {
    if (changeImageSelectedMedia?.url == media.url) {
      setIsSelected(true)
    } else {
      setIsSelected(false)
    }

  }, [changeImageSelectedMedia])

  return <Fragment>
    <Col xs={4} lg={2} className={`p-2 text-center position-relative ${(isSelected || isHovered) ? 'bg-dark-transparent' : ''}`}>
      <div
        className="avatar avatar-xxxl"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={() => {
          onMouseClick?.(media)
        }}
      >
        <img
          src={media.url}
          alt="img"
          style={{
            display: "block",
            margin: "0 auto",
            width: "100%",
            height: "100%",
            objectFit: "cover",
          }}
        />

      </div>

      <Form.Check
        type='checkbox'
        className="position-absolute top-0 start-0"
        checked={isSelected}
        onChange={() => onMouseClick?.(media)}
      />
    </Col>
  </Fragment>
}