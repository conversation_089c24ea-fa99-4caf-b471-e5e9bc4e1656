import { debounce } from "lodash";
import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Col, Dropdown, Form, OverlayTrigger, Row, Table, Tooltip } from "react-bootstrap";
import { <PERSON>, useNavigate } from "react-router-dom";
import LazySelect from "../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazySelectProductCategoryQuery } from "../../../../services/product-category";
import { useLazySelectProductTypeQuery } from "../../../../services/product/product_type";
import { useLazySelectProductTagQuery } from "../../../../services/product/product_tag";
import { useDeleteProductByIdMutation, useLazyListProductQuery } from "../../../../services/product";
import { useLazySelectProductVendorQuery } from "../../../../services/product/product_vendor";

interface ManagementVendorProductsListProps { }

const ManagementVendorProductsList: FC<ManagementVendorProductsListProps> = () => {
  const [page, setPage] = useState(1);
  const [limit] = useState(10);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20);

  const [search, setSearch] = useState("");
  const setSearchDebounced = debounce((value) => setSearch(value), 1000)

  // const [isInitial, setIsInitial] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});

  const [filters, setFilters] = useState<
    {
      status: string | null,
      vendorId: string | null
      categoryId: string | null,
      typeId: string | null,
      tagIds: string[] | null,
    }>({
      status: null,
      vendorId: null,
      categoryId: null,
      typeId: null,
      tagIds: null,
    })
  const [filtersActive, setFiltersActive] = useState(false)
  const [filtersChange, setFiltersChange] = useState(false)

  const [sortBy, setSortBy] = useState('newest')
  const sortByIcon = () => {
    let iconClassName = ''
    switch (sortBy) {
      case ('newest'): {
        iconClassName = "bi-sort-down"
        break
      }
      case ('oldest'): {
        iconClassName = "bi-sort-up"
        break
      }
      case ('a2z'): {
        iconClassName = "bi-sort-alpha-down"
        break
      }
      case ('z2a'): {
        iconClassName = "bi-sort-alpha-up"
        break
      }
    }

    return iconClassName
  }

  const [listCategories] = useLazySelectProductCategoryQuery()
  const [listTypes] = useLazySelectProductTypeQuery()
  const [listTags] = useLazySelectProductTagQuery()

  const [trigger] = useLazyListProductQuery();
  const [deleteById] = useDeleteProductByIdMutation();

  const [products, setProducts] = useState<TProduct[]>([]);

  const navigate = useNavigate();

  const loadData = () => {
    setIsLoading(true)
    // @ts-ignore
    trigger({ page, pageSize: limit, search, ...filters, sortBy, })
      .unwrap()
      .then((res) => {
        setProducts(res.data || [])
        setLastPage(res.meta.lastPage)
        setTotal(res.meta.total)

      })
      .catch((error) => console.log(error))
      .finally(() => setIsLoading(false))
  }

  useEffect(() => {
    loadData()
  }, [page, limit, search, filtersChange, sortBy])

  const [showAllVariants, setShowAllVariants] = useState(false)

  const handleDeleteClick = (product: TProduct) => {
    deleteSweetAlert({
      id: product.id,
      deleteAction: deleteById,
      confirmText: `Do you want to delete ${product.title} ?`,
      prepareAction: (() => setIsLoading(true)),
      finishAction: (() => loadData()),
    })
  };

  const checkFilterActive = () => {
    let active = false
    for (const filter of Object.values(filters)) {
      if (filter || (Array.isArray(filter) && filter.length > 0)) {
        active = true
        break
      }
    }
    return active
  }

  const handleFilterDropdownItemClick = (e) => {
    const name = e.target.id

    if (!name) { return }

    if (filters[name] === null) {
      const value = name == 'tagIds' ? [] : ''
      setFilters({ ...filters, [name]: value })
      setFiltersActive(true)
    } else {
      if (filters[name]) {
        setFiltersChange(!filtersChange)
      }
      const active = checkFilterActive()
      if (!active) { setFiltersActive(false) }

      setFilters({ ...filters, [name]: null })
    }
    e.currentTarget.blur()
  }

  const [selectVendor] = useLazySelectProductVendorQuery()

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Products Management"
                route=""
              ></CardHeaderWithBack>
              <div className="px-4 justify-content-end">
                <Button
                  variant="primary-light"
                  onClick={() => navigate("new")}
                >
                  Add<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </div>
            </Card.Header>
            <Card.Body className="border-bottom">
              <Row>
                <Col lg={9} className="mb-3">
                  <Form.Label>Search</Form.Label>
                  <Form.Control
                    type='search'
                    placeholder="Type here..."
                    onChange={(e) => setSearchDebounced(e.target.value)}
                  />
                </Col>
                <Col lg={3} className="mb-3">
                  <Form.Label>Vendor</Form.Label>
                  <LazySelect
                    isClearable
                    selectionFunction={selectVendor}
                    label={value => value.companyName}
                    getSelectedOptions={value => {
                      setFilters({
                        ...filters,
                        vendorId: value?.id
                      })
                      setFiltersChange(!filtersChange)
                    }}
                  />
                </Col>
              </Row>
              <Row>
                {
                  filters.status !== null &&
                  <Col lg={4} className="mb-3">
                    <Form.Label>Status</Form.Label>
                    <Form.Select
                      onChange={(e) => {
                        setFilters({ ...filters, status: e.target.value })
                        setFiltersChange(!filtersChange)
                      }}
                    >
                      <option value=''></option>
                      <option value='active'>Active</option>
                      <option value='draft'>Draft</option>
                      <option value='archived'>Archived</option>
                    </Form.Select>
                  </Col>
                }
                {
                  filters.categoryId !== null &&
                  <Col lg={4} className="mb-3">
                    <Form.Label>Category</Form.Label>
                    <LazySelect
                      isClearable
                      selectionFunction={listCategories}
                      label={(value) => value.name}
                      getSelectedOptions={(value) => {
                        setFilters((prev) => ({ ...prev, categoryId: value?.id || '' }))
                        setFiltersChange(!filtersChange)
                      }}
                    />
                  </Col>
                }

                {
                  filters.typeId !== null &&
                  <Col lg={4} className="mb-3">
                    <Form.Label>Type</Form.Label>
                    <LazySelect
                      isClearable
                      selectionFunction={listTypes}
                      label={(value) => value.name}
                      getSelectedOptions={(value) => {
                        setFilters((prev) => ({ ...prev, typeId: value?.id || '' }))
                        setFiltersChange(!filtersChange)
                      }}
                    />
                  </Col>
                }
              </Row>

              {filters.tagIds !== null &&
                <Row className="mb-3">
                  <Form.Label>Tags</Form.Label>
                  <LazySelect
                    isMulti
                    isClearable
                    selectionFunction={listTags}
                    label={(value) => value.name}
                    getSelectedOptions={(values) => {
                      setFilters((prev) => ({ ...prev, tagIds: values.map(value => value.id) }))
                      setFiltersChange(!filtersChange)
                    }}
                  />
                </Row>
              }

              <div className="d-flex">
                <Button
                  variant={showAllVariants ? "primary" : ''}
                  className="btn btn-primary-light rounded-pill"
                  onClick={(e) => {
                    setShowAllVariants(!showAllVariants)
                    e.currentTarget.blur()
                  }}
                >
                  {
                    showAllVariants
                      ? <i className="bi bi-dash-lg me-1" />
                      : <i className="bi bi-plus-lg me-1" />
                  }
                  <span>
                    {`${showAllVariants ? "Hide" : "Show"} All Variants`}
                  </span>
                </Button>
                <Dropdown>
                  <OverlayTrigger overlay={<Tooltip>Filters</Tooltip>}>
                    <Dropdown.Toggle
                      variant={`primary${filtersActive ? "" : "-light"}`}
                      className="btn-icon rounded-pill no-caret"
                    >
                      <i className="ri-filter-line" />
                    </Dropdown.Toggle>
                  </OverlayTrigger>
                  <Dropdown.Menu>
                    <Dropdown.Item
                      active={filters.status !== null}
                      id='status'
                      onClick={handleFilterDropdownItemClick}
                    >
                      Status
                    </Dropdown.Item>
                    <Dropdown.Item
                      active={filters.categoryId !== null}
                      id='categoryId'
                      onClick={handleFilterDropdownItemClick}
                    >
                      Category
                    </Dropdown.Item>
                    <Dropdown.Item
                      active={filters.typeId !== null}
                      id='typeId'
                      onClick={handleFilterDropdownItemClick}
                    >
                      Type
                    </Dropdown.Item>
                    <Dropdown.Item
                      active={filters.tagIds !== null}
                      id='tagIds'
                      onClick={handleFilterDropdownItemClick}
                    >
                      Tags
                    </Dropdown.Item>
                    {
                      filtersActive &&
                      <Fragment>
                        <Dropdown.Divider></Dropdown.Divider>
                        <Dropdown.Item
                          onClick={(e) => {
                            const active = checkFilterActive()

                            setFilters({
                              status: null,
                              vendorId: null,
                              categoryId: null,
                              typeId: null,
                              tagIds: null,
                            })
                            setFiltersActive(false)
                            e.currentTarget.blur()

                            if (active) { setFiltersChange(!filtersChange) }
                          }}
                        >
                          Clear Filters
                        </Dropdown.Item>
                      </Fragment>
                    }
                  </Dropdown.Menu>
                </Dropdown>
                <Dropdown>
                  <OverlayTrigger overlay={<Tooltip>Sort</Tooltip>}>
                    <Dropdown.Toggle
                      variant="primary-light"
                      className="btn-icon rounded-pill no-caret"
                    >
                      <i className={sortByIcon()} />
                    </Dropdown.Toggle>
                  </OverlayTrigger>
                  <Dropdown.Menu>
                    <Dropdown.Item
                      active={sortBy == 'newest'}
                      onClick={() => setSortBy('newest')}
                    >
                      Newest
                    </Dropdown.Item>
                    <Dropdown.Item
                      active={sortBy == 'oldest'}
                      onClick={() => setSortBy('oldest')}
                    >
                      Oldest
                    </Dropdown.Item>
                    <Dropdown.Item
                      active={sortBy == 'a2z'}
                      onClick={() => setSortBy('a2z')}
                    >
                      Title A-Z
                    </Dropdown.Item>
                    <Dropdown.Item
                      active={sortBy == 'z2a'}
                      onClick={() => setSortBy('z2a')}
                    >
                      Title Z-A
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            </Card.Body>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <Table className="table table-bordered text-nowrap border-bottom mb-3">
                  <thead>
                    <tr>
                      <th className="text-center">Image</th>
                      <th className="text-center">Title</th>
                      <th className="text-center">Vendor</th>
                      <th className="text-center">Category</th>
                      <th className="text-center">Type</th>
                      <th className="text-center">Status</th>
                      <th className="text-center">Inventory</th>
                      <th className="text-center">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.map((product: TProduct) => (
                      <Fragment key={product.id}>
                        <ReadOnlyRow
                          product={product}
                          handleDeleteClick={handleDeleteClick}
                          err={err}
                          setErr={setErr}
                          showAllVariants={showAllVariants}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <div>
                  <PaginationBar
                    page={page}
                    setPage={setPage}
                    limit={limit}
                    lastPage={lastPage}
                    total={total}
                  />
                </div>
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment >
  );
};

const ReadOnlyRow = ({
  product,
  handleDeleteClick,
  showAllVariants,
}: // err,
  // setErr,
  any) => {

  const [productNameHovered, setProductNameHovered] = useState(false)
  const [vendorNameHovered, setVendorNameHovered] = useState(false)

  const StatusColor = {
    active: 'success',
    draft: 'warning',
    archived: 'dark'
  }

  const inventoryCount = product.variants?.reduce((acc, vari) => acc + vari.inventoryQuantity, 0) || 0
  const variantCount = product.variants?.length || 0

  const [showVariants, setShowVariants] = useState(false)
  useEffect(() => {
    setShowVariants(showAllVariants)
  }, [showAllVariants])

  return (
    <Fragment>
      <tr>
        <td
          style={{
            textAlign: "center",
            padding: "10px",
            width: "10px",
            height: "10px",
          }}>
          <p className="avatar avatar-lg bg-dark-transparent my-auto">
            {
              product.image?.src
                ?
                <img
                  src={product.image.src || ""}
                  style={{
                    display: "block",
                    margin: "0 auto",
                    maxWidth: "100%",
                    height: "100%",
                    objectFit: "cover",
                  }}
                />
                : "IMG"
            }
          </p>
        </td>
        <td className="text-center">
          <div>
            <Link
              to={product.id}
              className={`text-${productNameHovered ? 'info text-decoration-underline' : 'primary'}`}
              onMouseEnter={() => setProductNameHovered(true)}
              onMouseLeave={() => setProductNameHovered(false)}
            >
              {product.title}
            </Link>
          </div>
        </td>
        <td className="text-center">
          <div>
            <Link
              to={`/managements-vendors/${product.vendor?.id}`}
              className={`text-${vendorNameHovered ? 'info text-decoration-underline' : 'primary'}`}
              onMouseEnter={() => setVendorNameHovered(true)}
              onMouseLeave={() => setVendorNameHovered(false)}
            >
              {product.vendor?.companyName}
            </Link>
          </div>
        </td>
        <td className="text-center">
          {product.category?.name}
        </td>
        <td className="text-center">
          {product.productType?.name}
        </td>
        <td className="text-center">
          <Badge
            className={`rounded-pill text-capitalize`}
            bg={`${StatusColor[product.status]}`}
          >
            {product.status}
          </Badge>
          <div>
            {
              product.pendingApproval == 0
                ? <span className="badge bg-success-transparent rounded-pill">Approved</span>
                : product.pendingApproval == 1 ?
                  <span className="badge bg-danger-transparent rounded-pill">Rejected</span>
                  : product.pendingApproval
                    ? <span className="badge bg-primary-transparent rounded-pill">Pending</span>
                    : product.pendingChanges
                      ? <span className="badge bg-info-transparent rounded-pill">Changes</span>
                      : ""
            }
          </div>
        </td>
        <td className="text-center">
          <Badge bg={inventoryCount > 0 ? 'success' : 'danger'}>
            {inventoryCount}
          </Badge>
          {" in "}
          <Badge bg="primary">
            {variantCount}
          </Badge>
          {` variant${variantCount > 1 ? 's' : ''}`}
        </td>
        <td className="text-center">
          <OverlayTrigger placement="top" overlay={<Tooltip>{`${showVariants ? "Hide" : "Show"} Variants`}</Tooltip>}>
            <Button
              variant={showVariants ? "primary" : ''}
              className="btn btn-primary-light btn-sm ms-2"
              onClick={(e) => {
                setShowVariants(!showVariants)
                e.currentTarget.blur()
              }}
            >
              {
                showVariants
                  ? <span className="ri-subtract-line fs-14" />
                  : <span className="ri-add-line fs-14" />
              }
            </Button>
          </OverlayTrigger>
          <OverlayTrigger placement="top" overlay={<Tooltip>Edit</Tooltip>}>
            <Link to={product.id}>
              <Button
                variant=""
                className="btn btn-warning-light btn-sm ms-2"
              >
                <span className="ri-edit-line fs-14"></span>
              </Button>
            </Link>
          </OverlayTrigger>

          <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
            <Button
              variant=""
              className="btn btn-danger-light btn-sm ms-2"
              onClick={() => handleDeleteClick(product)}
            >
              <span className="ri-delete-bin-7-line fs-14"></span>
            </Button>
          </OverlayTrigger>
        </td>
      </tr>
      {
        showVariants &&
        <tr>
          <th colSpan={8}>
            <Table className="table table-bordered text-nowrap border-bottom mb-3">
              <thead>
                <tr className="table-dark">
                  <th className="text-center">Image</th>
                  <th className="text-center">Title</th>
                  <th className="text-center">SKU</th>
                  <th className="text-center">Inventory</th>
                  <th className="text-center">Price</th>
                  <th className="text-center">Weight</th>
                </tr>
              </thead>
              <tbody>
                {product.variants.map((variant: TProductVariant) => (
                  <tr className="table-primary" key={variant.id}>
                    <td
                      style={{
                        textAlign: "center",
                        padding: "10px",
                        width: "10px",
                        height: "10px",
                      }}>
                      <p className="avatar avatar-lg bg-dark-transparent my-auto">
                        {
                          variant.image &&
                          <img
                            src={variant.image.src || ""}
                            style={{
                              display: "block",
                              margin: "0 auto",
                              maxWidth: "100%",
                              height: "100%",
                              objectFit: "cover",
                            }}
                          />
                        }
                      </p>
                    </td>
                    <td className="text-center">
                      {variant.title}
                    </td>
                    <td className="text-center">
                      {variant.sku}
                    </td>
                    <td className="text-center">
                      {variant.inventoryQuantity}
                    </td>
                    <td className="text-center">
                      {variant.price}
                    </td>
                    <td className="text-center">
                      {variant.weight}
                      {" "}
                      {variant.weightUnit}
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
          </th>
        </tr>
      }

    </Fragment >
  );
};
export default ManagementVendorProductsList;
