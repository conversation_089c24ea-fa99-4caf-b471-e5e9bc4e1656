import { FC, Fragment, useEffect, useState } from "react";
import { Col, Row } from "react-bootstrap";
import VendorTotalEstimatedEarningsCard from "./vendor-total-estimated-earnings-card";
import { useLazyGetVendorStatsQuery } from "../../../../services/vendors";
import { getAllErrorMessages } from "../../../../utils/errors";
import VendorTotalOrdersCard from "./vendor-total-orders-card";
import VendorTotalProductsCard from "./vendor-total-products-card";
import VendorTotalSalesCard from "./vendor-total-sales-card";

interface VendorSummaryCardProps {
  vendorId: string;
  setIsLoading: (loading: boolean) => void;
  setErr: any;
}

const VendorSummaryCard: FC<VendorSummaryCardProps> = ({ vendorId, setIsLoading, setErr }) => {
  const [stats, setStats] = useState<TVendorStats>();

  const [getVendorStats] = useLazyGetVendorStatsQuery();

  const loadVendorStats = (vendorId: string) => {
    setIsLoading(true);
    getVendorStats(vendorId)
      .unwrap()
      .then(setStats)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  useEffect(() => {
    if (vendorId) {
      loadVendorStats(vendorId);
    }
  }, [vendorId]);

  return (<Fragment>
    <Row>
      <Col xl={12}>
        <div className="row row-cols-xxl-5 row-cols-xl-3 row-cols-md-2">
          <VendorTotalOrdersCard totalOrders={stats?.totalOrders ?? 0} showTrend={false} showViewMore={false} />
          <VendorTotalProductsCard totalProducts={stats?.totalProducts ?? 0} showTrend={false} showViewMore={false} />
          <VendorTotalSalesCard totalSales={stats?.totalSales ?? 0} showTrend={false} />
          <VendorTotalEstimatedEarningsCard totalEstimatedEarnings={stats?.totalEstimatedEarnings ?? 0} showTrend={false} showViewMore={false} />
        </div>
      </Col>
    </Row>
  </Fragment>
  );
}

export default VendorSummaryCard;