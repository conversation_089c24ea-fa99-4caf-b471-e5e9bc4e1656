import { FC } from "react";
import { Card } from "react-bootstrap";
import { Link } from "react-router-dom";

interface VendorTotalOrdersCardProps {
  totalOrders: number;
  showTrend: boolean;
  showViewMore: boolean;
}

const VendorTotalOrdersCard: FC<VendorTotalOrdersCardProps> = ({ totalOrders, showTrend, showViewMore }) => {
  return (
    <div
      className="col card-background flex-fill"
      key={Math.random()}
    >
      <div className="card custom-card">
        <Card.Body>
          <div className="d-flex">
            <div>
              <p className="fw-medium mb-1 text-muted">Total Orders</p>
              <h3 className="mb-0">{new Intl.NumberFormat('en-US').format(totalOrders)}</h3>
            </div>
            <div
              className={`avatar avatar-md br-4 bg-primary-transparent ms-auto`}
            >
              <i className="bi bi-cart-check fs-20"></i>
            </div>
          </div>
          <div className="d-flex mt-2">
            {
              (showTrend) &&
              (
                <span className={`badge bg-primary-transparent rounded-pill`}            >
                  +0.00%
                  <i className={`fe fe-arrow-success`}></i>
                </span>
              )
            }
            {
              (showViewMore) &&
              (
                <Link
                  to="/orders"
                  className="text-muted fs-11 ms-auto text-decoration-underline mt-auto"
                >
                  view more
                </Link>
              )
            }
            {
              (!showTrend && !showViewMore) && (<div>&nbsp;</div>)
            }
          </div>
        </Card.Body>
      </div>
    </div>
  );
}

export default VendorTotalOrdersCard;