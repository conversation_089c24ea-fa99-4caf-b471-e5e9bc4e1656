import { FC } from "react";
import { Card } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import formatUSD from "../../../../utils/currency-formatter";

interface VendorTotalPayoutsCardProps {
  totalPayoutAmount: number;
  showTrend: boolean;
  showViewMore: boolean;
}

const VendorTotalPayoutsCard: FC<VendorTotalPayoutsCardProps> = ({ totalPayoutAmount, showTrend, showViewMore }) => {
  return (
    <div
      className="col card-background flex-fill"
      key={Math.random()}
    >
      <div className="card custom-card">
        <Card.Body>
          <div className="d-flex">
            <div>
              <p className="fw-medium mb-1 text-muted">Total Payouts</p>
              <h3 className="mb-0">{formatUSD(totalPayoutAmount)}</h3>
            </div>
            <div
              className={`avatar avatar-md br-4 bg-primary-transparent ms-auto`}
            >
              <i className="bi bi-cash"></i>
            </div>
          </div>
          <div className="d-flex mt-2">
            {
              (showTrend) &&
              (
                <span className={`badge bg-success-transparent rounded-pill`}            >
                  +0.00%
                  <i className={`fe fe-arrow-up`}></i>
                </span>
              )
            }
            {
              (showViewMore) &&
              (
                <Link
                  to="/earnings"
                  className="text-muted fs-11 ms-auto text-decoration-underline mt-auto"
                >
                  view more
                </Link>
              )
            }
            {
              (!showTrend && !showViewMore) && (<div>&nbsp;</div>)
            }
          </div>
        </Card.Body>
      </div>
    </div>
  );
}

export default VendorTotalPayoutsCard;