import { FC } from "react";
import { Card } from "react-bootstrap";
import formatUSD from "../../../../utils/currency-formatter";

interface VendorTotalSalesCardProps {
  totalSales: number;
  showTrend: boolean;
}

const VendorTotalSalesCard: FC<VendorTotalSalesCardProps> = ({ totalSales, showTrend }) => {
  return (
    <div
      className="col card-background flex-fill"
      key={Math.random()}
    >
      <div className="card custom-card">
        <Card.Body>
          <div className="d-flex">
            <div>
              <p className="fw-medium mb-1 text-muted">Total Sales</p>
              <h3 className="mb-0">{formatUSD(totalSales)}</h3>
            </div>
            <div
              className={`avatar avatar-md br-4 bg-warning-transparent ms-auto`}
            >
              <i className="bi bi-currency-dollar fs-20"></i>
            </div>
          </div>
          <div className="d-flex mt-2">
            {
              (showTrend) ?
                (
                  <span className={`badge bg-success-transparent rounded-pill`}>
                    +06 %
                    <i className="fe fe-arrow-up"></i>
                  </span>
                ) :
                (
                  <div>&nbsp;</div>
                )
            }
          </div>
        </Card.Body>
      </div>
    </div>
  );
}

export default VendorTotalSalesCard;