import { FC, Fragment, useEffect, useState } from "react";
import { Al<PERSON>, Card, Table } from "react-bootstrap";
import { useLazyGetVendorEarningsOfVendorQuery } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import VendorEarningRow from "./vendor-earning-row";

interface VendorDetailEarningsCardProps {
  vendor: TVendor;
}

const VendorDetailEarningsCard: FC<VendorDetailEarningsCardProps> = ({ vendor }) => {
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [earnings, setEarnings] = useState<TVendorEarning[]>([]);

  const [getEarnings] = useLazyGetVendorEarningsOfVendorQuery();

  useEffect(() => {
    loadVendorsList(vendor.id, { page });
  }, [vendor, page]);

  const loadVendorsList = (vendorId: string, params: TQueryAPI) => {
    setIsLoading(true);
    getEarnings({ vendorId, params })
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setEarnings(res.data || []);
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Earnings</Card.Title>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>Created at</th>
                <th>Amount ($)</th>
                <th className="w-5" style={{ textAlign: "center" }}>Status</th>
                <th colSpan={3} style={{ textAlign: "center" }}>Actions</th>
              </tr>
            </thead>
            <tbody>
              {
                earnings.map((commission) => (
                  < VendorEarningRow
                    showVendorColumn={false}
                    inputEarning={commission}
                    setErr={setErr}
                  />
                ))
              }
            </tbody>
          </Table>

          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Body>
      </Card>
    </Fragment>
  );
}

export default VendorDetailEarningsCard;