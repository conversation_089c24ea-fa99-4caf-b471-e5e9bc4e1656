import {FC, Fragment, useCallback, useState} from "react";
import {<PERSON>, useNavigate, useParams} from "react-router-dom";
import { Card, Button, Alert, Row, Col } from "react-bootstrap";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { useEffect } from "react";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import VendorPayout from "./vendor-payout";
import VendorSummaryCard from "./summary/vendor-summary-card";
import {useLazyGetVendorByIdQuery, useUpdateVendorMutation} from "../../../services/vendors";
import { getAllErrorMessages } from "../../../utils/errors";
import VendorDetailEarningsCard from "./vendor-detail-earnings-card";
import Swal from "sweetalert2";

const VendorDetail: FC = () => {
  const { id: vendorId } = useParams<{ id?: string }>();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [vendor, setVendor] = useState<TVendor>();

  const navigate = useNavigate();
  const [getVendor] = useLazyGetVendorByIdQuery();
  const [updateVendor] = useUpdateVendorMutation();

  const loadVendor = (vendorId: string) => {
    setIsLoading(true);
    setErr({});
    getVendor(vendorId)
      .unwrap()
      .then((res) => {
        setVendor(res);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const handleApprove = useCallback(() => {
    if(vendor) {
      Swal.fire({
        title: "Are you sure?",
        html: `<p>Do you want to approve the registration of ${vendor.companyName}?</p>`,
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, approve!",
      })
          .then((result) => {
            if (result.isConfirmed) {
              setIsLoading(true);
              updateVendor({id: vendor.id, data: {registrationStatus: EApprovalStatus.APPROVED}})
                  .unwrap()
                  .then(() => {
                    Swal.fire("Approved!", `The registration of ${vendor.companyName} has been approved.`, "success");
                    loadVendor(vendor.id);
                  })
                  .catch((error) => {
                    const errorMessages = getAllErrorMessages(error);
                    console.error("Error approving vendor:", errorMessages);
                    Swal.fire("Error!", errorMessages.messages[0], "error");
                  })
                  .finally(() => {
                    setIsLoading(false);
                  });
            }
          })
          .catch((error) => {
            const errorMessages = getAllErrorMessages(error);
            console.error("Error during vendor approval:", errorMessages);
            Swal.fire("Error!", errorMessages.messages[0], "error");
          });
    }
  }, [vendor])

  useEffect(() => {
    if (vendorId) {
      loadVendor(vendorId);
    }
  }, []);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title="Vendor Details"
            route=""
          />
          <div className="px-4 justify-content-end">
            {
                vendor && vendor?.registrationStatus !== EApprovalStatus.APPROVED &&
                <Button
                    hidden={!hasPermission(ACTION.UPDATE, RESOURCE.VENDOR)}
                    variant="btn btn-success ms-2 btn btn-success"
                    onClick={() => handleApprove()}
                >
                  Approve <i className="bi bi-check-lg ms-2"></i>
                </Button>
            }

            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.VENDOR)}
              variant="primary-light m-2"
              onClick={() => navigate('edit')}
            >
              Edit <i className="bi bi-pencil ms-2"></i>
            </Button>
          </div>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Row>
            <Col xl={4}>
              <p>
                <strong>Company Name:</strong> {vendor?.companyName}
              </p>
              <p>
                <strong>Brand Name:</strong> {vendor?.brandName}
              </p>
              <p>
                <strong>Website:</strong> {vendor?.website}
              </p>
              <p>
                <strong>EIN:</strong> {vendor?.ein}
              </p>
            </Col>
            <Col xl={4}>
              <p>
                <strong>Contact Name:</strong> {vendor?.contactName}
              </p>
              <p>
                <strong>Phone:</strong> {vendor?.phone}
              </p>
              <p>
                <strong>Email:</strong> {vendor?.email}
              </p>
              <p>
                <strong>Warehouse:</strong> {vendor?.warehouse?.name}
              </p>
            </Col>
            <Col xl={4}>
              {vendor?.address1 && (
                <p>
                  <strong>Address:</strong>
                  {`${vendor?.address1} ${vendor?.address2}, ${vendor?.city}, ${vendor?.state} ${vendor?.zipCode}, ${vendor?.country}`}
                </p>
              )}
              <p>
                <strong>Registration Status:</strong> {
                  vendor?.registrationStatus === EApprovalStatus.PENDING ? (
                    <span className="badge bg-warning-transparent">Pending</span>
                  ) : vendor?.registrationStatus === EApprovalStatus.APPROVED ? (
                    <span className="badge bg-success text-white">Approved</span>
                  ) : (
                    <span className="badge bg-danger text-white">Rejected</span>
                  )
                }
              </p>
              {
                vendor?.registrationStatus === EApprovalStatus.REJECTED &&
                <p>
                  <strong>Rejection Reason:</strong> {vendor?.rejectionReason}
                </p>
              }
              <p>
                <strong>Register At:</strong> {vendor?.createdAt ? new Date(vendor?.createdAt).toLocaleString() : ""}
              </p>
              <p>
                <strong>Last Update:</strong> {vendor?.updatedAt ? new Date(vendor?.updatedAt).toLocaleString() : ""}
              </p>
              <p>
                <strong>Documents:</strong>
                {
                  vendor && vendor?.businessLicenseDocuments.map((file: any) => (
                    <Link className={'btn btn-info btn-sm'} key={file.id} to={file.url} target={'_blank'}> View </Link>
                    ))
                }
              </p>
            </Col>
          </Row>

        </Card.Body>
      </Card>

      <VendorSummaryCard
        vendorId={vendor?.id ?? ''}
        setIsLoading={setIsLoading}
        setErr={setErr}
      />

      {vendor && <VendorDetailEarningsCard vendor={vendor} />}

      {vendor && <VendorPayout vendor={vendor} />}
    </Fragment>
  );
};

export default VendorDetail;