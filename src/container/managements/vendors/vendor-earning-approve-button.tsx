import { But<PERSON> } from "react-bootstrap";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import Swal from "sweetalert2";
import { getAllErrorMessages } from "../../../utils/errors";
import { useUpdateVendorEarningStatusMutation } from "../../../services/vendors";

export default function VendorEarningApproveButton({ earning, setIsLoading, onFinished }) {
  if (!earning) return null;

  const [updateVendorEarningStatus] = useUpdateVendorEarningStatusMutation();

  const handleApprovingEarning = () => {
    if (!earning) return;

    Swal.fire({
      title: "Are you sure?",
      html: `<p>Approve earning amount $${earning.finalAmount}?</p><p>An email will be sent to this vendor</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, approve!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          updateVendorEarningStatus({ id: earning.id, status: EApprovalStatus.APPROVED })
            .unwrap()
            .then((updatedEarning) => {
              Swal.fire("Approved!",
                `Earning amount $${earning.finalAmount} has been approved.`,
                "success"
              );
              onFinished({ success: true, updatedEarning });
            })
            .catch((error) => {
              Swal.fire("Error!", error.message.body ?? "Something went wrong!", "error");
              onFinished({ success: false });
            })
            .finally(() => {
              setIsLoading(false);
              onFinished({ success: false });
            })
        }
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during earning approval confirmation:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinished({ success: false });
      });
  }

  return (
    <Button
      hidden={!hasPermission(ACTION.APPROVE, RESOURCE.VENDOR) || earning.status == EApprovalStatus.APPROVED || parseFloat(earning.finalAmount ?? '0') === 0}
      variant="success"
      className="btn btn-success btn-sm ms-2"
      onClick={handleApprovingEarning}
    >
      Approve
    </Button>
  );
}