import { But<PERSON> } from "react-bootstrap";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import Swal from "sweetalert2";
import { getAllErrorMessages } from "../../../utils/errors";
import { Fragment, useState } from "react";
import CommissionRejectionDialog from "../../../components/dialog/commission-rejection-dialog";
import { useUpdateVendorEarningStatusMutation } from "../../../services/vendors";

export default function VendorEarningRejectButton({ earning, setIsLoading, onFinished }) {
  if (!earning) return null;

  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [updateEarningStatus] = useUpdateVendorEarningStatusMutation();

  const handleRejectingEarning = (rejectionReason: string) => {
    if (!earning) return;
    setIsLoading(true);

    updateEarningStatus({ id: earning.id, status: EApprovalStatus.REJECTED, rejectionReason })
      .unwrap()
      .then((updatedEarning) => {
        onFinished({ success: true, updatedEarning });
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinished({ success: false });
      })
      .finally(() => {
        setIsLoading(false);
      })
  }

  return (
    <Fragment>
      <Button
        hidden={!hasPermission(ACTION.REJECT, RESOURCE.AFFILIATION) || earning.status == EApprovalStatus.REJECTED || parseFloat(earning.finalAmount ?? '0') === 0}
        variant="danger"
        className="btn btn-danger btn-sm ms-2"
        onClick={() => { setShowConfirmModal(true) }}
      >
        Reject
      </Button>

      <CommissionRejectionDialog
        show={showConfirmModal}
        setShow={setShowConfirmModal}
        handleConfirm={handleRejectingEarning}
      />
    </Fragment>
  );
}