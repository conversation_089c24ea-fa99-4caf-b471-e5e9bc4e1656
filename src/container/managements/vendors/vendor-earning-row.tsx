import { FC, Fragment, useEffect } from "react";
import { But<PERSON> } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { hasPermission } from "../../../utils/authorization";
import { useState } from "react";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import { format } from "date-fns";
import VendorEarningApproveButton from "./vendor-earning-approve-button";
import VendorEarningRejectButton from "./vendor-earning-reject-button";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";

interface VendorEarningRowProps {
  showVendorColumn: boolean;
  inputEarning: TVendorEarning;
  setErr: any
}

const VendorEarningRow: FC<VendorEarningRowProps> = ({ showVendorColumn, inputEarning, setErr }) => {
  const [earning, setEarning] = useState(inputEarning);
  const [isLoading, setIsLoading] = useState(false);

  const navigate = useNavigate();

  useEffect(() => {
    setEarning(inputEarning);
  }, [inputEarning]);

  const onFinishedApproveEarning = ({ success, updatedEarning }: { success: boolean; updatedEarning: TVendorEarning; }) => {
    if (success) {
      setEarning(updatedEarning);
      setErr({});
    }
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <tr>
        {showVendorColumn && <td>{earning.vendor?.companyName}</td>}
        <td>{format(earning.createdAt, 'EEE, MMM dd, yyyy, hh:mm aa')}</td>
        <td>{earning.finalAmount}</td>

        <td style={{ textAlign: "center" }}>
          {
            earning.status == EApprovalStatus.PENDING ? (
              <span className="badge bg-warning-transparent">Pending</span>
            ) : earning.status == EApprovalStatus.APPROVED ? (
              <span className="badge bg-success-transparent">Approve</span>
            ) : (
              <span className="badge bg-danger-transparent">Rejected</span>
            )
          }
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <Button
            hidden={!hasPermission(ACTION.READ, RESOURCE.AFFILIATION)}
            variant="primary-light"
            className="btn btn-primary-light btn-sm ms-2"
            onClick={() => navigate(`/managements-vendor-earnings/${earning.id}`)}
          >
            View
          </Button>
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <VendorEarningApproveButton
            earning={earning}
            setIsLoading={setIsLoading}
            onFinished={onFinishedApproveEarning}
          />
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          <VendorEarningRejectButton
            earning={earning}
            setIsLoading={setIsLoading}
            onFinished={onFinishedApproveEarning}
          />
        </td>
      </tr>
    </Fragment >
  );
}

export default VendorEarningRow;