import { FC, Fragment, useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Card, Button, Form, Alert, Col, Row } from "react-bootstrap";
import { useLazyGetVendorByIdQuery, useLazyGetVendorWarehousesSelectionsQuery, useUpdateVendorMutation, useUpdateVendorWarehouseMutation } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import CardHeaderWithBack from "../../../components/table-title/card-header-with-back";
import { getAllErrorMessages } from "../../../utils/errors";
import VendorUsers from "./vendor-users-setting";
import Swal from "sweetalert2";

const VendorEdit: FC = () => {
  const { id: vendorId } = useParams<{ id: string }>();

  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [vendor, setVendor] = useState<TVendor>();

  const navigate = useNavigate();
  const [getVendor] = useLazyGetVendorByIdQuery();
  const [updateVendor] = useUpdateVendorMutation();

  const [form, setForm] = useState({
    companyName: "",
    brandName: "",
    website: "",
    contactName: "",
    phone: "",
    email: "",
    address1: "",
    address2: "",
    city: "",
    state: "",
    country: "",
    zipCode: "",
    ein: "",
    commissionRate: 0,
    fixedCommissionAmount: 0,
  });

  const loadVendor = (vendorId: string) => {
    setIsLoading(true);
    setErr({});
    getVendor(vendorId)
      .unwrap()
      .then(setVendor)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!vendorId) return;

    const data = {
      ...form,
      commissionRate: form.commissionRate / 100
    }

    setIsLoading(true);
    setErr({});
    updateVendor({ id: vendorId, data })
      .unwrap()
      .then(() => {
        navigate(-1);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const [vendorWarehouseSelected, setVendorWarehouseSelected] = useState<any | null>(null)
  const [vendorWarehousesSelection, setVendorWarehousesSelection] = useState([])
  useEffect(() => {
    if (vendorId) {
      loadVendor(vendorId);

      selectWarehouses({})
        .unwrap()
        .then((res) => { setVendorWarehousesSelection(res) })
        .catch((error) => { console.log(error); })
    }
  }, []);

  useEffect(() => {
    if (vendor && vendorWarehousesSelection.length > 0) {
      setVendorWarehouseSelected(vendorWarehousesSelection.find((warehouse: any) => warehouse.id == vendor?.warehouseId) || null)
    }
  }, [vendor, vendorWarehousesSelection])

  useEffect(() => {
    if (vendor) {
      setForm({
        companyName: vendor.companyName || "",
        brandName: vendor.brandName || "",
        website: vendor.website || "",
        contactName: vendor.contactName || "",
        phone: vendor.phone || "",
        email: vendor.email || "",
        address1: vendor.address1 || "",
        address2: vendor.address2 || "",
        city: vendor.city || "",
        state: vendor.state || "",
        country: vendor.country || "",
        zipCode: vendor.zipCode || "",
        ein: vendor.ein || "",
        commissionRate: vendor.commissionRate * 100 || 0,
        fixedCommissionAmount: vendor.fixedCommissionAmount || 0,
      });
    }
  }, [vendor]);

  const [selectWarehouses] = useLazyGetVendorWarehousesSelectionsQuery()
  const [updateWarehouse] = useUpdateVendorWarehouseMutation()
  const handleWarehouseChange = (warehouse) => {
    Swal.fire({
      icon: 'question',
      title: `Update Warehouse to ${warehouse.name} ?`,
      text: 'This will change inventories for all products of this vendor!',
    })
      .then((result) => {
        if (result.isConfirmed) {
          setVendorWarehouseSelected(warehouse)
          setIsLoading(true)
          updateWarehouse({
            vendorId,
            warehouseId: warehouse.id
          })
            .unwrap()
            .then((res) => { Swal.fire(res, '', 'success') })
            .catch((error) => { Swal.fire('Error!', getAllErrorMessages(error).messages[0], 'error') })
            .finally(() => setIsLoading(false))
        }
      })
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack title="Edit Vendor" route="" />
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Form onSubmit={handleSubmit}>
            <Fragment>
              <Row>
                <Col xl={6} md={6} sm={12}>
                  <Form.Group className="mb-3">
                    <Form.Label>Company Name</Form.Label>
                    <Form.Control
                      name="companyName"
                      value={form.companyName}
                      onChange={handleChange}
                      required
                    />
                  </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                  <Form.Group className="mb-3">
                    <Form.Label>Brand Name</Form.Label>
                    <Form.Control
                      name="brandName"
                      value={form.brandName}
                      onChange={handleChange}
                    />
                  </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Website</Form.Label>
                  <Form.Control
                    name="website"
                    value={form.website}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Name</Form.Label>
                  <Form.Control
                    name="contactName"
                    value={form.contactName}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Phone</Form.Label>
                  <Form.Control
                    name="phone"
                    value={form.phone}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    name="email"
                    value={form.email}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Address 1</Form.Label>
                  <Form.Control
                    name="address1"
                    value={form.address1}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Address 2</Form.Label>
                  <Form.Control
                    name="address2"
                    value={form.address2}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>City</Form.Label>
                  <Form.Control
                    name="city"
                    value={form.city}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                  <Row>
                    <Col xl={6} md={6} sm={12}>

                    <Form.Group className="mb-3">
                      <Form.Label>State</Form.Label>
                      <Form.Control
                        name="city"
                        value={form.state}
                        onChange={handleChange}
                      />
                    </Form.Group>
                  </Col>
                  <Col xl={6} md={6} sm={12}>
                    <Form.Group className="mb-3">
                      <Form.Label>Country</Form.Label>
                      <Form.Control
                        name="country"
                        value={form.country}
                        onChange={handleChange}
                      />
                    </Form.Group>
                  </Col>
                  </Row>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Zip Code</Form.Label>
                  <Form.Control
                    name="country"
                    value={form.zipCode}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>EIN</Form.Label>
                  <Form.Control
                    name="ein"
                    value={form.ein}
                    onChange={handleChange}
                  />
                </Form.Group>
                </Col>
                <Col xl={6} md={6} sm={12}>
                  <Row>
                    <Col xl={6} md={6} sm={12}>
                      <Form.Group className="mb-3">
                        <Form.Label>Commission Rate (%)</Form.Label>
                        <Form.Control
                            name="commissionRate"
                            value={form.commissionRate}
                            onChange={handleChange}
                        />
                      </Form.Group>
                    </Col>
                    <Col xl={6} md={6} sm={12}>
                      <Form.Group className="mb-3">
                        <Form.Label>Fixed Commission Amount ($)</Form.Label>
                        <Form.Control
                            name="fixedCommissionAmount"
                            value={form.fixedCommissionAmount}
                            onChange={handleChange}
                        />
                      </Form.Group>
                    </Col>
                  </Row>
                </Col>
                <Col xl={6} md={6} sm={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Warehouse</Form.Label>
                  <Form.Select
                    // @ts-ignore
                    value={vendorWarehouseSelected?.id || ''}
                    onChange={(e) =>
                      handleWarehouseChange(
                        vendorWarehousesSelection
                          .find((value: any) => value.id == e.target.value)
                      )
                    }
                  >
                    <option value=''></option>
                    {
                      vendorWarehousesSelection.map((warehouse: any, index) => (
                        <option
                          key={index}
                          value={warehouse.id}
                        >
                          {warehouse.name}
                        </option>
                      ))
                    }
                  </Form.Select>
                </Form.Group>
                </Col>
                <Col>
                  <div className="text-end mt-4">
                    <Button variant="secondary" onClick={() => navigate(-1)}>
                      Cancel
                    </Button>
                    <Button type="submit" className="ms-2" variant="primary" disabled={isLoading}>
                      {isLoading ? "Saving..." : "Save"}
                    </Button>
                  </div>
                </Col>
              </Row>
            </Fragment>
          </Form>
        </Card.Body>
      </Card>
      {
        vendor && <VendorUsers vendor={vendor} />
      }
    </Fragment>
  );
};

export default VendorEdit;