import { FC, Fragment, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Table, OverlayTrigger, Tooltip } from "react-bootstrap";
import { useLazyGetVendorPaymentsQuery, useLazyDeletePayoutByIdQuery } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import { format } from "date-fns";
import { hasPermission } from "../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import VendorPaymentDialog from "../../../components/dialog/vendor-payment-dialog";
import formatUSD from "../../../utils/currency-formatter";
import Swal from "sweetalert2";

interface VendorPayoutProps {
  vendor: TVendor;
}

const VendorPayout: FC<VendorPayoutProps> = ({ vendor }) => {
  const [payments, setPayments] = useState<TVendorPayment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [showPaymentModal, setShowPaymentModal] = useState<boolean>(false);

  const [getVendorPayments] = useLazyGetVendorPaymentsQuery();
  const [deleteVendorPayments] = useLazyDeletePayoutByIdQuery();

  const loadVendorPayments = (vendor: TVendor) => {
    setIsLoading(true);
    setErr({});
    getVendorPayments({ vendorId: vendor.id, params: {} })
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setPayments(res.data || []);
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const handlePaymentModalClose = (success) => {
    setShowPaymentModal(false);
    if (success) {
      loadVendorPayments(vendor);
    }
  }

  const handleDeleteClick = (pm) => {
    Swal.fire({
      title: "Are you sure?",
      html: `<p>Delete this payout with amount $${pm.amount}?</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete!",
    })
    .then((result) => {
      if (result.isConfirmed) {
        setIsLoading(true);
        deleteVendorPayments(pm.id)
          .unwrap()
          .then(() => {
            Swal.fire("Delete!",
                `Payout $${pm.amount} has been deleted.`,
                "success"
            );
            setPayments(payments.filter(pay => pay.id !== pm.id));
          })
          .catch((error) => {
            Swal.fire("Error!", error.message.body ?? "Something went wrong!", "error");
          })
          .finally(() => {
            setIsLoading(false);
          })
      }
    })
    .catch((error) => {
      const errorMessages = getAllErrorMessages(error);
      console.error("Error during delete:", errorMessages);
      Swal.fire("Error!", errorMessages.messages[0], "error");
    });
  }

  useEffect(() => {
    loadVendorPayments(vendor);
  }, []);

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Payouts</Card.Title>
          <div className="px-4 justify-content-end">
            <Button
              hidden={!hasPermission(ACTION.UPDATE, RESOURCE.VENDOR)}
              variant="primary-light m-2"
              onClick={() => { setShowPaymentModal(true) }}
            >
              Pay <i className="bi bi-currency-dollar" />
            </Button>
          </div>
        </Card.Header>

        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
              <tr>
                <th>Date Time</th>
                <th>Payment Method</th>
                <th>Amount ($)</th>
                <th>Note</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {
                payments.map(pm => (
                  <tr key={pm.id}>
                    <td>{format(pm.createdAt, 'EEE, MMM dd, yyyy, hh:mm aa')}</td>
                    <td>{pm.paymentMethod?.paymentTypeName}</td>
                    <td>{formatUSD(pm.amount)}</td>
                    <td>{pm.note}</td>
                    <td>
                      <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
                        <Button
                            variant="primary-light"
                            className="btn btn-danger-light btn-sm ms-2"
                            onClick={() => handleDeleteClick(pm)}
                        >
                          <span className="ri-delete-bin-7-line fs-14"></span>
                        </Button>
                      </OverlayTrigger>
                    </td>
                  </tr>
                ))
              }
            </tbody>
          </Table>
        </Card.Body>

        <Card.Footer>
          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Footer>
      </Card>

      <VendorPaymentDialog
        vendor={vendor}
        show={showPaymentModal}
        setIsLoading={setIsLoading}
        handleClose={handlePaymentModalClose} />
    </Fragment>
  );
}

export default VendorPayout;