import { FC, Fragment } from "react";
import { <PERSON>ge, Button, OverlayTrigger, Tooltip } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { ACTION, RESOURCE } from "../../../utils/constant/authorization";
import { hasPermission, hasSuperAdminPermission } from "../../../utils/authorization";
import { useState } from "react";
import Swal from "sweetalert2";
import { getAllErrorMessages } from "../../../utils/errors";
import { useDeleteVendorMutation, useUpdateVendorMutation } from "../../../services/vendors";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import VendorRejectionDialog from "../../../components/dialog/vendor-rejection-dialog";
import { EVendorsListLocation } from "../../../components/enums/vendors-list-location";

interface VendorRowProps {
  vendor: TVendor;
  commissionSettings?: TVendorCommissionSetting;
  listLocation?: EVendorsListLocation;
  setIsLoading: (loading: boolean) => void;
  onFinishedAction: (result: { success: boolean }) => void;
}

const VendorRow: FC<VendorRowProps> = ({ vendor, commissionSettings, listLocation = EVendorsListLocation.MAIN, setIsLoading, onFinishedAction }) => {
  const navigate = useNavigate();
  const [approving, setApproving] = useState(false);
  const [reopening, setReopening] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [resetting, setResetting] = useState(false);
  const [showRejectionDialog, setShowRejectionDialog] = useState(false);

  const [updateVendor] = useUpdateVendorMutation();
  const [deleteVendor] = useDeleteVendorMutation();

  const handleApprove = async () => {
    setApproving(true);
    Swal.fire({
      title: "Are you sure?",
      html: `<p>Do you want to approve the registration of ${vendor.companyName}?</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, approve!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          updateVendor({ id: vendor.id, data: { registrationStatus: EApprovalStatus.APPROVED } })
            .unwrap()
            .then(() => {
              Swal.fire("Approved!", `The registration of ${vendor.companyName} has been approved.`, "success");
              onFinishedAction({ success: true });
            })
            .catch((error) => {
              const errorMessages = getAllErrorMessages(error);
              console.error("Error approving vendor:", errorMessages);
              Swal.fire("Error!", errorMessages.messages[0], "error");
              onFinishedAction({ success: false });
            })
            .finally(() => {
              setIsLoading(false);
            });
        }
        setApproving(false);
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during vendor approval:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinishedAction({ success: false });
      });
  }

  const handleReject = async (rejectionReason: string) => {
    setIsLoading(true);
    updateVendor({ id: vendor.id, data: { registrationStatus: EApprovalStatus.REJECTED, rejectionReason: rejectionReason } })
      .unwrap()
      .then(() => {
        Swal.fire("Rejected!", `The registration of ${vendor.companyName} has been rejected.`, "success");
        onFinishedAction({ success: true });
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error rejecting vendor:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinishedAction({ success: false });
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const handleReopen = async () => {
    setReopening(true);
    Swal.fire({
      title: "Are you sure?",
      html: `<p>Do you want to reopen the registration of ${vendor.companyName}?</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, reopen!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          updateVendor({ id: vendor.id, data: { registrationStatus: EApprovalStatus.PENDING } })
            .unwrap()
            .then(() => {
              Swal.fire("Reopened!", `The registration of ${vendor.companyName} has been reopened.`, "success");
              onFinishedAction({ success: true });
            })
            .catch((error) => {
              const errorMessages = getAllErrorMessages(error);
              console.error("Error approving vendor:", errorMessages);
              Swal.fire("Error!", errorMessages.messages[0], "error");
              onFinishedAction({ success: false });
            })
            .finally(() => {
              setIsLoading(false);
            });
        }
        setReopening(false);
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during vendor reopen:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinishedAction({ success: false });
      });
  }

  const handleDelete = async () => {
    setDeleting(true);
    Swal.fire({
      title: "Are you sure?",
      html: `<p>Do you want to delete this vendor?</p><p>All data related to this vendor will be deleted.</p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, approve!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          deleteVendor(vendor.id)
            .unwrap()
            .then(() => {
              Swal.fire("Deleted!", "Vendor has been deleted.", "success");
              onFinishedAction({ success: true });
            })
            .catch((error) => {
              const errorMessages = getAllErrorMessages(error);
              console.error("Error deleting vendor:", errorMessages);
              Swal.fire("Error!", errorMessages.messages[0], "error");
              onFinishedAction({ success: false });
            })
            .finally(() => {
              setIsLoading(false);
            });
        }
        setDeleting(false);
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during commission approval confirmation:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinishedAction({ success: false });
      });
  };

  const handleResetCommissionSettings = async () => {
    if (!commissionSettings) {
      console.error("Commission settings are not provided.");
      return;
    }

    setResetting(true);
    Swal.fire({
      title: "Are you sure?",
      html: `<p>Do you want to reset commission settings of ${vendor.companyName} to default?</p>
        <p>Commission Rate: <b>${commissionSettings.defaultCommissionRate * 100}%</b></p>
        <p>Fixed Commission Amount: <b>$${commissionSettings.defaultFixedCommissionAmount}%</b></p>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, reset!",
    })
      .then((result) => {
        if (result.isConfirmed) {
          setIsLoading(true);
          updateVendor({ id: vendor.id, data: { commissionRate: commissionSettings.defaultCommissionRate, fixedCommissionAmount: commissionSettings.defaultFixedCommissionAmount } })
            .unwrap()
            .then(() => {
              Swal.fire("Reset!", `The commission settings of ${vendor.companyName} has been reset to default.`, "success");
              onFinishedAction({ success: true });
            })
            .catch((error) => {
              const errorMessages = getAllErrorMessages(error);
              console.error("Error reset commission settings of vendor:", errorMessages);
              Swal.fire("Error!", errorMessages.messages[0], "error");
              onFinishedAction({ success: false });
            })
            .finally(() => {
              setIsLoading(false);
            });
        }
        setResetting(false);
      })
      .catch((error) => {
        const errorMessages = getAllErrorMessages(error);
        console.error("Error during resetting commission settings:", errorMessages);
        Swal.fire("Error!", errorMessages.messages[0], "error");
        onFinishedAction({ success: false });
      });
  }

  const getFirstButton = () => {
    if (listLocation === EVendorsListLocation.MAIN || listLocation === EVendorsListLocation.SETTINGS) {
      return (
        <Button
          variant="primary-light"
          className="btn btn-primary-light btn-sm ms-2"
          hidden={!hasPermission(ACTION.READ, RESOURCE.VENDOR)}
          onClick={() => navigate(`/managements-vendors/${vendor.id}`)}
        >
          <span className="ri-eye-line fs-14"></span>
        </Button>
      );
    } else {
      return (
        <Button
          variant="primary-light"
          className="btn btn-primary-light btn-sm ms-2"
          hidden={!hasPermission(ACTION.READ, RESOURCE.VENDOR)}
          onClick={() => navigate(`/managements-vendors/${vendor.id}`)}
        >
          View
        </Button>
      );
    }
  }

  const getSecondButton = () => {
    if (listLocation === EVendorsListLocation.MAIN || listLocation === EVendorsListLocation.SETTINGS) {
      return (
        <Button
          variant="primary-light"
          className="btn btn-warning-light btn-sm ms-2"
          hidden={!hasPermission(ACTION.UPDATE, RESOURCE.VENDOR)}
          onClick={() => navigate(`/managements-vendors/${vendor.id}/edit`)}
        >
          <span className="ri-edit-line fs-14"></span>
        </Button>
      );
    } else if (listLocation === EVendorsListLocation.PENDING) {
      return (
        <Button
          variant="success"
          className="btn btn-success btn-sm ms-2"
          hidden={!hasPermission(ACTION.APPROVE, RESOURCE.VENDOR)}
          disabled={approving}
          onClick={handleApprove}
        >
          Approve
        </Button>
      );
    } else {
      return (
        <Button
          variant="primary-light"
          className="btn btn-warning-light btn-sm ms-2"
          hidden={!hasPermission(ACTION.APPROVE, RESOURCE.VENDOR)}
          disabled={reopening}
          onClick={handleReopen}
        >
          Reopen
        </Button>
      );
    }
  }

  const getThirdButton = () => {
    if (listLocation === EVendorsListLocation.MAIN) {
      return (
        <Button
          hidden={!hasSuperAdminPermission()}
          variant="primary-light"
          className="btn btn-danger-light btn-sm ms-2"
          onClick={handleDelete}
          disabled={deleting}
        >
          <span className="ri-delete-bin-7-line fs-14"></span>
        </Button>
      );
    } else if (vendor.registrationStatus === EApprovalStatus.PENDING) {
      return (
        <Button
          variant="danger"
          className="btn btn-danger btn-sm ms-2"
          hidden={!hasPermission(ACTION.APPROVE, RESOURCE.VENDOR)}
          onClick={() => { setShowRejectionDialog(true); }}
        >
          Reject
        </Button>
      );
    } else if (listLocation === EVendorsListLocation.SETTINGS) {
      return (
        <OverlayTrigger overlay={<Tooltip>Reset commission settings<br />to default</Tooltip>}>
          <Button
            hidden={!hasPermission(ACTION.UPDATE, RESOURCE.VENDOR)}
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-2"
            onClick={handleResetCommissionSettings}
            disabled={resetting || !commissionSettings}
          >
            <span className="bi-arrow-counterclockwise fs-14"></span>
          </Button>
        </OverlayTrigger >
      );
    } else {
      return null;
    }
  }

  return (
    <Fragment>
      <tr>
        <td><Link to={`/managements-vendors/${vendor.id}`}>{vendor.companyName}</Link></td>
        <td>{vendor.brandName}</td>
        <td>
          {vendor.website ? (
            <a
              href={vendor.website}
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: "#2563eb", textDecoration: "underline" }}
            >
              Link
            </a>
          ) : (
            ''
          )}
        </td>
        <td>{vendor.contactName}</td>
        <td>{vendor.ein}</td>
        {
          (listLocation === EVendorsListLocation.MAIN) && <td>{`${vendor.commissionRate * 100}%`}</td>
        }
        {
          (listLocation === EVendorsListLocation.SETTINGS) &&
          <td>
            {
              (vendor.commissionRate === commissionSettings?.defaultCommissionRate) ?
                `${vendor.commissionRate * 100}%` :
                <Badge bg="secondary-transparent">{`${vendor.commissionRate * 100}%`}</Badge>
            }
          </td>
        }
        {
          (listLocation === EVendorsListLocation.MAIN) && <td>{`$${vendor.fixedCommissionAmount}`}</td>
        }
        {
          (listLocation === EVendorsListLocation.SETTINGS) &&
          <td>
            {
              (vendor.fixedCommissionAmount === commissionSettings?.defaultFixedCommissionAmount) ?
                `$${vendor.fixedCommissionAmount}` :
                <Badge bg="secondary-transparent">{`$${vendor.fixedCommissionAmount}`}</Badge>
            }
          </td>
        }
        <td style={{ textAlign: "center" }}>
          {
            vendor.registrationStatus === EApprovalStatus.PENDING ? (
              <span className="badge bg-warning-transparent">Pending</span>
            ) : vendor.registrationStatus === EApprovalStatus.APPROVED ? (
              <span className="badge bg-success text-white">Approved</span>
            ) : (
              <span className="badge bg-danger text-white">Rejected</span>
            )
          }
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          {getFirstButton()}
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          {getSecondButton()}
        </td>
        <td className="w-5" style={{ textAlign: "center" }}>
          {getThirdButton()}
        </td>
      </tr>

      <VendorRejectionDialog
        show={showRejectionDialog}
        setShow={setShowRejectionDialog}
        handleConfirm={handleReject}
      />
    </Fragment>
  );
}

export default VendorRow;