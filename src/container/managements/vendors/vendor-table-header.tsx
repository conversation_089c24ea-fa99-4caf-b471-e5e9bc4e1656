import { EVendorsListLocation } from "../../../components/enums/vendors-list-location";

interface VendorTableHeaderProps {
  listLocation?: EVendorsListLocation;
}

export default function VendorTableHeader({ listLocation = EVendorsListLocation.MAIN }: VendorTableHeaderProps) {
  return (
    <thead>
      <tr>
        <th>Company Name</th>
        <th>Brand Name</th>
        <th>Website</th>
        <th>Contact Name</th>
        <th>EIN</th>
        {(listLocation === EVendorsListLocation.MAIN || listLocation === EVendorsListLocation.SETTINGS) && <th>Commission Rate</th>}
        {(listLocation === EVendorsListLocation.MAIN || listLocation === EVendorsListLocation.SETTINGS) && <th>Fixed Commission Amount</th>}
        <th className="w-5" style={{ textAlign: "center" }}>Status</th>
        <th colSpan={3} style={{ textAlign: "center" }}>Actions</th>
      </tr>
    </thead>
  );
}