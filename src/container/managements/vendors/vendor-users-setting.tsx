import { FC, Fragment, useEffect, useState, useCallback } from "react";
import { useNavigate, Link } from "react-router-dom";
import { Card, Button, Form, Alert, Table, InputGroup, OverlayTrigger, Tooltip } from "react-bootstrap";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import LazySelect from "../../../components/lazy-select/lazy-select";
import { useLazySelectUserQuery, useUpdateUserMutation } from "../../../services/user";
import Swal from "sweetalert2";

interface VendorProps {
  vendor: TVendor
}
const VendorUsers: FC<VendorProps> = ({vendor}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [users, setUsers] = useState<TUser[]>([]);
  const [selectedUser, setSelectedUser] = useState<TUser| null>(null);
  const [isClear, setIsClear] = useState(false);

  const navigate = useNavigate();
  const [selectUser] = useLazySelectUserQuery()
  const [updateUser] = useUpdateUserMutation()

  useEffect(() => {
    if (vendor) {
      setUsers(vendor.users)
    } else {
      setUsers([])
    }
  }, [vendor]);

  const assignUserToVendor = useCallback(async () => {
    if(selectedUser) {
      if(!selectedUser.vendorId) {
        //Update user
        setIsLoading(true)
        updateUser({...selectedUser, vendorId: vendor.id})
            .unwrap()
            .then(() => {
              setUsers([...users, selectedUser])
            })
            .catch((error) => {
              setErr(getAllErrorMessages(error));
            }).finally(() => setIsLoading(false))
      } else {
        await Swal.fire({
          title: "Error!",
          text: selectedUser.fullname + " is belong to another vendor!",
          icon: "error"
        });
      }
    }
  }, [selectedUser])

  const handleDeleteClick = (user: TUser) => {
    Swal.fire({
      title: "Are you sure?",
      text: `Do you want to delete ${user.email} out of this vendor?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    })
        .then((result) => {
          if (result.isConfirmed) {
            setIsLoading(true)

            updateUser({...user, vendorId: "-"})
                .unwrap()
                .then(() => {
                  setUsers(users.filter(u => u.id !== user.id))
                })
                .catch((error) => {
                  setErr(getAllErrorMessages(error));
                }).finally(() => setIsLoading(false))
          }
        })
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <Card.Title className="">Manage Users</Card.Title>
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}
          <div className={"mb-4"}>
            <Form.Label>Assign a user to vendor</Form.Label>
            <InputGroup>
              <div style={{ width: '-webkit-fill-available' }}>
                <LazySelect
                    isClearable
                    selectionFunction={selectUser}
                    label={value => value.fullname + ` (${value.email})`}
                    clearSelected={[isClear, setIsClear]}
                    getSelectedOptions={setSelectedUser}
                />
              </div>
              <Button onClick={assignUserToVendor}>
                Add
              </Button>
            </InputGroup>
          </div>
          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <thead>
            <tr>
              <th>Avatar</th>
              <th>email</th>
              <th>User Name</th>
              <th>phone</th>
              <th className="w-5" style={{ textAlign: "center" }}>Status</th>
              <th colSpan={3} style={{ textAlign: "center" }}>Actions</th>
            </tr>
            </thead>
            <tbody>
            {
              users && users.map(user => (
              <tr key={user.id}>
                <td style={{ textAlign: "center", padding: "10px" }}>
                  <Link to={"/managements-users/details/" + user.id}>
                  <p className="avatar avatar-xxl my-auto">
                    {(user.avatarUrl || user.avatarId) && (
                        <img
                            src={user.avatarUrl || user.avatarMedia?.url || ""}
                            style={{
                              display: "block",
                              margin: "0 auto",
                              maxWidth: "100%",
                              height: "100%",
                              objectFit: "cover",
                            }}
                        />
                    )}
                  </p>
                  </Link>
                </td>
                <td>{user.email}</td>
                <td>{`${user.firstName || ""} ${user.lastName || ""}`}</td>
                <td>{user.phone}</td>
                <td>
                  {user.active ? (
                      <span className="badge bg-success-transparent rounded-pill">
                        Active
                      </span>
                  ) : (
                      <span className="badge bg-danger-transparent rounded-pill">
                        Deactive
                      </span>
                  )}
                </td>
                <td>

                </td>
                <td>
                  <OverlayTrigger placement="top" overlay={<Tooltip>View</Tooltip>}>
                    <Button
                        variant="primary-light"
                        className="btn btn-info-light btn-sm ms-2"
                        onClick={() => navigate("/managements-users/details/" + user.id)}
                    >
                      <span className="ri-eye-line fs-14"></span>
                    </Button>
                  </OverlayTrigger>

                  <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
                    <Button
                        variant="primary-light"
                        className="btn btn-danger-light btn-sm ms-2"
                        onClick={() => handleDeleteClick(user)}
                    >
                      <span className="ri-delete-bin-7-line fs-14"></span>
                    </Button>
                  </OverlayTrigger>
                </td>
              </tr>
                ))
            }
            </tbody>
          </Table>

        </Card.Body>
      </Card>
    </Fragment>
  );
}

export default VendorUsers;