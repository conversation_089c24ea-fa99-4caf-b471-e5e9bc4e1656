import { FC, Fragment, useEffect, useState } from "react";
import { Alert, Card, Table } from "react-bootstrap";
import Card<PERSON>eaderWithBack from "../../../components/table-title/card-header-with-back";
import { useLazyGetVendorsListsQuery } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import VendorRow from "./vendor-row";
import { Tabs, Tab } from "react-bootstrap";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import VendorTableHeader from "./vendor-table-header";
import { EVendorsListLocation } from "../../../components/enums/vendors-list-location";

interface VendorsApprovalListProps { }

const VendorsApprovalList: FC<VendorsApprovalListProps> = () => {
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [totalPendingVendors, setTotalPendingVendors] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [vendors, setVendors] = useState<TVendor[]>([]);
  const [activeTab, setActiveTab] = useState<EApprovalStatus>(EApprovalStatus.PENDING);

  const [getVendorsList] = useLazyGetVendorsListsQuery();

  useEffect(() => {
    loadVendorsList({ page, filter: [`registrationStatus=${activeTab}`] });
  }, [page, activeTab]);

  const loadVendorsList = (query: TQueryAPI) => {
    setIsLoading(true);
    getVendorsList(query)
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setVendors(res.data || []);
        if (activeTab === EApprovalStatus.PENDING) {
          setTotalPendingVendors(res?.meta?.total || 0);
        }
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title="Vendors Registration"
            route=""
          />
        </Card.Header>
        <Card.Body>
          <Tabs
            activeKey={activeTab}
            onSelect={(k) => {
              setActiveTab(k as EApprovalStatus);
              setPage(1);
            }}
            className="mb-3"
          >
            <Tab eventKey="pending" title={`Pending (${totalPendingVendors})`}>
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}
              <Table className="table table-bordered text-nowrap border-bottom" responsive>
                <VendorTableHeader listLocation={EVendorsListLocation.PENDING} />
                <tbody>
                  {vendors.map((vendor) => (
                    <VendorRow
                      key={vendor.id}
                      vendor={vendor}
                      listLocation={EVendorsListLocation.PENDING}
                      setIsLoading={setIsLoading}
                      onFinishedAction={() => {
                        loadVendorsList({ page, filter: [`registrationStatus=${activeTab}`] });
                      }}
                    />
                  ))}
                </tbody>
              </Table>
              <PaginationBar
                page={page}
                setPage={setPage}
                lastPage={totalPages}
              />
            </Tab>
            <Tab eventKey="rejected" title="Rejected">
              {err?.messages?.map((message: string, index: number) => (
                <Alert key={index} variant="danger">
                  {message}
                </Alert>
              ))}
              <Table className="table table-bordered text-nowrap border-bottom" responsive>
                <VendorTableHeader listLocation={EVendorsListLocation.REJECTED} />
                <tbody>
                  {vendors.map((vendor) => (
                    <VendorRow
                      key={vendor.id}
                      vendor={vendor}
                      listLocation={EVendorsListLocation.REJECTED}
                      setIsLoading={setIsLoading}
                      onFinishedAction={() => {
                        loadVendorsList({ page, filter: [`registrationStatus=${activeTab}`] });
                      }}
                    />
                  ))}
                </tbody>
              </Table>
              <PaginationBar
                page={page}
                setPage={setPage}
                lastPage={totalPages}
              />
            </Tab>
          </Tabs>
        </Card.Body>
      </Card>
    </Fragment >
  );
};

export default VendorsApprovalList;