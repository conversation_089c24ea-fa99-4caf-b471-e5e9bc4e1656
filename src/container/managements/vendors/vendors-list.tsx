import { FC, Fragment, useEffect, useState } from "react";
import { Alert, Card, Table } from "react-bootstrap";
import Card<PERSON>eaderWithBack from "../../../components/table-title/card-header-with-back";
import { useLazyGetVendorsListsQuery } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import VendorRow from "./vendor-row";
import VendorTableHeader from "./vendor-table-header";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import { EVendorsListLocation } from "../../../components/enums/vendors-list-location";

interface VendorsListProps { }

const VendorsList: FC<VendorsListProps> = () => {
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [vendors, setVendors] = useState<TVendor[]>([]);

  const [getVendorsList] = useLazyGetVendorsListsQuery();

  useEffect(() => {
    loadVendorsList({ page });
  }, [page]);

  const loadVendorsList = (query: TQueryAPI) => {
    setIsLoading(true);
    getVendorsList({ ...query, filter: [`registrationStatus=${EApprovalStatus.APPROVED}`] })
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setVendors(res.data || []);
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack
            title='Vendors'
            route=''
          />
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}

          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <VendorTableHeader listLocation={EVendorsListLocation.MAIN} />
            <tbody>
              {
                vendors.map((vendor) => (
                  < VendorRow
                    key={vendor.id}
                    vendor={vendor}
                    listLocation={EVendorsListLocation.MAIN}
                    setIsLoading={setIsLoading}
                    onFinishedAction={() => {
                      loadVendorsList({ page });
                    }}
                  />
                ))
              }
            </tbody>
          </Table>

          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Body>
      </Card>
    </Fragment>
  );
}

export default VendorsList;