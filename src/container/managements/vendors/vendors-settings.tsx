import { <PERSON><PERSON>, Card, Col, Row, Table } from "react-bootstrap";
import <PERSON><PERSON><PERSON>er<PERSON>ithBack from "../../../components/table-title/card-header-with-back";
import { Fragment } from "react";
import { useEffect, useState } from "react";
import { useLazyGetVendorsListsQuery, useLazyGetVendorsSettingsQuery } from "../../../services/vendors";
import { LoadingOverlay } from "../../../components/loading/loading-overlay";
import { getAllErrorMessages } from "../../../utils/errors";
import { EApprovalStatus } from "../../../components/enums/approval-status-enum";
import PaginationBar from "../../../components/pagination-bar/pagination-bar";
import VendorTableHeader from "./vendor-table-header";
import { EVendorsListLocation } from "../../../components/enums/vendors-list-location";
import VendorRow from "./vendor-row";

export default function VendorsSettings() {
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(20);
  const [isLoading, setIsLoading] = useState(false);
  const [err, setErr] = useState<any>({});
  const [settings, setSettings] = useState<TVendorCommissionSetting>({ defaultCommissionRate: 0, defaultFixedCommissionAmount: 0 });
  const [vendors, setVendors] = useState<TVendor[]>([]);

  const [getVendorsSettings] = useLazyGetVendorsSettingsQuery();
  const [getVendorsList] = useLazyGetVendorsListsQuery();

  useEffect(() => {
    fetchSettings();
  }, []);

  useEffect(() => {
    loadVendorsList({ page });
  }, [page]);

  const fetchSettings = () => {
    setIsLoading(true);
    setErr({});
    getVendorsSettings()
      .unwrap()
      .then(setSettings)
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  const loadVendorsList = (query: TQueryAPI) => {
    setIsLoading(true);
    getVendorsList({ ...query, filter: [`registrationStatus=${EApprovalStatus.APPROVED}`] })
      .unwrap()
      .then((res) => {
        setTotalPages(res?.meta?.lastPage);
        setVendors(res.data || []);
        setErr({});
      })
      .catch((error) => {
        setErr(getAllErrorMessages(error));
      })
      .finally(() => {
        setIsLoading(false);
      });
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}

      <Card className="custom-card">
        <Card.Header>
          <CardHeaderWithBack title="Settings" route="" />
        </Card.Header>
        <Card.Body>
          {err?.messages?.map((message: string, index: number) => (
            <Alert key={index} variant="danger">
              {message}
            </Alert>
          ))}
        </Card.Body>
      </Card>

      <Row>
        <Col xl={4}>
          <Card className="" style={{ height: '150px' }}>
            <Card.Body className="">
              <Row>
                <div className="d-flex align-items-start justify-content-between mb-3">
                  <div className="flex-grow-1">
                    <p className="fs-14 mb-2">Default Commission Rate</p>
                    <h3 className="mb-3">
                      {settings ? `${settings.defaultCommissionRate * 100}%` : "--"}
                    </h3>
                  </div>
                </div>
              </Row>
            </Card.Body>
          </Card >
        </Col>
        <Col xl={4}>
          <Card className="" style={{ height: '150px' }}>
            <Card.Body className="">
              <Row>
                <div className="d-flex align-items-start justify-content-between mb-3">
                  <div className="flex-grow-1">
                    <p className="fs-14 mb-2">Default Fixed Commission Amount per Item</p>
                    <h3 className="mb-3">
                      {settings ? `$${settings.defaultFixedCommissionAmount}` : "--"}
                    </h3>
                  </div>
                </div>
              </Row>
            </Card.Body>
          </Card >
        </Col>
        <Col xl={4}>
          <Card className="" style={{ height: '150px' }}>
            <Card.Body className="">
              <Row>
                <div className="d-flex align-items-start justify-content-between mb-3">
                  <div className="flex-grow-1">
                    <p className="fs-14 mb-2">Default Final Commission</p>
                    <h3 className="mb-3">
                      {settings
                        ? `${settings.defaultCommissionRate * 100}% + $${settings.defaultFixedCommissionAmount}`
                        : "--"}
                    </h3>
                    <p className="fs-14 mb-2 text-muted">(Commission Rate + Fixed Commission Amount)</p>
                  </div>
                </div>
              </Row>
            </Card.Body>
          </Card >
        </Col>
      </Row>

      <Card className="custom-card">
        <Card.Header>
          <Card.Title>Details</Card.Title>
        </Card.Header>
        <Card.Body className="">
          <Table className="table table-bordered text-nowrap border-bottom" responsive>
            <VendorTableHeader listLocation={EVendorsListLocation.SETTINGS} />
            <tbody>
              {
                vendors.map((vendor) => (
                  < VendorRow
                    vendor={vendor}
                    commissionSettings={settings}
                    listLocation={EVendorsListLocation.SETTINGS}
                    setIsLoading={setIsLoading}
                    onFinishedAction={() => {
                      loadVendorsList({ page });
                    }}
                  />
                ))
              }
            </tbody>
          </Table>

          <PaginationBar
            page={page}
            setPage={setPage}
            lastPage={totalPages}
          />
        </Card.Body>
      </Card >
    </Fragment >
  );
}