import { Route, Routes } from "react-router-dom";
import Error404 from "../../components/errors/error404";
import ManagementWarehouse from "../../container/managements/warehouse/warehouse_list";
import ManagementSupplier from "../../container/managements/supplier/supplier_list";
import ManagementInventoryMovement from "../../container/managements/inventory-movements/inventory_movements_list";
import ManagementWarehouseDetails from "../../container/managements/warehouse/warehouse_details";
import ManagementSupplierDetails from "../../container/managements/supplier/supplier_details";

export const WarehouseRouter = () => {
  return (
    <Routes>
      <Route
        path={`warehouses`}
        element={<ManagementWarehouse />}
      />
      <Route
        path={`warehouses/:id`}
        element={<ManagementWarehouseDetails />}
      />

      <Route
        path={'suppliers'}
        element={<ManagementSupplier />}
      />
      <Route
        path={'suppliers/:id'}
        element={<ManagementSupplierDetails />}
      />

      <Route
        path={'inventory-movements'}
        element={<ManagementInventoryMovement />}
      />

      <Route path="*" element={<Error404 />} />
    </Routes>
  );
};
