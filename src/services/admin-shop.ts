import { apiService } from "./api";
import {
  TAdminShopProduct,
  TAdminShopSearchParams,
  TAdminShopCategoryProductsParams,
  TAdminShopCollectionProductsParams,
  TAdminShopSuggestionsParams,
  TAdminShopProductDetailsParams,
  TAdminShopCategoryResponse,
  TAdminShopCollectionResponse,
  TAdminShopCategoryProductsResponse,
  TAdminShopCollectionProductsResponse,
  TAdminShopSearchResponse,
  TAdminShopProductSuggestionsResponse,
} from "../types/admin/shop";

import { TAdminCart } from "../types/admin/cart";

const ENDPOINT = "v1/admin/shop";

const ALTERNATIVE_ENDPOINTS = {
  PRODUCTS: "v1/admin/products",
  CATEGORIES: "v1/admin/product-categories",
  VENDORS: "v1/admin/product-vendor",
  TAGS: "v1/admin/product-tag",
  TYPES: "v1/admin/product-type",
};

export const adminShopService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getCategories: build.query<TAdminShopCategoryResponse, void>({
      query: () => ({
        url: `${ENDPOINT}/categories`,
        method: "GET",
      }),
      keepUnusedDataFor: 300,
      providesTags: ["Categories"],
    }),
    getCategoryProducts: build.query<
      TAdminShopCategoryProductsResponse,
      { categoryId: string; params?: TAdminShopCategoryProductsParams }
    >({
      query: ({ categoryId, params }) => ({
        url: `${ENDPOINT}/categories/${categoryId}/products`,
        method: "GET",
        params: {
          page: params?.page || 1,
          perPage: Math.min(params?.perPage || 20, 100),
          sort: params?.sort || "title",
          order: params?.order || "asc",
          search: params?.search,
          minPrice: params?.minPrice,
          maxPrice: params?.maxPrice,
          inStock: params?.inStock,
          collectionId: params?.collectionId,
        },
      }),
      keepUnusedDataFor: 120,
      providesTags: (_result, _error, { categoryId }) => [
        { type: "Products", id: `category-${categoryId}` },
      ],
    }),
    getCollections: build.query<TAdminShopCollectionResponse, void>({
      query: () => ({
        url: `${ENDPOINT}/collections`,
        method: "GET",
      }),
      keepUnusedDataFor: 300,
      providesTags: ["Collections"],
    }),
    getCollectionProducts: build.query<
      TAdminShopCollectionProductsResponse,
      { collectionId: string; params?: TAdminShopCollectionProductsParams }
    >({
      query: ({ collectionId, params }) => ({
        url: `${ENDPOINT}/collections/${collectionId}/products`,
        method: "GET",
        params: {
          page: params?.page || 1,
          perPage: Math.min(params?.perPage || 20, 100),
          categoryId: params?.categoryId,
          orderBy: params?.orderBy || "BEST_SELLING",
          reverse: params?.reverse || false,
          search: params?.search,
          minPrice: params?.minPrice,
          maxPrice: params?.maxPrice,
          inStock: params?.inStock,
        },
      }),
      keepUnusedDataFor: 120,
      providesTags: (_result, _error, { collectionId }) => [
        { type: "Products", id: `collection-${collectionId}` },
      ],
    }),
    searchProducts: build.query<
      TAdminShopSearchResponse,
      TAdminShopSearchParams
    >({
      query: (params) => {
        if (!params.q || params.q.length < 2) {
          throw new Error("Search query must be at least 2 characters long");
        }

        return {
          url: `${ENDPOINT}/search`,
          method: "GET",
          params: {
            q: params.q,
            page: params.page || 1,
            perPage: Math.min(params.perPage || 20, 100),
            categoryId: params.categoryId,
            collectionId: params.collectionId,
            status: params.status,
            minPrice: params.minPrice,
            maxPrice: params.maxPrice,
            inStock: params.inStock,
            sortBy: params.sortBy || "relevance",
            sortOrder: params.sortOrder || "desc",
          },
        };
      },
      keepUnusedDataFor: 60,
      providesTags: (_result, _error, params) => [
        { type: "Products", id: `search-${params.q}` },
      ],
    }),
    getProductDetails: build.query<
      TAdminShopProduct,
      { productId: string; params?: TAdminShopProductDetailsParams }
    >({
      query: ({ productId, params }) => ({
        url: `${ENDPOINT}/products/${productId}`,
        method: "GET",
        params: {
          includeVariants: params?.includeVariants !== false,
          includeImages: params?.includeImages !== false,
          includeCollections: params?.includeCollections !== false,
          includeCategory: params?.includeCategory !== false,
          includeReviews: params?.includeReviews || false,
        },
      }),
    }),
    getProductSuggestions: build.query<
      TAdminShopProductSuggestionsResponse,
      TAdminShopSuggestionsParams
    >({
      query: (params) => ({
        url: `${ENDPOINT}/suggestions`,
        method: "GET",
        params: {
          cartId: params.cartId,
          perPage: Math.min(params.perPage || 10, 20),
          categoryId: params.categoryId,
          excludeProductIds: params.excludeProductIds,
          includeOutOfStock: params.includeOutOfStock || false,
        },
      }),
    }),
    quickAddProducts: build.mutation<
      { success: boolean; message: string; addedCount: number },
      {
        cartId: string;
        products: Array<{ variantId: string; quantity: number }>;
      }
    >({
      query: (payload) => ({
        url: `${ENDPOINT}/quick-add`,
        method: "POST",
        data: payload,
      }),
    }),
    getRecentProducts: build.query<TAdminShopProduct[], { perPage?: number }>({
      query: (params) => ({
        url: `${ENDPOINT}/recent-products`,
        method: "GET",
        params: {
          perPage: Math.min(params.perPage || 10, 50),
        },
      }),
    }),
    getPopularProducts: build.query<
      TAdminShopProduct[],
      {
        perPage?: number;
        categoryId?: string;
        period?: "week" | "month" | "year";
      }
    >({
      query: (params) => ({
        url: `${ENDPOINT}/popular-products`,
        method: "GET",
        params: {
          perPage: Math.min(params.perPage || 20, 100),
          categoryId: params.categoryId,
          period: params.period || "month",
        },
      }),
      keepUnusedDataFor: 600,
      providesTags: (_result, _error, params) => [
        { type: "Products", id: `popular-${params.period || "month"}` },
      ],
    }),
    getCarts: build.query<TReponsePaging<TAdminCart>, TQueryAPI>({
      query: (params) => ({
        url: "v1/admin/carts",
        method: "GET",
        params,
      }),
    }),
    getCartById: build.query<TAdminCart, string>({
      query: (cartId) => ({
        url: `v1/admin/carts/${cartId}`,
        method: "GET",
      }),
    }),
    getCartsByUserId: build.query<
      TReponsePaging<TAdminCart>,
      { userId: string; params?: TQueryAPI }
    >({
      query: ({ userId, params }) => ({
        url: `v1/admin/carts/user/${userId}`,
        method: "GET",
        params,
      }),
    }),
  }),
  overrideExisting: true,
});

export const adminShopFallbackService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getFallbackCategories: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.CATEGORIES,
        method: "GET",
        params,
      }),
    }),
    getFallbackVendors: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.VENDORS,
        method: "GET",
        params,
      }),
    }),
    getFallbackTags: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.TAGS,
        method: "GET",
        params,
      }),
    }),
    getFallbackTypes: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => ({
        url: ALTERNATIVE_ENDPOINTS.TYPES,
        method: "GET",
        params,
      }),
    }),
  }),
  overrideExisting: true,
});

export const {
  useGetCategoriesQuery,
  useGetCollectionsQuery,
  useGetPopularProductsQuery,
  useLazyGetCategoriesQuery,
  useLazyGetCategoryProductsQuery,
  useLazyGetCollectionsQuery,
  useLazyGetCollectionProductsQuery,
  useLazySearchProductsQuery,
  useLazyGetProductDetailsQuery,
  useLazyGetProductSuggestionsQuery,
  useLazyGetRecentProductsQuery,
  useLazyGetPopularProductsQuery,
  useQuickAddProductsMutation,
  useLazyGetCartsQuery,
  useLazyGetCartByIdQuery,
  useLazyGetCartsByUserIdQuery,
} = adminShopService;

export const {
  useLazyGetFallbackCategoriesQuery,
  useLazyGetFallbackVendorsQuery,
  useLazyGetFallbackTagsQuery,
  useLazyGetFallbackTypesQuery,
} = adminShopFallbackService;

export const buildSearchQuery = (
  params: Partial<TAdminShopSearchParams>
): TAdminShopSearchParams => {
  if (!params.q || params.q.length < 2) {
    throw new Error("Search query must be at least 2 characters long");
  }

  return {
    q: params.q,
    page: Math.max(1, params.page || 1),
    perPage: Math.min(Math.max(1, params.perPage || 20), 100),
    categoryId: params.categoryId,
    collectionId: params.collectionId,
    status: params.status,
    minPrice: params.minPrice,
    maxPrice: params.maxPrice,
    inStock: params.inStock,
    sortBy: params.sortBy || "relevance",
    sortOrder: params.sortOrder || "desc",
  };
};

export const buildCategoryProductsQuery = (
  categoryId: string,
  params: Partial<TAdminShopCategoryProductsParams> = {}
): { categoryId: string; params: TAdminShopCategoryProductsParams } => {
  return {
    categoryId,
    params: {
      page: Math.max(1, params.page || 1),
      perPage: Math.min(Math.max(1, params.perPage || 20), 100),
      sort: params.sort || "title",
      order: params.order || "asc",
      search: params.search,
      minPrice: params.minPrice,
      maxPrice: params.maxPrice,
      inStock: params.inStock,
      collectionId: params.collectionId,
    },
  };
};

export const buildCollectionProductsQuery = (
  collectionId: string,
  params: Partial<TAdminShopCollectionProductsParams> = {}
): { collectionId: string; params: TAdminShopCollectionProductsParams } => {
  return {
    collectionId,
    params: {
      page: Math.max(1, params.page || 1),
      perPage: Math.min(Math.max(1, params.perPage || 20), 100),
      categoryId: params.categoryId,
      orderBy: params.orderBy || "BEST_SELLING",
      reverse: params.reverse || false,
      search: params.search,
      minPrice: params.minPrice,
      maxPrice: params.maxPrice,
      inStock: params.inStock,
    },
  };
};
