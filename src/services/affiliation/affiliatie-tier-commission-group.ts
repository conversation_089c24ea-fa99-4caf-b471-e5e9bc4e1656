import { ECommissionGroupType } from "../../components/enums/commission_group_type";
import { apiService } from "../api";
const ENDPOINT = "v1/admin/affiliate-tiers";

export const affiliateTierService = apiService.injectEndpoints({
  endpoints: (builder) => ({

    getCommissionGroups: builder.query<TAffiliateTierCommissionGroup[], string>({
      query: (id) => ({
        url: `${ENDPOINT}/${id}/commission-groups`,
        method: "GET",
      }),
    }),

    addCommissionGroup: builder.mutation<
      TAffiliateTierCommissionGroup, {
        id: string;
        type: ECommissionGroupType;
      }
    >({
      query: ({ id, type }) => ({
        url: `${ENDPOINT}/${id}/commission-groups`,
        method: "POST",
        data: { type }
      }),
    }),

    updateCommissionGroup: builder.mutation<
      TAffiliateTierCommissionGroup, {
        groupId: string;
        type: ECommissionGroupType;
      }
    >({
      query: ({ groupId, type }) => ({
        url: `${ENDPOINT}/commission-groups/${groupId}`,
        method: "PUT",
        data: { type }
      }),
    }),

    deleteCommissionGroup: builder.mutation<TAffiliateTierCommissionGroup, string>({
      query: (groupId) => ({
        url: `${ENDPOINT}/commission-groups/${groupId}`,
        method: "DELETE"
      }),
    }),

    getProductsOfCommissionGroups: builder.query<
      TReponsePaging<TProduct>,
      {
        groupId: string,
        searchTerm: string,
        page: number,
        limit?: number
      }
    >({
      query: (params) => ({
        url: `${ENDPOINT}/commission-groups/${params.groupId}/products`,
        method: "GET",
        params
      }),
    }),

    getNonCommissionProducts: builder.query<
      TReponsePaging<TProduct>,
      {
        tierId: string;
        collectionId: string;
        searchTerm: string;
        page: number;
        limit?: number;
      }
    >({
      query: (params) => ({
        url: `${ENDPOINT}/${params.tierId}/commission-groups/non-commission-products`,
        method: "GET",
        params
      }),
    }),

    updateProductsOfCommissionGroup: builder.mutation<
      TAffiliateTierCommissionGroup, {
        groupId: string;
        action: string;
        productIds: string[];
        collectionId?: string;
      }
    >({
      query: ({ groupId, action, collectionId, productIds }) => ({
        url: `${ENDPOINT}/commission-groups/${groupId}/products`,
        method: "PUT",
        data: { action, collectionId, productIds }
      }),
    }),

    syncProductsWithCollection: builder.mutation<
      TAffiliateTierCommissionGroup, {
        groupId: string;
        collectionId: string;
      }
    >({
      query: ({ groupId, collectionId }) => ({
        url: `${ENDPOINT}/commission-groups/${groupId}/sync-with-collection`,
        method: "PUT",
        data: { collectionId }
      }),
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyGetCommissionGroupsQuery,
  useAddCommissionGroupMutation,
  useUpdateCommissionGroupMutation,
  useDeleteCommissionGroupMutation,

  useLazyGetProductsOfCommissionGroupsQuery,
  useLazyGetNonCommissionProductsQuery,
  useUpdateProductsOfCommissionGroupMutation,
  useSyncProductsWithCollectionMutation,
} = affiliateTierService;
