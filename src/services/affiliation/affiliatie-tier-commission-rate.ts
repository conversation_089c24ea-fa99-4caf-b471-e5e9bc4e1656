import { apiService } from "../api";
const ENDPOINT = "v1/admin/affiliate-tiers";

const affiliateTierCommissionRateService = apiService.injectEndpoints({
  endpoints: (builder) => ({

    getCommissionRates: builder.query<TAffiliateTierCommissionRate[], string>({
      query: (commissionGroupId) => ({
        url: `${ENDPOINT}/commission-groups/${commissionGroupId}/commission-rates`,
        method: "GET",
      }),
    }),

    addCommissionRate: builder.mutation<
      TAffiliateTierCommissionRate,
      {
        commissionGroupId: string,
        data: Partial<TAffiliateTierCommissionRate>
      }
    >({
      query: ({ commissionGroupId, data }) => ({
        url: `${ENDPOINT}/commission-groups/${commissionGroupId}/commission-rates`,
        method: "POST",
        data
      }),
    }),

    updateCommissionRate: builder.mutation<
      TAffiliateTierCommissionRate,
      {
        commissionRateId: string,
        data: Partial<TAffiliateTierCommissionRate>
      }
    >({
      query: ({ commissionRateId, data }) => ({
        url: `${ENDPOINT}/commission-groups/commission-rates/${commissionRateId}`,
        method: "PUT",
        data
      }),
    }),

    deleteCommissionRate: builder.mutation<TAffiliateTierCommissionGroup, string>({
      query: (commissionRateId) => ({
        url: `${ENDPOINT}/commission-groups/commission-rates/${commissionRateId}`,
        method: "DELETE"
      }),
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyGetCommissionRatesQuery,
  useAddCommissionRateMutation,
  useUpdateCommissionRateMutation,
  useDeleteCommissionRateMutation,
} = affiliateTierCommissionRateService;
