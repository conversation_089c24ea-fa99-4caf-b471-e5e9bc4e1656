import { apiService } from "./api";

const ENDPOINT = "v1/products";

export const appProductService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listAppProducts: build.query<TReponsePaging<TProduct>, TQueryAPI>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params: {
            ...params,
            pageSize: params.limit || 10,
          },
        };
      },
      transformResponse: (rawResult: TReponsePaging<TProduct>) => {
        const data = rawResult.data?.map((item: TProduct, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getAppProductById: build.query<TProduct, string>({
      query: (id: string) => {
        return {
          url: `${ENDPOINT}/${id}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TProduct) => {
        return rawResult;
      },
    }),
    listVendorProducts: build.query<TReponsePaging<TProduct>, TQueryAPI>({
      query: (params) => {
        return {
          url: `${ENDPOINT}/vendor-products`,
          method: "GET",
          params: {
            ...params,
            pageSize: params.limit || 10,
          },
        };
      },
      transformResponse: (rawResult: TReponsePaging<TProduct>) => {
        const data = rawResult.data?.map((item: TProduct, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
  }),
  overrideExisting: true,
});

const SHOP_ENDPOINT = "v1/app/shop";

export const shopService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listShopCategories: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => {
        return {
          url: `${SHOP_ENDPOINT}/categories`,
          method: "GET",
          params,
        };
      },
    }),
    getCategoryProducts: build.query<
      TReponsePaging<TProduct>,
      { id: string } & TQueryAPI
    >({
      query: ({ id, ...params }) => {
        return {
          url: `${SHOP_ENDPOINT}/categories/${id}/products`,
          method: "GET",
          params,
        };
      },
    }),
    listShopCollections: build.query<TReponsePaging<any>, TQueryAPI>({
      query: (params) => {
        return {
          url: `${SHOP_ENDPOINT}/collections`,
          method: "GET",
          params,
        };
      },
    }),
  }),
  overrideExisting: true,
});

const VENDOR_ENDPOINT = "v1/app/vendors";

export const appVendorService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getVendorProducts: build.query<
      TReponsePaging<TProduct>,
      { id: string } & TQueryAPI
    >({
      query: ({ id, ...params }) => {
        return {
          url: `${VENDOR_ENDPOINT}/${id}/products`,
          method: "GET",
          params: {
            ...params,
            pageSize: params.limit || 10,
          },
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListAppProductsQuery,
  useLazyGetAppProductByIdQuery,
  useLazyListVendorProductsQuery,
} = appProductService;

export const {
  useLazyListShopCategoriesQuery,
  useLazyGetCategoryProductsQuery,
  useLazyListShopCollectionsQuery,
} = shopService;

export const { useLazyGetVendorProductsQuery } = appVendorService;
