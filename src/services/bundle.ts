import { apiService } from "./api";
import { TBundle } from "../types/cart";

const ENDPOINT = "v1/products/bundles";

interface TBundleSelectOption {
  label: string;
  value: string;
  data: TBundle;
}

interface TBundleSelectResponse {
  data: TBundleSelectOption[];
  count?: number;
  page?: number;
  pageCount?: number;
  total?: number;
}

export const bundleService = apiService.injectEndpoints({
  endpoints: (build) => ({
    selectBundle: build.query<TBundleSelectResponse, TQueryAPI>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params: {
            ...params,
            pageSize: params.limit || 20,
          },
        };
      },
      transformResponse: (rawResult: TReponsePaging<TBundle>) => {
        const data = rawResult.data?.map((item: TBundle) => ({
          label: item.name,
          value: item.id,
          data: item,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
  }),
  overrideExisting: true,
});

export const { useLazySelectBundleQuery } = bundleService;
