import { apiService } from "./api";
import {
  TCart,
  TCartListParams,
  TAddProductToCartRequest,
  TAddBundleToCartRequest,
  TRemoveCartSectionRequest,
} from "../types/cart";

const ENDPOINT = "v1/admin/carts";

export const cartService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listCarts: build.query<TReponsePaging<TCart>, TCartListParams>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TCart>) => {
        const data = rawResult.data?.map((item: TCart, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
      providesTags: (result) =>
        result && result.data
          ? [
              ...result.data.map(({ id }) => ({ type: "Cart" as const, id })),
              { type: "Cart", id: "LIST" },
            ]
          : [{ type: "Cart", id: "LIST" }],
    }),
    getUserCart: build.query<TCart, string>({
      query: (userId: string) => {
        return {
          url: `${ENDPOINT}/user/${userId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TCart) => {
        return rawResult;
      },
      providesTags: (result, _error, userId) =>
        result
          ? [
              { type: "Cart", id: result.id },
              { type: "Cart", id: `user-${userId}` },
            ]
          : [],
    }),
    getCartDetails: build.query<TCart, string>({
      query: (cartId: string) => {
        return {
          url: `${ENDPOINT}/${cartId}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TCart) => {
        return rawResult;
      },
      providesTags: (result, _error, cartId) =>
        result ? [{ type: "Cart", id: cartId }] : [],
    }),
    deleteCart: build.mutation<{ success: boolean }, string>({
      query: (cartId: string) => {
        return {
          url: `${ENDPOINT}/${cartId}`,
          method: "DELETE",
        };
      },
      invalidatesTags: (_result, _error, cartId) => [
        { type: "Cart", id: cartId },
        { type: "Cart", id: "LIST" },
      ],
    }),
    addProductToCart: build.mutation<
      TCart,
      { cartId: string; data: TAddProductToCartRequest }
    >({
      query: ({ cartId, data }) => {
        return {
          url: `${ENDPOINT}/${cartId}/items`,
          method: "POST",
          data,
        };
      },
      invalidatesTags: (_result, _error, { cartId }) => [
        { type: "Cart", id: cartId },
        { type: "Cart", id: "LIST" },
        { type: "Cart", id: `user-${cartId}` },
      ],
    }),
    addBundleToCart: build.mutation<
      TCart,
      { cartId: string; data: TAddBundleToCartRequest }
    >({
      query: ({ cartId, data }) => {
        return {
          url: `${ENDPOINT}/${cartId}/bundles`,
          method: "POST",
          data,
        };
      },
      invalidatesTags: (_result, _error, { cartId }) => [
        { type: "Cart", id: cartId },
        { type: "Cart", id: "LIST" },
        { type: "Cart", id: `user-${cartId}` },
      ],
    }),
    removeCartSection: build.mutation<
      { message: string },
      { cartSectionId: string; data: TRemoveCartSectionRequest }
    >({
      query: ({ cartSectionId, data }) => {
        return {
          url: `${ENDPOINT}/sections/${cartSectionId}`,
          method: "DELETE",
          data,
        };
      },
      invalidatesTags: () => [{ type: "Cart", id: "LIST" }],
    }),
    updateCartSectionQuantity: build.mutation<
      any,
      { request: { cartSectionId: string; quantity: number; notes?: string } }
    >({
      query: ({ request }) => {
        return {
          url: `${ENDPOINT}/sections/${request.cartSectionId}/quantity`,
          method: "PUT",
          data: request,
        };
      },
      invalidatesTags: (result) => [
        ...(result
          ? [
              {
                type: "Cart" as const,
                id: (result as any).cartId || (result as any).id,
              },
            ]
          : []),
        { type: "Cart", id: "LIST" },
      ],
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListCartsQuery,
  useLazyGetUserCartQuery,
  useLazyGetCartDetailsQuery,
  useGetCartDetailsQuery,
  useDeleteCartMutation,
  useAddProductToCartMutation,
  useAddBundleToCartMutation,
  useRemoveCartSectionMutation,
  useUpdateCartSectionQuantityMutation,
} = cartService;
