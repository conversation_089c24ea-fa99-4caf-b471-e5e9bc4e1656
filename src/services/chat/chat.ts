import { apiService } from "../api";
const ENDPOINT = "v1/admin/chat";

export const streamPostService = apiService.injectEndpoints({
  endpoints: (build) => ({
    createChatToken: build.mutation<
      any,
      any
    >({
      query: (roomId: string) => {
        return {
          url: ENDPOINT + `/${roomId}/token`,
          method: "GET",
        };
      },
    }),
    createChatRoom: build.mutation<
      any,
      any
    >({
      query: (payload) => {
        return {
          url: ENDPOINT + `/`,
          method: "POST",
          data: payload
        };
      },
    }),
    createChatMessage: build.mutation<
      any,
      any
    >({
      query: ({ roomId, ...payload }) => {
        return {
          url: ENDPOINT + `/${roomId}/messages`,
          method: "POST",
          data: payload
        };
      },
    }),
    deleteChatMessage: build.mutation<
      any,
      any
    >({
      query: (messageId) => {
        return {
          url: ENDPOINT + `/messages/` + messageId,
          method: "DELETE",
        };
      },
    }),
    sendChatEvent: build.mutation({
      query: ({ roomId, ...payload }) => {
        return {
          url: ENDPOINT + `/${roomId}/event`,
          method: "POST",
          data: payload,
        };
      },
    }),
    listChatRooms: build.query({
      query: (payload) => {
        return {
          url: ENDPOINT + `/`,
          method: "GET",
          params: payload,
        };
      },
    }),
    listChatMessages: build.query({
      query: ({ roomId, ...payload }) => {
        return {
          url: ENDPOINT + `/${roomId}/messages`,
          method: "GET",
          params: payload,
        };
      },
    }),
    listChatChildren: build.query({
      query: ({ id, ...payload }) => {
        return {
          url: ENDPOINT + `/messages/${id}/children`,
          method: "GET",
          params: payload,
        };
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useCreateChatRoomMutation,
  useCreateChatTokenMutation,
  useCreateChatMessageMutation,
  useDeleteChatMessageMutation,
  useSendChatEventMutation,
  useLazyListChatRoomsQuery,
  useLazyListChatMessagesQuery,
  useLazyListChatChildrenQuery,
} = streamPostService;
