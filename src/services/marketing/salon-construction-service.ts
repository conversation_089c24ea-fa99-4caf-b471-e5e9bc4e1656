import { apiService } from "../api";
import { ESalonConstructionServiceStatus } from "../../utils/constant/salon-construction-service";

const ENDPOINT = "v1/admin/salon-construction-service";

export const salonConstructionServiceService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listSalonConstructionService: build.query<
      TReponsePaging<TSalonConstructionServiceSignup>,
      TSalonConstructionServiceListParams
    >({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TSalonConstructionServiceListResponse) => {
        // rawResult.data.data là mảng signup
        const data = rawResult.data?.data?.map(
          (item: TSalonConstructionServiceSignup, index: number) => ({
            ...item,
            _rowIndex: index + 1,
          })
        );
        return {
          ...rawResult.data,
          data: data || [],
          total: rawResult.data?.meta?.total || 0,
        };
      },
    }),
    getSalonConstructionServiceById: build.query<
      TSalonConstructionServiceSignup,
      string
    >({
      query: (id: string) => {
        return {
          url: `${ENDPOINT}/${id}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TSalonConstructionServiceResponse) => {
        return rawResult.data;
      },
    }),
    updateSalonConstructionServiceStatus: build.mutation<
      TSalonConstructionServiceSignup,
      { id: string } & TSalonConstructionServiceUpdateRequest
    >({
      query: ({ id, ...payload }) => {
        return {
          url: `${ENDPOINT}/${id}`,
          method: "PUT",
          data: payload,
        };
      },
      transformResponse: (rawResult: TSalonConstructionServiceResponse) => {
        return rawResult.data;
      },
    }),
    getSalonConstructionServiceStats: build.query<
      {
        total: number;
        pending: number;
        approved: number;
        rejected: number;
      },
      void
    >({
      query: () => {
        return {
          url: `${ENDPOINT}/stats`,
          method: "GET",
        };
      },
      transformResponse: (
        rawResult: TSalonConstructionServiceStatsResponse
      ) => {
        return rawResult.data;
      },
    }),
    deleteSalonConstructionServiceById: build.mutation<
      { success: boolean; message: string },
      string
    >({
      query: (id: string) => {
        return {
          url: `${ENDPOINT}/${id}`,
          method: "DELETE",
        };
      },
      transformResponse: (rawResult: { success: boolean; message: string }) => {
        return rawResult;
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListSalonConstructionServiceQuery,
  useLazyGetSalonConstructionServiceByIdQuery,
  useUpdateSalonConstructionServiceStatusMutation,
  useLazyGetSalonConstructionServiceStatsQuery,
  useDeleteSalonConstructionServiceByIdMutation,
} = salonConstructionServiceService;

export const salonConstructionServiceHelpers = {
  approveSignup: (id: string) => ({
    id,
    status: ESalonConstructionServiceStatus.APPROVED,
  }),
  rejectSignup: (id: string, rejectionReason?: string) => ({
    id,
    status: ESalonConstructionServiceStatus.REJECTED,
    ...(rejectionReason && { rejectionReason }),
  }),
  downloadPDF: (id: string) => {
    const url = `${
      import.meta.env.VITE_APP_API_URL
    }/v1/salon-construction-service/${id}/pdf`;
    window.open(url, "_blank");
  },
  getPDFUrl: (id: string) => {
    return `${
      import.meta.env.VITE_APP_API_URL
    }/v1/salon-construction-service/${id}/pdf`;
  },
};
