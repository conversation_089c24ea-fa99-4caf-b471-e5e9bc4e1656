import { apiService } from "../api";
const ENDPOINT = "v1/admin/report/inventory-import";

export const inventoryImportReportService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getInventoryImportReport: build.query<
      TReponsePaging<TInventorySurplusReport>,
      {
        page: number,
        limit?: number,
        startDate?: string,
        endDate?: string,
      }>({
        query: (params) => {
          return {
            url: ENDPOINT,
            method: "GET",
            params,
          };
        },
      }),
    exportInventoryImportReport: build.query({
      query: (params) => {
        return {
          url: ENDPOINT + "/export",
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: {
        filename: string,
        url: string,
      }) => {
        return rawResult;
      },
    }),
  }),
});

export const {
  useLazyGetInventoryImportReportQuery,
  useLazyExportInventoryImportReportQuery,
} = inventoryImportReportService;
