import { apiService } from "../api";
const ENDPOINT = "v1/admin/report/inventory-surplus";

export const inventorySurplusReportService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getInventorySurplusReport: build.query<
      TReponsePaging<TInventorySurplusReport>,
      {
        page: number,
        limit?: number,
        cutoffDate: string,
        sku: string,
      }>({
        query: (params) => {
          return {
            url: ENDPOINT,
            method: "GET",
            params,
          };
        },
      }),
    exportInventorySurplusReport: build.query({
      query: (params) => {
        return {
          url: ENDPOINT + "/export",
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: {
        filename: string,
        url: string,
      }) => {
        return rawResult;
      },
    }),
  }),
});

export const {
  useLazyGetInventorySurplusReportQuery,
  useLazyExportInventorySurplusReportQuery,
} = inventorySurplusReportService;
