import { apiService } from "./api";
const ENDPOINT = "v1/admin/reward-points";

export const rewardPointService = apiService.injectEndpoints({
    endpoints: (build) => ({
        listRewardActivities: build.query<TReponsePaging<TRewardActivity>, any>({
            query: (payload) => {
                return {
                    url: ENDPOINT + "/activities",
                    method: "GET",
                    params: payload
                };
            },
        }),
    }),
    overrideExisting: true,
});

export const {
    useLazyListRewardActivitiesQuery,
} = rewardPointService;
