import { apiService } from "./api";
const ENDPOINT = "v1/admin/reward-programs";

export const rewardProgramService = apiService.injectEndpoints({
    endpoints: (build) => ({
        listRewardPrograms: build.query<TReponsePaging<TRewardProgram>, any>({
            query: (payload) => {
                return {
                    url: ENDPOINT,
                    method: "GET",
                    params: payload
                };
            },
        }),
        getRewardProgramById: build.query<TRewardProgram, any>({
            query: ({ id, ...payload }) => {
                return {
                    url: ENDPOINT + `/${id}`,
                    method: "GET",
                    data: payload
                };
            },
        }),
        createRewardProgram: build.mutation<TRewardProgram, any>({
            query: ({ id, ...payload }) => {
                return {
                    url: ENDPOINT,
                    method: "POST",
                    data: payload
                };
            },
        }),
        updateRewardProgram: build.mutation<TRewardProgram, any>({
            query: ({ id, ...payload }) => {
                return {
                    url: ENDPOINT + `/${id}`,
                    method: "PUT",
                    data: payload
                };
            },
        }),
        deleteRewardProgram: build.mutation<TRewardProgram, any>({
            query: (id:string) => {
                return {
                    url: ENDPOINT + `/${id}`,
                    method: "DELETE",
                };
            },
        }),
    }),
    overrideExisting: true,
});

export const {
    useLazyListRewardProgramsQuery,
    useLazyGetRewardProgramByIdQuery,
    useCreateRewardProgramMutation,
    useUpdateRewardProgramMutation,
    useDeleteRewardProgramMutation,
} = rewardProgramService;
