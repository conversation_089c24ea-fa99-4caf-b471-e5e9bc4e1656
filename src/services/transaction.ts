import { apiService } from "./api";
const ENDPOINT = "v1/admin/transactions";

export const transactionService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getTransactionStats: build.query<TTransactionStats, void>({
      query: () => {
        return {
          url: ENDPOINT + "/stats",
          method: "GET",
        };
      },
      transformResponse: (rawResult: TTransactionStats) => {
        return rawResult;
      },
    }),
    listTransactions: build.query<
      TReponsePaging<TTransaction>,
      TTransactionQuery
    >({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TTransaction>) => {
        const data = rawResult.data?.map((item: TTransaction, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getTransactionById: build.query<TTransaction, string>({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TTransaction) => {
        return rawResult;
      },
    }),
    getTransactionsByOrder: build.query<TTransaction[], string>({
      query: (orderId: string) => {
        return {
          url: ENDPOINT + "/order/" + orderId,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TTransaction[]) => {
        return rawResult || [];
      },
    }),
    refundTransaction: build.mutation<
      TTransaction,
      { id: string; data: TTransactionRefundRequest }
    >({
      query: ({ id, data }) => {
        return {
          url: ENDPOINT + "/" + id + "/refund",
          method: "POST",
          data,
        };
      },
      transformResponse: (rawResult: TTransaction) => {
        return rawResult;
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyGetTransactionStatsQuery,
  useLazyListTransactionsQuery,
  useLazyGetTransactionByIdQuery,
  useLazyGetTransactionsByOrderQuery,
  useRefundTransactionMutation,
} = transactionService;
