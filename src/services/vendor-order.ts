import { apiService } from "./api";
const ENDPOINT = "v1/admin/vendors/orders";

export const vendorOrderService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listVendorOrders: build.query<TReponsePaging<TVendorOrder>, TQueryAPI>({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (rawResult: TReponsePaging<TVendorOrder>) => {
        const data = rawResult.data?.map((item: TVendorOrder, index) => ({
          ...item,
          _rowIndex: index + 1,
        }));
        return { ...rawResult, data: data || [] };
      },
    }),
    getVendorOrderById: build.query<TVendorOrder, string>({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "GET",
        };
      },
      transformResponse: (rawResult: TVendorOrder) => {
        return rawResult;
      },
    }),
    createVendorOrder: build.mutation<
      TVendorOrder,
      Partial<TVendorOrder>
    >({
      query: (payload) => {
        return {
          url: ENDPOINT,
          method: "POST",
          data: payload,
        };
      },
      transformResponse: (rawResult: TVendorOrder) => {
        return rawResult;
      },
    }),
    updateVendorOrder: build.mutation<
      TVendorOrder,
      Partial<TVendorOrder>
    >({
      query: ({ id, ...payload }) => {
        return {
          url: ENDPOINT + `/${id}`,
          method: "PUT",
          data: payload,
        };
      },
      transformResponse: (rawResult: TVendorOrder) => {
        return rawResult;
      },
    }),
    deleteVendorOrderById: build.query<
      TVendorOrder,
      string
    >({
      query: (id: string) => {
        return {
          url: ENDPOINT + "/" + id,
          method: "DELETE",
        };
      },
      transformResponse: (rawResult: TVendorOrder) => {
        return rawResult;
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListVendorOrdersQuery,
  useLazyGetVendorOrderByIdQuery,
  useCreateVendorOrderMutation,
  useUpdateVendorOrderMutation,
  useLazyDeleteVendorOrderByIdQuery,
} = vendorOrderService;
