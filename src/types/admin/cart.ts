import {
  T<PERSON><PERSON><PERSON><PERSON><PERSON>,
  TA<PERSON><PERSON><PERSON><PERSON>,
  TAdminProduct,
  TAdminProductVariant,
  TAdminProductImage,
} from "./common";

export interface TAdminCart {
  id: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  user?: TAdminUser;
  sections?: TAdminCartSection[];
}

export interface TAdminCartSection {
  id: string;
  cartId: string;
  type: "product" | "bundle";
  title: string;
  quantity: number;
  total: number;
  bundleId: string | null;
  cartItems: TAdminCartItem[];
}

export interface TAdminCartItem {
  id: string;
  cartSectionId: string;
  productId: string;
  variantId: string;
  quantity: number;
  price: number;
  product: TAdminCartProduct;
  variant: TAdminCartProductVariant;
}

export interface TAdminCartProduct extends TAdminProduct {}

export interface TAdminCartProductVariant extends TAdminProductVariant {}

export interface TAdminCartProductImage extends TAdminProductImage {}

export interface TAdminCartQueryParams extends TQueryAPI {
  userId?: string;
  status?: "active" | "abandoned" | "completed";
  dateFrom?: string;
  dateTo?: string;
}

export interface TAdminCartQuickAddRequest {
  products: TAdminCartQuickAddProduct[];
}

export interface TAdminCartQuickAddProduct {
  variantId: string;
  quantity: number;
  affiliateId?: string;
  notes?: string;
}

export interface TAdminCartBulkAddRequest {
  categoryId?: string;
  collectionId?: string;
  searchQuery?: string;
  limit?: number;
  affiliateId?: string;
  notes?: string;
}

export interface TAdminCartMoveItemRequest {
  cartId: string;
  cartItemId: string;
  targetSectionId: string;
}

export interface TAdminCartUpdateQuantityRequest {
  quantity: number;
}

export interface TAdminCartAnalytics {
  totalItems: number;
  totalValue: number;
  sectionCount: number;
  categoryDistribution: Record<string, number>;
  priceRange: {
    min: number;
    max: number;
    average: number;
  };
  sections: TAdminCartSectionSummary[];
}

export interface TAdminCartSectionSummary {
  id: string;
  type: string;
  title: string;
  itemCount: number;
  totalValue: number;
}

export interface TAdminCartSummary {
  cart: TAdminCart;
  user: TAdminUser;
  summary: {
    totalItems: number;
    totalValue: number;
    sectionCount: number;
  };
  sections: TAdminCartSectionSummary[];
}

export interface TAdminCartValidation {
  isValid: boolean;
  issues: string[];
  warnings: string[];
  details: {
    invalidItems: string[];
    outOfStockItems: string[];
    priceChanges: string[];
  };
}
