export interface TQueryAPI {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  [key: string]: any;
}

export interface TReponsePaging<T> {
  data: T[];
  meta: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
    firstPage: number;
    firstPageUrl: string;
    lastPageUrl: string;
    nextPageUrl: string | null;
    previousPageUrl: string | null;
  };
}

export interface TAdminUser {
  id: string;
  email: string;
  firstName: string | null;
  lastName: string | null;
  phone: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface TAdminProductImage {
  id: string;
  src: string;
  altText: string | null;
  position: number;
  width?: number | null;
  height?: number | null;
}

export interface TAdminProductVariant {
  id: string;
  title: string;
  price: string;
  sku: string | null;
  availableForSale: number;
  inventoryQuantity: number;
}

export interface TAdminProduct {
  id: string;
  title: string;
  handle: string;
  status: "active" | "draft";
  price: string;
  image?: TAdminProductImage;
}

export interface TAdminPaginationMeta {
  total: number;
  perPage: number;
  currentPage: number;
  lastPage: number;
  firstPage: number;
  firstPageUrl: string;
  lastPageUrl: string;
  nextPageUrl: string | null;
  previousPageUrl: string | null;
}
