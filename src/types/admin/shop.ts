import { TAdminProductImage, TAdminPaginationMeta } from "./common";

export interface TAdminShopCategory {
  id: string;
  shopifyId: string;
  name: string;
  fullName: string;
  level: number;
  ancestorIds: string[];
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface TAdminShopCollection {
  id: string;
  shopifyCollectionId: string;
  title: string;
  description: string | null;
  status: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  categories: TAdminShopCategory[];
}

export interface TAdminShopProduct {
  id: string;
  shopifyProductId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  handle: string;
  status: "active" | "draft";
  deletedAt: string | null;
  price: string;
  vendorId: string;
  productTypeId: string | null;
  categoryId: string | null;
  pickupOnly: number;
  fulfilProductId: string | null;
  classification: string | null;
  isGift: number;
  pendingChanges: string | null;
  pendingApproval: number | null;
  variants: TAdminShopProductVariant[];
  productType: TAdminShopProductType | null;
  image: TAdminShopProductImage | null;
  vendor: TAdminShopVendor;
  tags: TAdminShopProductTag[];
  reviewSummary: TAdminShopReviewSummary;
  onlineStoreUrl: string;
}

export interface TAdminShopProductVariant {
  id: string;
  productId: string;
  title: string;
  price: string;
  compareAtPrice: string | null;
  inventoryQuantity: number;
  inventoryPolicy: string;
  sku: string | null;
  availableForSale: number;
  numberSold: number;
  warehouseInventories: any[];
}

export interface TAdminShopProductType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface TAdminShopProductImage extends TAdminProductImage {
  productId: string;
  shopifyImageId: string | null;
  variantId: string | null;
}

export interface TAdminShopVendor {
  id: string;
  companyName: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  brandName: string | null;
  website: string | null;
  contactName: string;
  phone: string | null;
  email: string;
  address1: string | null;
  address2: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  zipCode: string | null;
  ein: string | null;
  registrationStatus: string;
  rejectionReason: string | null;
  commissionRate: number;
  fixedCommissionAmount: number;
  warehouseId: string | null;
  thumbnailId: string | null;
  description: string | null;
  returnWarrantyPolicy: string | null;
  name: string;
}

export interface TAdminShopProductTag {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

export interface TAdminShopReviewSummary {
  latestReviews: TAdminShopReview[];
  totalReviews: number;
  averageRating: number;
  details: {
    "1": number;
    "2": number;
    "3": number;
    "4": number;
    "5": number;
  };
}

export interface TAdminShopReview {
  id: string;
  stampedId: string | null;
  productId: string;
  shopifyProductId: string | null;
  userId: string;
  author: string;
  email: string;
  title: string;
  body: string;
  rating: number;
  reply: string | null;
  repliedAt: string | null;
  status: number;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface TAdminShopCategoryImage {
  id: string;
  fileKey: string;
  url: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  type: string;
  resourceId: string | null;
  sourceFrom: string | null;
  start: string | null;
  end: string | null;
}

export interface TAdminShopCollectionProduct {
  id: string;
  shopifyProductId: string;
  title: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  handle: string;
  status: "active" | "draft";
  deletedAt: string | null;
  price: string;
  vendorId: string;
  productTypeId: string | null;
  categoryId: string | null;
  pickupOnly: number;
  fulfilProductId: string | null;
  classification: string | null;
  isGift: number;
  pendingChanges: string | null;
  pendingApproval: number | null;
  variant: TAdminShopCollectionProductVariant;
  productType: TAdminShopProductType | null;
  image: TAdminShopProductImage | null;
  reviewSummary: TAdminShopReviewSummary;
  onlineStoreUrl: string;
  pivotOrderBy: number;
}

export interface TAdminShopCollectionProductVariant {
  id: string;
  productId: string;
  shopifyVariantId: string;
  title: string;
  price: string;
  compareAtPrice: string | null;
  sku: string;
  legacyResourceId: string;
  position: number;
  inventoryQuantity: number;
  maxQuantity: number | null;
  inventoryPolicy: string;
  inventoryManagement: string;
  weight: number | null;
  weightUnit: string | null;
  barcode: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  imageId: string;
  supplierId: string | null;
  fulfilVariantId: string | null;
  availableForSale: number;
  numberSold: number;
  warehouseInventories: any[];
}

export interface TAdminShopSearchParams {
  q: string;
  page?: number;
  perPage?: number;
  categoryId?: string;
  collectionId?: string;
  status?: "active" | "draft";
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  sortBy?:
    | "title"
    | "price"
    | "createdAt"
    | "updatedAt"
    | "popularity"
    | "relevance";
  sortOrder?: "asc" | "desc";
}

export interface TAdminShopCategoryProductsParams {
  page?: number;
  perPage?: number;
  sort?: "title" | "price" | "createdAt" | "updatedAt" | "popularity";
  order?: "asc" | "desc";
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  collectionId?: string;
}

export interface TAdminShopCollectionProductsParams {
  page?: number;
  perPage?: number;
  categoryId?: string;
  orderBy?: "BEST_SELLING" | "TITLE" | "PRICE" | "CREATED" | "MANUAL";
  reverse?: boolean;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
}

export interface TAdminShopSuggestionsParams {
  cartId?: string;
  perPage?: number;
  categoryId?: string;
  excludeProductIds?: string[];
  includeOutOfStock?: boolean;
}

export interface TAdminShopProductDetailsParams {
  includeVariants?: boolean;
  includeImages?: boolean;
  includeCollections?: boolean;
  includeCategory?: boolean;
  includeReviews?: boolean;
}

export interface TAdminShopCategoryResponse {
  meta: TAdminPaginationMeta;
  data: TAdminShopCategory[];
}

export interface TAdminShopCollectionResponse {
  meta: TAdminPaginationMeta;
  data: TAdminShopCollection[];
}

export interface TAdminShopCategoryProductsResponse {
  meta: TAdminPaginationMeta;
  data: TAdminShopProduct[];
}

export interface TAdminShopCollectionProductsResponse {
  id: string;
  shopifyCollectionId: string;
  title: string;
  description: string;
  handle: string;
  imageUrl: string;
  imageAltText: string | null;
  status: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  imageId: string | null;
  categories: TAdminShopCategory[];
  image: any | null;
  products: TAdminShopCollectionProduct[];
  meta: TAdminPaginationMeta;
}

export interface TAdminShopSearchResponse {
  success: boolean;
  message: string;
  error: string;
}

export interface TAdminShopProductSuggestionsResponse {
  success: boolean;
  message: string;
  error: string;
}

export interface TFallbackProductCategory {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number;
}

export interface TFallbackProductVendor {
  id: string;
  companyName: string;
  brandName?: string;
  website?: string;
  contactName: string;
  phone?: string;
  email: string;
  address1?: string;
  address2?: string;
  city?: string;
  state?: string;
  country?: string;
  zipCode?: string;
  registrationStatus: string;
  commissionRate: number;
  fixedCommissionAmount: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number;
}

export interface TFallbackProductTag {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number;
}

export interface TFallbackProductType {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  _rowIndex?: number;
}

export interface TFallbackPaginationResponse<T> {
  data: T[];
  meta?: TAdminPaginationMeta;
}

export interface TFallbackQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}
