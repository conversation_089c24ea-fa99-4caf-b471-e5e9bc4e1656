// TODO: Extends from TChatMessage in the future
type TChatbotMessage = {
    id: string
    roomId: string
    content: string
    attributes?: any
    avatarUrl?: string
    role: TChatbotRole
    sentAt?: string
    annotatedChatMessageId?: string | null
    _localAnnotation?: string
    userName?: string
    pending?: boolean
    adminId?: string | null
    userId?: string | null
    assistantId?: string | null
    ivsChatMessageId?: string | null

    createdAt?: string | null
    updatedAt?: string | null
    deletedAt?: string | null
}
