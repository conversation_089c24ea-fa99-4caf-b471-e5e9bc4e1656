export type TChatRoom = {
    id: string
    title?: string
    createdAt: string
    updatedAt: string
}

export type TChatResource = {
    id: string
    type: string
    url: string
}

export type TChatTokenResponse = {
    token: string
    expiresAt: string
}

export type TChatSuggestion = {
    id: string
    text: string
}

export type TChatMessage = {
    id: string
    role?: 'user' | 'bot' | 'system'
    content: string
    sentAt: string

    attributes?: {
        parentMessageIvsId?: string
        productIds?: string[]
        postId?: string
        collectionIds?: string[]
        [key: string]: any
    }
}
