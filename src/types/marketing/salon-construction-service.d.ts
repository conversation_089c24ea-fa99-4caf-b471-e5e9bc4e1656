type TSalonConstructionServicePdfFile = {
  id: string;
  url: string;
  fileKey: string;
};

type TSalonConstructionServiceSignup = {
  id: string;
  fullName: string;
  businessName: string;
  salonAddress: string;
  phoneNumber: string;
  emailAddress: string;
  serviceInterest: EServiceInterest[];
  preferredStartDate?: string;
  budgetRange: EBudgetRange;
  additionalNotes?: string;
  consentConfirmed: boolean;
  signature?: string;
  signatureDate?: string;
  status: ESalonConstructionServiceStatus;
  pdfFile?: TSalonConstructionServicePdfFile;
  pdfFileId?: string;
  createdAt: string;
  updatedAt: string;
};

type TSalonConstructionServiceCreateRequest = {
  fullName: string;
  businessName: string;
  salonAddress: string;
  phoneNumber: string;
  emailAddress: string;
  serviceInterest: EServiceInterest[];
  preferredStartDate?: string;
  budgetRange: EBudgetRange;
  additionalNotes?: string;
  consentConfirmed: boolean;
  signature?: string;
  signatureDate?: string;
};

type TSalonConstructionServiceUpdateRequest = {
  status: ESalonConstructionServiceStatus;
  rejectionReason?: string;
};

type TSalonConstructionServiceListParams = {
  page?: number;
  limit?: number;
  status?: ESalonConstructionServiceStatus;
  search?: string;
  startDate?: string;
  endDate?: string;
  budgetRange?: EBudgetRange;
};

type TSalonConstructionServiceListResponse = {
  success: boolean;
  data: {
    data: TSalonConstructionServiceSignup[];
    meta: {
      total: number;
      perPage: number;
      currentPage: number;
      lastPage: number;
      firstPage: number;
      firstPageUrl: string;
      lastPageUrl: string;
      nextPageUrl: string | null;
      previousPageUrl: string | null;
    };
  };
};

type TSalonConstructionServiceResponse = {
  success: boolean;
  message?: string;
  data: TSalonConstructionServiceSignup;
};

type TSalonConstructionServiceStatsResponse = {
  success: boolean;
  data: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  };
};

type TSalonConstructionServiceErrorResponse = {
  success: false;
  message: string;
  errors?: Array<{
    message: string;
    rule: string;
    field: string;
  }>;
};

// Filter options for the management interface
type TSalonConstructionServiceFilters = {
  status: ESalonConstructionServiceStatus | "all";
  search: string;
  startDate: string;
  endDate: string;
  budgetRange: EBudgetRange | "all";
  page: number;
  limit: number;
};

// Table column configuration
type TSalonConstructionServiceTableColumn = {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  render?: (
    value: any,
    record: TSalonConstructionServiceSignup
  ) => React.ReactNode;
};
