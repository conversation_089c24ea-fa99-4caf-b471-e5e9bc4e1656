type TRewardProgram = TAppModel & {
  smileId: number

  rewardName: string
  rewardDescription: string | null
  rewardImageUrl: string

  status: number
  rewardType: number
  programType: number

  exchangeType: string
  exchangeDescription: string

  pointsPrice: number
  minimumPointsPrice: number
  rewardValue: number

  variablePointsStep: number | null
  variablePointsStepRewardValue: number | null
  variablePointsMin: number | null
  variablePointsMax: number | null
};
