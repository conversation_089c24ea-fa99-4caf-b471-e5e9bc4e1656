type TVendorOrder = TAppModel & {
    status: string,
    note: string

    orderDetailsCount: number,
    subtotalPrice: number,
    totalDiscounts: number,
    totalShipping: number,
    totalTax: number,
    totalPrice: number,
    currentTotalPrice: number,

    cancelledAt: Date,
    closedAt: Date,

    orderId: string,
    order: TOrder,

    vendorId: string,
    vendor: TVendor,

    orderDetails: TOrderDetail[]

    fulfillmentId: string
    fulfillment: TOrderFulfillment
}