export enum ERewardProgramExchangeType {
  FIXED = 'fixed',
  VARIABLE = 'variable',
}

export enum ERewardProgramStatus {
  DISABLED = 0,
  ACTIVE = 1,
}

export enum EProgramRewardType {
  EARN = 1,
  REDEEM = 2,
}

export enum ERewardProgramType {
  SIGNUP_REFERRAL = 1,
}

export enum ERewardProgramTypeEarn {
  SIGNUP_REFERRAL = ERewardProgramType.SIGNUP_REFERRAL,
}

export enum ERewardProgramTypeRedeem {
}

export const ERewardProgramStatusName = {
  [ERewardProgramStatus.DISABLED]: "Disabled",
  [ERewardProgramStatus.ACTIVE]: "Active",
}

export const EProgramRewardTypeName = {
  [EProgramRewardType.EARN]: "Earn",
  [EProgramRewardType.REDEEM]: "Redeem",
}

export const ERewardProgramTypeName = {
  [ERewardProgramType.SIGNUP_REFERRAL]: "Signup Referral",
}
