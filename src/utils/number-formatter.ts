const formatNumber = (value: number, fractionDigits?: number) => {
  const trimedDecimalStr = value.toFixed(5);
  const trimedDecimalNumber = Number(trimedDecimalStr);

  const numberFormater = fractionDigits ?
    new Intl.NumberFormat('en-US', {
      minimumFractionDigits: fractionDigits,
      maximumFractionDigits: fractionDigits
    }) :
    new Intl.NumberFormat('en-US');
  return numberFormater.format(trimedDecimalNumber);
}

export default formatNumber;