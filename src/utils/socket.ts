import { io } from 'socket.io-client';
export class Ws {
    // private baseUrl = `${import.meta.env.VITE_APP_API_URL}:${import.meta.env.VITE_SOCKET_PORT}/'`
    private baseUrl = import.meta.env.VITE_SOCKET_HOST || import.meta.env.VITE_APP_API_URL
    private baseProtocol = import.meta.env.VITE_SOCKET_PROTOCOL || 'ws'

    socket(name?: string) {
        let namespace = ''
        if (name) { namespace = name }

        // if protocol doesn't exist, add one
        if (this.baseUrl.split('://').length == 1) {
            this.baseUrl = this.baseProtocol + '://' + this.baseUrl
        }

        const socketUrl = new URL(this.baseUrl)

        socketUrl.protocol = this.baseProtocol
        socketUrl.port = import.meta.env.VITE_SOCKET_PORT
        socketUrl.pathname = namespace

        const socket = io(
            socketUrl.href,
            {
                autoConnect: false,
                auth: {
                    token: import.meta.env.VITE_SOCKET_PASSWORD
                }
            })

        return socket
    }
}